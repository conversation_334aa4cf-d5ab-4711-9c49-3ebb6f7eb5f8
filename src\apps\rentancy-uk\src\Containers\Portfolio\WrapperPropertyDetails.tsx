import { RoleUtils } from "@rentancy/common";
import { makeStyles } from "@mui/styles";
import { useDeviceScreen } from "@rentancy/common";
import { useParams } from "react-router-dom";
import { useUser, PropertyDetailsProvider } from "@rentancy/common";

import { ContractDetailsMobile } from "./Contracts/contract/ContractDetailsMobile";
import { DesktopPropertyDetails } from "./Desktop/DesktopPropertyDetails";
import { MobilePropertyDetails } from "./Mobile/MobilePropertyDetails";

export const WrapperPropertyDetails = () => {
  const { isDesktop } = useDeviceScreen();
  const { user } = useUser();
  const isTenant = !RoleUtils.isAgent(user);
  const classes = useStyles({ isDesktop });
  const params = useParams<{ type: string; tenancyId?: string }>();

  if (params.tenancyId && !isDesktop && !isTenant) {
    return <ContractDetailsMobile />;
  }

  return (
    <PropertyDetailsProvider>
      <div className={classes.root}>
        {isDesktop ? <DesktopPropertyDetails /> : <MobilePropertyDetails />}
      </div>
    </PropertyDetailsProvider>
  );
};

const useStyles = makeStyles(theme => ({
  root: {
    background: theme.palette.common.white,
    width: "100%",
    minHeight: "100%",
    overflow: "unset",
    [theme.breakpoints.up("lg")]: {
      background: theme.palette.specialColors.grey[100],
      overflowY: "auto",
      overflowX: "hidden",
    },
  },
}));
