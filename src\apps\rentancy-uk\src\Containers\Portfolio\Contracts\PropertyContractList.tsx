import { Authorization } from "Components/Authorization";
import {
  Box,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import { Link, useLocation, useNavigate, useParams } from "react-router-dom";
import { Add } from "@mui/icons-material";
import {
  CONTRACT_ROUTES,
  getXeroAccounts,
  RoleUtils,
  Tenancy,
  useApi,
  useDeviceScreen,
  useUser,
  useXeroConnection,
  XeroAccount,
  usePropertyDetails,
} from "@rentancy/common";
import { TenantsContractDetails } from "Components/Tenants/components/TenantsContractDetails";
import { WithFabAddButton } from "Components/HomePage/WithFabAddButton";
import { makeStyles } from "@mui/styles";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import { BulkUpload } from "./components/BulkUpload";
import { ContractTypeSelect } from "./components/ContractTypeSelect/ContractTypeSelect";
import { PropertyContractItemDesktop } from "./PropertyContractItemDesktop";
import { PropertyContractItemMobile } from "./PropertyContractItemMobile";
import { useTableStyles, useLayoutStyles, EmptyBackground } from "@rentancy/ui";
import { clsx } from "clsx";
import { EmptyState } from "./PropertyContractEmptyState";

export function PropertyContractList() {
  const { propertyContracts, details, loading, getContractList } = usePropertyDetails();
  const { apiCall: getXeroAccountsApi, data: ledgerCodes } = useApi(getXeroAccounts);
  const { integrationStatus } = useXeroConnection();
  const { user } = useUser();
  const params = useParams<{ tenancyId: string; id: string }>();
  const { isDesktop } = useDeviceScreen();
  const classes = useStyles();
  const tableClasses = useTableStyles();
  const layoutClasses = useLayoutStyles();
  const navigate = useNavigate();
  const location = useLocation();
  const isTenant = !RoleUtils.isAgent(user);
  const { t } = useTranslation();

  const [contractType, setContractType] = useState<string>("All");
  const [contractList, setContractList] = useState<Tenancy[]>([]);
  const [contractListHolder, setContractListHolder] = useState<Tenancy[]>([]);
  const [xeroLedgerCode, setXeroLedgerCode] = useState<XeroAccount>();

  useEffect(() => {
    if (ledgerCodes) {
      const pattern = /Rent Income/i;
      const account = ledgerCodes.filter(value => pattern.test(value.name));

      if (account && account.length > 0) {
        setXeroLedgerCode(account[0]);
      }
    }
  }, [ledgerCodes]);

  useEffect(() => {
    if (user && integrationStatus.status === "CONNECTED") {
      getXeroAccountsApi(user.organisationId);
    }
  }, [getXeroAccountsApi, integrationStatus.status, user]);

  useEffect(() => {
    setContractList(propertyContracts);
    setContractListHolder(propertyContracts);
  }, [propertyContracts]);

  useEffect(() => {
    if (contractType === "Archived") {
      const filteredContracts = contractListHolder.filter(
        item => item.status === contractType.toUpperCase(),
      );
      setContractList(filteredContracts);
    } else if (contractType === "Current") {
      const filteredContracts = contractListHolder.filter(item =>
        ["ACTIVE", "RENEW"].includes(item.status || ""),
      );
      setContractList(filteredContracts);
    } else if (contractType === "Hot list") {
      const filteredContracts = contractListHolder.filter(
        item => !["ARCHIVED"].includes(item.status || ""),
      );
      setContractList(filteredContracts);
    } else {
      setContractList(contractListHolder);
    }
  }, [contractListHolder, contractType]);

  useEffect(() => {
    getContractList();
  }, []);

  if (isTenant) {
    return <TenantsContractDetails />;
  }

  if (contractListHolder.length === 0) {
    return <EmptyState />;
  }

  return (
    <Box width="100%" display="flex" justifyContent="center">
      <Box display="flex" flexDirection="column" className={classes.contractContent}>
        {isDesktop && (
          <Authorization isAgent>
            <div className={clsx(tableClasses.searchWrapper, classes.contractTableFilters)}>
              <ContractTypeSelect value={contractType} onChange={value => setContractType(value)} />
              <BulkUpload />
              <Button
                variant="outlined"
                color="secondary"
                startIcon={<Add />}
                component={Link}
                to={`/contract/create?propertyId=${params.id}`}
                state={{ background: location }}
                data-testid="property-add-contract-btn"
              >
                {t("addTenancy")}
              </Button>
            </div>
          </Authorization>
        )}

        {isDesktop && (
          <TableContainer
            className={clsx(layoutClasses.flexFull, tableClasses.hideTopBorderRadius)}
          >
            <Table aria-label="Contracts List" stickyHeader>
              <TableHead>
                <TableRow>
                  <TableCell>{t("status")}</TableCell>
                  <TableCell width="80px">{t("type")}</TableCell>
                  <TableCell>{t("start")}</TableCell>
                  <TableCell>{t("end")}</TableCell>
                  <TableCell>{t("reference")}</TableCell>
                  <TableCell>{t("amount")}</TableCell>
                  <TableCell>{t("period")}</TableCell>
                  <TableCell>{t("primaryTenant")}</TableCell>
                  <TableCell />
                </TableRow>
              </TableHead>
              <TableBody>
                {contractList &&
                  contractList.map((contract, index) => (
                    <PropertyContractItemDesktop
                      data={contract}
                      propertyDetails={details}
                      key={contract.id}
                      index={index}
                      xeroLedgerCode={xeroLedgerCode}
                    />
                  ))}
                {!loading && !contractList?.length && <EmptyBackground />}
              </TableBody>
            </Table>
          </TableContainer>
        )}
        {!!propertyContracts.length && !isDesktop && (
          <div className={classes.listWrapper}>
            {contractList &&
              contractList.map(contract => (
                <PropertyContractItemMobile data={contract} key={contract.id} />
              ))}
          </div>
        )}
      </Box>

      {!isDesktop && (
        <WithFabAddButton
          height="75px"
          isHidden={false}
          onClick={() => {
            navigate(CONTRACT_ROUTES.createContractMobile(params.id!));
          }}
        />
      )}
    </Box>
  );
}

const useStyles = makeStyles(theme => ({
  contractContent: {
    width: "100%",
    height: "100%",
    boxSizing: "border-box",
    padding: theme.spacing(2.5),
  },
  contractTableFilters: {
    display: "flex",
    columnGap: theme.spacing(3.75),
    "& > *": {
      "&:nth-child(2)": {
        marginLeft: "auto",
      },
    },
  },
  listWrapper: {
    borderRadius: 4,
    border: `solid 1px ${theme.palette.specialColors.grey[200]}`,
    marginBottom: "60px",
  },
  noContract: {
    height: "21px",
    borderRadius: 4,
    padding: "25px",
    textAlign: "center",
    background: "transparent",
    border: "none",
    "& p": {
      margin: 0,
      color: theme.palette.specialColors.grey[400],
    },
    [theme.breakpoints.up("lg")]: {
      textAlign: "left",
      background: theme.palette.common.white,
      border: `solid 1px ${theme.palette.specialColors.grey[200]}`,
    },
  },
  emptyState: {
    background: theme.palette.common.white,
  },
}));
