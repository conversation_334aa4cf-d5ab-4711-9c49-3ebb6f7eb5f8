import { LazyLoad } from "@rentancy/ui";
import { Route, Routes as Switch, useLocation } from "react-router-dom";
import { lazy, memo, useCallback, useEffect, useRef } from "react";
import {
  DispatchInput,
  RoleUtils,
  ServiceWorkerUtils,
  WorkOrders,
  hasSubscription,
  useDeviceScreen,
  useGlobal,
  useUser,
  SubscriptionType,
  TaskPopup,
} from "@rentancy/common";
import { overwriteRoutesAfterAuth } from "loftypay";
import { useFlag } from "@unleash/proxy-client-react";

const BillsAllocation = lazy(() =>
  import("Containers/Finance/Bills/Allocation/BillsAllocation").then(({ BillsAllocation }) => ({
    default: BillsAllocation,
  })),
);

const ContactWrapper = lazy(() =>
  import("Containers/Contacts/ContactsWrapper").then(({ ContactWrapper }) => ({
    default: ContactWrapper,
  })),
);
const LeadsWrapper = lazy(() =>
  import("Containers/Leads/LeadsWrapper").then(({ LeadsWrapper }) => ({
    default: LeadsWrapper,
  })),
);
const ReferenceModal = lazy(() =>
  import("Containers/Contacts/Contact/ReferenceModal/ReferenceModal").then(
    ({ ReferenceModal }) => ({
      default: ReferenceModal,
    }),
  ),
);
const ContractDetailsMobile = lazy(() =>
  import("Containers/Portfolio/Contracts/contract/ContractDetailsMobile").then(
    ({ ContractDetailsMobile }) => ({ default: ContractDetailsMobile }),
  ),
);
const ContractsList = lazy(() =>
  import("Containers/Contracts/ContractsList").then(({ ContractsList }) => ({
    default: ContractsList,
  })),
);
const ConversationWrapper = lazy(() =>
  import("Containers/Conversation/ConversationWrapper").then(({ ConversationWrapper }) => ({
    default: ConversationWrapper,
  })),
);
const CommunicationWrapper = lazy(() =>
  import("Containers/Communication/CommunicationWrapper").then(({ CommunicationWrapper }) => ({
    default: CommunicationWrapper,
  })),
);

const CreateBillsPopup = lazy(() =>
  import("@rentancy/common").then(({ CreateBillsPopup }) => ({ default: CreateBillsPopup })),
);

const Dashboard = lazy(() =>
  import("Containers/Dashboard").then(({ Dashboard }) => ({ default: Dashboard })),
);
const TenantDocuments = lazy(() =>
  import("@rentancy/common/src/Containers/TenantPortal/Documents").then(
    ({ Documents: TenantDocuments }) => ({
      default: TenantDocuments,
    }),
  ),
);
const DesktopAttachmentPermissionPopup = lazy(() =>
  import("Components/Documents/DesktopDocumentUpload").then(
    ({ DesktopAttachmentPermissionPopup }) => ({ default: DesktopAttachmentPermissionPopup }),
  ),
);
const GlobalUpload = lazy(() =>
  import("Containers/UserProfile/GlobalUpload/GlobalUpload").then(({ GlobalUpload }) => ({
    default: GlobalUpload,
  })),
);
const DesktopContactPopup = lazy(() =>
  import("@rentancy/common").then(({ DesktopContactPopup }) => ({
    default: DesktopContactPopup,
  })),
);

const DesktopPropertyModal = lazy(() =>
  import("Containers/Portfolio/Desktop/DesktopPropertyModal").then(({ DesktopPropertyModal }) => ({
    default: DesktopPropertyModal,
  })),
);

const Documents = lazy(() =>
  import("Containers/Documents/DocumentsWrapper").then(({ DocumetsWrapper }) => ({
    default: DocumetsWrapper,
  })),
);
const DocusignModal = lazy(() =>
  import("Containers/Portfolio/Contracts/components/DocusignModal").then(({ DocusignModal }) => ({
    default: DocusignModal,
  })),
);
const FinanceWrapper = lazy(() =>
  import("Containers/Finance/FinanceWrapper").then(({ FinanceWrapper }) => ({
    default: FinanceWrapper,
  })),
);
const InviteContactToContractDesktop = lazy(() =>
  import("Containers/Portfolio/Contracts/components/InviteContactToContractDesktop").then(
    ({ InviteContactToContractDesktop }) => ({ default: InviteContactToContractDesktop }),
  ),
);
const InviteContactsToPropertyMobile = lazy(() =>
  import("Containers/Portfolio/Components/InviteContactsToPropertyMobile").then(
    ({ InviteContactsToPropertyMobile }) => ({ default: InviteContactsToPropertyMobile }),
  ),
);
const LandlordReport = lazy(() =>
  import("Containers/Reports/Landlord/LandlordReport").then(({ LandlordReport }) => ({
    default: LandlordReport,
  })),
);
const MobileDocumentUpload = lazy(() =>
  import("Components/Documents/MobileDocumentUpload").then(({ MobileDocumentUpload }) => ({
    default: MobileDocumentUpload,
  })),
);
const MobilePropertyLanding = lazy(() =>
  import("Containers/Portfolio/Mobile/MobilePropertyLanding").then(({ MobilePropertyLanding }) => ({
    default: MobilePropertyLanding,
  })),
);
const NonAgentInvitation = lazy(() =>
  import("@rentancy/common").then(({ NonAgentInvitation }) => ({
    default: NonAgentInvitation,
  })),
);
const PersonDetailsPopup = lazy(() =>
  import("Containers/Contacts/PersonDetailsPopup").then(({ PersonDetailsPopup }) => ({
    default: PersonDetailsPopup,
  })),
);
const WrapperPropertyDetails = lazy(() =>
  import("Containers/Portfolio/WrapperPropertyDetails").then(({ WrapperPropertyDetails }) => ({
    default: WrapperPropertyDetails,
  })),
);
const Properties = lazy(() =>
  import("Containers/Properties/Properties").then(({ Properties }) => ({ default: Properties })),
);
const ParentWrapper = lazy(() =>
  import("Containers/ParentProperty").then(({ ParentProperty }) => ({ default: ParentProperty })),
);
const MobileParentWrapper = lazy(() =>
  import("Containers/ParentProperty/MobileParentPropertyLanding").then(
    ({ MobileParentPropertyLanding }) => ({
      default: MobileParentPropertyLanding,
    }),
  ),
);
const ParentPropertyList = lazy(() =>
  import("Containers/ParentProperty/PropertyList").then(({ ParentPropertyList }) => ({
    default: ParentPropertyList,
  })),
);
const Reports = lazy(() =>
  import("Containers/Reports/WorkSpace/Reports").then(({ Reports }) => ({ default: Reports })),
);
const Settings = lazy(() =>
  import("Containers/Settings").then(({ Settings }) => ({ default: Settings })),
);

const Tasks = lazy(() =>
  import("Containers/Tasks/Tasks").then(({ Tasks }) => ({ default: Tasks })),
);
const TermsAndConditions = lazy(() =>
  import("Containers/SignUpWithEmail/TermsAndConditions").then(({ TermsAndConditions }) => ({
    default: TermsAndConditions,
  })),
);
const WrapperContact = lazy(() =>
  import("Containers/Contacts/Contact/WrapperContact").then(({ WrapperContact }) => ({
    default: WrapperContact,
  })),
);
const ViewReference = lazy(() =>
  import("Containers/Contacts/Contact/ViewReference").then(({ ViewReference }) => ({
    default: ViewReference,
  })),
);
const NotFound = lazy(() =>
  import("@rentancy/common/src/Components/NotFound").then(({ NotFound }) => ({
    default: NotFound,
  })),
);

const ReportIssue = lazy(() =>
  import("@rentancy/common").then(({ ReportIssue }) => ({ default: ReportIssue })),
);

const ReportProblemDialog = lazy(() =>
  import("@rentancy/common").then(({ ReportProblemDialog }) => ({ default: ReportProblemDialog })),
);
const ProblemCardListContextProvider = lazy(() =>
  import("@rentancy/common/src/Containers/ReportIssue/context/ProblemCardListProvider").then(
    ({ ProblemCardListContextProvider }) => ({ default: ProblemCardListContextProvider }),
  ),
);

const EndOfTenancy = lazy(() =>
  import("Containers/Contracts/EndOfTenancy").then(({ EndOfTenancy }) => ({
    default: EndOfTenancy,
  })),
);

const Application = lazy(() =>
  import("Containers/Contracts/Application").then(({ Application }) => ({
    default: Application,
  })),
);

//? Landlord imports
const LandlordPortfolio = lazy(() =>
  import("@rentancy/common").then(({ LandlordPortfolio }) => ({
    default: LandlordPortfolio,
  })),
);

const Statements = lazy(() =>
  import("@rentancy/common/src/Containers/LandlordPortal").then(({ Statements }) => ({
    default: Statements,
  })),
);

const ContractDetailsMobilePopup = lazy(() =>
  import("Containers/Portfolio/Contracts/ContractDetailsMobilePopup").then(
    ({ ContractDetailsMobilePopup }) => ({ default: ContractDetailsMobilePopup }),
  ),
);

const ClientPayoutPage = lazy(() =>
  import("Containers/Finance/Payouts/Landlord/ClientPayoutPage").then(({ ClientPayoutPage }) => ({
    default: ClientPayoutPage,
  })),
);

export const Routes = memo(() => {
  const { user } = useUser();
  const { globalState, dispatch } = useGlobal();
  const { isDesktop } = useDeviceScreen();
  const location = useLocation();
  const background = createBackGroundLink(location, isDesktop);
  const isCommunicationRESTEnabled = useFlag("CommunicationREST");

  const ref = useRef<number>();

  const updateStatusUser = useCallback(async () => {
    if (!user.username) {
      return;
    }
    try {
      if (user.authStatus === "signedIn") {
        if ("Notification" in window) {
          Promise.resolve(Notification.requestPermission()).then(function () {
            ServiceWorkerUtils.registerPushNotifications(user);
          });
        }
      }
    } catch (error) {
      console.log("🚀 ~ file: index.ts Routes ~ line 170 ~ useEffect ~ error", error);
    }
  }, [user]);

  useEffect(() => {
    updateStatusUser();
    const online = () => {
      updateStatusUser();
    };
    const offline = () => {
      clearInterval(ref.current);
    };
    window.addEventListener("online", online);
    window.addEventListener("offline", offline);

    return () => {
      clearInterval(ref.current);
      window.removeEventListener("online", online);
      window.removeEventListener("offline", offline);
    };
  }, [updateStatusUser]);

  useEffect(() => {
    setActiveMenu(location.pathname, dispatch, globalState.activeMenu, location.search);
    //eslint-disable-next-line
  }, [location.pathname, location.search]);

  return (
    <LazyLoad>
      <Switch location={background || location}>
        <Route
          path="/"
          element={
            hasSubscription(user, [SubscriptionType.LOFTYPAY]) ? (
              <Dashboard />
            ) : isCommunicationRESTEnabled ? (
              <CommunicationWrapper />
            ) : (
              <ConversationWrapper />
            )
          }
        />
        <Route path="/conversations" element={<ConversationWrapper />} />
        {isCommunicationRESTEnabled && (
          <Route path="/communication" element={<CommunicationWrapper />} />
        )}
        <Route path="/terms" element={<TermsAndConditions />} />
        {
          <>
            {overwriteRoutesAfterAuth(true)
              .filter(route => /^\/accounting/.test(route.path))
              .map((route, index) => (
                <Route key={index} path={route.path} element={route.element} />
              ))}
          </>
        }

        {RoleUtils.isTenant(user) && (
          <>
            <Route path="/report-issues/*" element={<ReportIssue />} />
            <Route
              path="/report-issues/:category/:mode?/:propertyId?/:problemId?"
              element={<ReportIssue />}
            />
            <Route path="/tenancy/:tenancyId" element={<ContractDetailsMobile />} />
            <Route path="/tenant/documents" element={<TenantDocuments />} />
          </>
        )}
        {RoleUtils.isLandlord(user) && (
          <>
            <Route path="/dashboard" element={<Dashboard />} />
            <Route path="/portfolio/*" element={<LandlordPortfolio />} />
            <Route path="/statements" element={<Statements />} />
            <Route path="/workorders/*" element={<WorkOrders />} />
          </>
        )}
        {RoleUtils.isAgent(user) ? (
          <>
            <Route path="/dashboard" element={<Dashboard />} />
            <Route path="/documents" element={<Documents />} />
            <Route path="/parent-properties" element={<ParentPropertyList />} />
            <Route path="/parent-properties/:id/:tab?" element={<ParentWrapper />} />
            {!isDesktop && (
              <Route path="/parent-properties/:id/tabs" element={<MobileParentWrapper />} />
            )}
            <Route path="/properties/:mode?" element={<Properties />} />
            <Route path="/reports/landlord/:id" element={<LandlordReport />} />
            <Route
              path="/property/:id/details/invite-user/:role"
              element={<InviteContactsToPropertyMobile />}
            />
            <Route path="/property/:id/*" element={<WrapperPropertyDetails />} />
            <Route path="/settings/*" element={<Settings />} />
            <Route path="/tenancy/:tenancyId" element={<ContractDetailsMobile />} />
            <Route path="/tasks/" element={<Tasks />} />
            <Route
              path="/contacts/person/:id/tenant-screening/:referenceId"
              element={<ViewReference />}
            />
            <Route path="/contacts/people" element={<ContactWrapper />} />
            <Route path="/contacts/leads" element={<LeadsWrapper />} />
            <Route path="/contacts/person/:id/*" element={<WrapperContact />} />
            <Route path="/finance/bills/allocation/:id" element={<BillsAllocation />} />
            <Route path="/finance/*" element={<FinanceWrapper />} />
            <Route path="/reports" element={<Reports />} />
            <Route path="/contracts/:tab?" element={<ContractsList />} />
            <Route path="/end-of-tenancy/*" element={<EndOfTenancy />} />
            <Route path="/application/*" element={<Application />} />
            <Route path="/client/:id/payouts" element={<ClientPayoutPage />} />
          </>
        ) : null}
        {!isDesktop && (
          <>
            <Route path="/upload" element={<GlobalUpload />} />
            {RoleUtils.isAgent(user) ? (
              <>
                <Route
                  path="/contacts/person/:id/documents/create"
                  element={<MobileDocumentUpload />}
                />
                <Route
                  path="/property/:propertyId/contracts/details/:id"
                  element={<ContractDetailsMobilePopup />}
                />

                <Route path="/contacts/people/:type/:id?" element={<DesktopContactPopup />} />
                <Route path="/invitation/:id" element={<NonAgentInvitation />} />
                <Route
                  path="/contacts/create-reference/:type/:applicantId/:referenceId?"
                  element={<ReferenceModal />}
                />
                <Route path="/property/:id" element={<MobilePropertyLanding />} />
                <Route path="/tasks/:boardId/task/:taskId/?/*" element={<TaskPopup />} />
              </>
            ) : null}
          </>
        )}
        <Route path="*" element={<NotFound />} />
      </Switch>

      {!isDesktop && (
        <Switch>
          {overwriteRoutesAfterAuth(false).map((route, index) => (
            <Route key={index} path={route.path} element={route.element} />
          ))}
        </Switch>
      )}
      {isDesktop && (
        <Switch>
          <Route path="/upload" element={<GlobalUpload />} />
          {RoleUtils.isAgent(user) ? (
            <>
              <Route path="/contacts/person/overview/:id?" element={<PersonDetailsPopup />} />
              <Route
                path="/property/:propertyId/problem-reports/create"
                element={
                  <ProblemCardListContextProvider>
                    <ReportProblemDialog open />
                  </ProblemCardListContextProvider>
                }
              />
              <Route
                path="/property/:propertyId/problem-reports/:problemId/create"
                element={
                  <ProblemCardListContextProvider>
                    <ReportProblemDialog open />
                  </ProblemCardListContextProvider>
                }
              />
              <Route path="/tasks/:boardId/task/:taskId/?/*" element={<TaskPopup />} />
              <Route path="/contacts/people/:type/:id?" element={<DesktopContactPopup />} />
              <Route path="/property/edit?" element={<DesktopPropertyModal />} />
              <Route path="/property/:id/details/:type?" element={<DesktopPropertyModal />} />
              <Route path="/invitation/:id" element={<NonAgentInvitation />} />
              <Route
                path="/property/:id/contracts/:tenancyId/details/invite-user/:role"
                element={<InviteContactToContractDesktop />}
              />
              <Route
                path="/property/:id/documents/create"
                element={<DesktopAttachmentPermissionPopup multiple />}
              />
              <Route
                path="/contacts/person/:id/documents/create"
                element={<DesktopAttachmentPermissionPopup multiple />}
              />
              <Route
                path="/contacts/create-reference/:type/:applicantId/:referenceId?"
                element={<ReferenceModal />}
              />
              <Route
                path="/property/:id/contracts/:tenancyId/details/docusign"
                element={<DocusignModal />}
              />
              <Route
                path="/property/:id/contracts/:tenancyId/attachments/docusign"
                element={<DocusignModal />}
              />
              <Route
                path="/property/:id/contracts/:tenancyId/invoices/docusign"
                element={<DocusignModal />}
              />
              <Route path="/property/:id/documents/docusign" element={<DocusignModal />} />
              <Route path="/contacts/person/:id/documents/docusign" element={<DocusignModal />} />
              <Route path="/property/docusign" element={<DocusignModal />} />
              <Route path="/contracts/:idContractType/docusign" element={<DocusignModal />} />
              <Route
                path="/property/:id/contracts/:tenancyId/attachments/upload"
                element={<DesktopAttachmentPermissionPopup multiple />}
              />
              <Route path="/tasks/bills" element={<CreateBillsPopup />} />
              <Route path="/bills/create" element={<CreateBillsPopup />} />
              {overwriteRoutesAfterAuth(false).map((route, index) => (
                <Route key={index} path={route.path} element={route.element} />
              ))}
            </>
          ) : null}
        </Switch>
      )}
    </LazyLoad>
  );
});
Routes.displayName = "Routes";

//#TODO find a way to remove "any" from location and type it properly
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function createBackGroundLink(location: any, isDesktops: boolean) {
  if (!isDesktops) {
    return location;
  }

  if (location.state && location.state.background) {
    return location.state.background;
  }
  //this code is for when user wants to open popup with url instead of clicking on a button
  //and we have to set up a background to show behind the popup

  switch (true) {
    case location.pathname === "/bills/create":
      return { pathname: "/finance/bills" };

    case location.pathname.includes("/contacts/person/overview"):
      return { pathname: `/contacts/person/${location.pathname.split("/")[4]}` };

    case location.pathname === "/upload":
      return { pathname: "/documents" };

    case location.pathname.includes("/contacts/people/edit"):
      return { pathname: `/contacts/person/${location.pathname.split("/")[4]}` };

    case location.pathname.includes("/property/edit"):
      return { pathname: `/property/edit/${location.pathname.split("/")[3]}` };

    case location.pathname.includes(`/tasks/${location.pathname.split("/")[2]}/create`):
    case location.pathname.includes(`/tasks/${location.pathname.split("/")[2]}/task/`):
    case location.pathname.includes("/tasks/bills"):
      return { pathname: "/tasks" };

    case location.pathname.includes(`/property/${location.pathname.split("/")[2]}/details/edit`):
      return { pathname: `/property/${location.pathname.split("/")[2]}/details` };

    case location.pathname.includes(
      `/property/${location.pathname.split("/")[2]}/documents/create`,
    ):
      return { pathname: `/property/${location.pathname.split("/")[2]}/documents` };

    case location.pathname.includes(
      `/property/${location.pathname.split("/")[2]}/problem-reports/create`,
    ):
      return { pathname: `/property/${location.pathname.split("/")[2]}/problem-reports` };

    case location.pathname.includes(
      `/property/${location.pathname.split("/")[2]}/problem-reports/${
        location.pathname.split("/")[4]
      }/create`,
    ):
      return {
        pathname: `/property/${location.pathname.split("/")[2]}/problem-reports/${
          location.pathname.split("/")[4]
        }`,
      };

    case location.pathname.includes(
      `/contacts/person/${location.pathname.split("/")[3]}/documents/create`,
    ):
      return { pathname: `/contacts/person/${location.pathname.split("/")[3]}/documents` };

    case location.pathname.includes(`/contracts/${location.pathname.split("/")[4]}/create`): {
      return {
        pathname: `/property/${location.pathname.split("/")[2]}/contracts/${
          location.pathname.split("/")[4]
        }/details`,
      };
    }
    case location.pathname.includes("/contracts/create"): {
      return {
        pathname: "/contracts",
      };
    }

    case location.pathname.includes(
      `/property/${location.pathname.split("/")[2]}/contracts/${
        location.pathname.split("/")[4]
      }/attachment/upload`,
    ):
      return {
        pathname: `/property/${location.pathname.split("/")[2]}/contracts/${
          location.pathname.split("/")[4]
        }/attachment`,
      };

    case location.pathname.includes("/invitation/"):
      return { pathname: "/contacts/people/" };

    case location.pathname.includes("/tenancy/") && location.pathname.includes("/invite-user/"):
      return { pathname: `/tenancy/${location.pathname.split("/")[2]}` };

    case location.pathname.includes(
      `/property/${location.pathname.split("/")[2]}/contracts/${
        location.pathname.split("/")[4]
      }/details/docusign`,
    ):
      return {
        pathname: `/property/${location.pathname.split("/")[2]}/contracts/${
          location.pathname.split("/")[4]
        }/details`,
      };

    case location.pathname.includes(
      `/property/${location.pathname.split("/")[2]}/documents/docusign`,
    ):
      return {
        pathname: `/property/${location.pathname.split("/")[2]}/documents`,
      };

    case location.pathname.includes(
      `/contacts/person/${location.pathname.split("/")[3]}/documents/docusign`,
    ):
      return {
        pathname: `/contacts/person/${location.pathname.split("/")[3]}/documents`,
      };

    case location.pathname.includes("/contract/"):
      return { pathname: "/contracts/all" };
  }
}

function setActiveMenu(
  location: string,
  dispatch: (i: DispatchInput) => void,
  activeMenu: string,
  search: string,
) {
  const path = location.toLocaleLowerCase();
  const isLeadsPage = window.location.search.includes("APPLICANT");
  switch (true) {
    case path.includes("/documents") && !path.includes("/property") && !path.includes("/person"):
    case path.startsWith("/upload"):
      return (
        activeMenu !== "DOCUMENTS" && dispatch({ type: "SET_ACTIVE_MENU", value: "DOCUMENTS" })
      );
    case path.includes("/parent-properties"):
      return (
        activeMenu !== "PARENT_PROPERTY" &&
        dispatch({ type: "SET_ACTIVE_MENU", value: "PARENT_PROPERTY" })
      );
    case path.includes("properties") || path.includes("property"):
      return (
        activeMenu !== "PROPERTIES" && dispatch({ type: "SET_ACTIVE_MENU", value: "PROPERTIES" })
      );
    case path.includes("tasks"):
      return (
        activeMenu !== "TASKS_BOARD" && dispatch({ type: "SET_ACTIVE_MENU", value: "TASKS_BOARD" })
      );
    case path === "/" || path.includes("conversation") || path.includes("group-chat"):
      return (
        activeMenu !== "CONVERSATIONS" &&
        dispatch({ type: "SET_ACTIVE_MENU", value: "CONVERSATIONS" })
      );
    case path.includes("/workorders"):
      return (
        activeMenu !== "WORKORDERS" && dispatch({ type: "SET_ACTIVE_MENU", value: "WORKORDERS" })
      );
    case path.includes("contacts/people"): {
      return isLeadsPage
        ? activeMenu !== "LEADS" && dispatch({ type: "SET_ACTIVE_MENU", value: "LEADS" })
        : activeMenu !== "CONTACTS_PEOPLE" &&
            dispatch({ type: "SET_ACTIVE_MENU", value: "CONTACTS_PEOPLE" });
    }
    case path.includes("contacts/leads") ||
      path.includes("interested") ||
      path.includes("suggested") ||
      path.includes("tenant-screening"):
      return activeMenu !== "LEADS" && dispatch({ type: "SET_ACTIVE_MENU", value: "LEADS" });
    case path.includes("contacts/applicants"):
      return (
        activeMenu !== "APPLICANTS" && dispatch({ type: "SET_ACTIVE_MENU", value: "APPLICANTS" })
      );
    case (path.includes("contacts/people") ||
      path.includes("contacts/person") ||
      path.includes("/invitation/")) &&
      !search.includes("PROSPECT"):
      return (
        activeMenu !== "CONTACTS_PEOPLE" &&
        dispatch({ type: "SET_ACTIVE_MENU", value: "CONTACTS_PEOPLE" })
      );
    case path.includes("settings"):
      return (
        activeMenu !== "SETTINGS_WORKSPACE" &&
        dispatch({ type: "SET_ACTIVE_MENU", value: "SETTINGS_WORKSPACE" })
      );
    case path.includes("settings/notifications"):
      return (
        activeMenu !== "SETTINGS_NOTIFICATIONS" &&
        dispatch({ type: "SET_ACTIVE_MENU", value: "SETTINGS_NOTIFICATIONS" })
      );
    case path.includes("finance"):
      return activeMenu !== "FINANCE" && dispatch({ type: "SET_ACTIVE_MENU", value: "FINANCE" });
    case path.includes("calendar"):
      return activeMenu !== "CALENDAR" && dispatch({ type: "SET_ACTIVE_MENU", value: "CALENDAR" });
    case path.includes("/reports"):
      return activeMenu !== "REPORTS" && dispatch({ type: "SET_ACTIVE_MENU", value: "REPORTS" });
    case path.includes("/contracts"):
      return (
        activeMenu !== "CONTRACTS" && dispatch({ type: "SET_ACTIVE_MENU", value: "CONTRACTS" })
      );
    case path.includes("/end-of-tenancy"):
      return (
        activeMenu !== "END_OF_TENANCY" &&
        dispatch({ type: "SET_ACTIVE_MENU", value: "END_OF_TENANCY" })
      );
    case path.includes("/application"):
      return (
        activeMenu !== "APPLICATION" && dispatch({ type: "SET_ACTIVE_MENU", value: "APPLICATION" })
      );
    case path.includes("/dashboard"):
      return (
        activeMenu !== "DASHBOARD" && dispatch({ type: "SET_ACTIVE_MENU", value: "DASHBOARD" })
      );
    case path.includes("/applications/prospect"):
      return dispatch({ type: "SET_ACTIVE_MENU", value: "PROSPECT" });
    case path.includes("/applications/rental"):
      return dispatch({ type: "SET_ACTIVE_MENU", value: "RENTAL" });
    case path.includes("/report-issues"):
      return dispatch({ type: "SET_ACTIVE_MENU", value: "REPORT_ISSUES" });
    //? Landlord Portal specific
    case path === "/portfolio":
      return dispatch({ type: "SET_ACTIVE_MENU", value: "PORTFOLIO" });
    case path === "/statements":
      return dispatch({ type: "SET_ACTIVE_MENU", value: "STATEMENTS" });
    case path.includes("accounting"):
      return (
        activeMenu !== "ACCOUNTING" && dispatch({ type: "SET_ACTIVE_MENU", value: "ACCOUNTING" })
      );
    default:
      return dispatch({ type: "SET_ACTIVE_MENU", value: "" });
  }
}
