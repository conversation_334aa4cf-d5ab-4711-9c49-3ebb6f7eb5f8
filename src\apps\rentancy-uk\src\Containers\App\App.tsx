import "./init-amplify";
import { BrowserRouter, Route, Routes, useNavigate } from "react-router-dom";
import { FC, lazy, useEffect, useState } from "react";
import { clsx } from "clsx";
import {
  FlowControl,
  GlobalProvider,
  NetworkGlobal,
  NotificationProvider,
  UserGlobal,
  ErrorBoundary,
  useUser,
  hasSubscription,
  SubscriptionType,
  RoleUtils,
  downloadDocument,
  IntegrationStatusProvider,
  DictionariesProvider,
  WebSocketProvider,
} from "@rentancy/common";
import { IconButton, Typography } from "@mui/material";
import { Icon } from "@rentancy/icon";
import AiBot from "assets/icons/aibot.png";
import { LazyLoad } from "@rentancy/ui";
import { fetchWSToken } from "Api/User";

import {
  ClientProvider,
  makeClient,
  ChatBot,
  useLazyRef,
  QueryClient,
  QueryClientProvider,
} from "@loftyworks/chatbot";
import "@loftyworks/chatbot/style.css";

import { apiFetch } from "Api/ChatBot";

import { makeStyles } from "@mui/styles";
import { Authorization } from "Components/Authorization";
import { Layout } from "./layout/Layout";
import { initAdTrack } from "./ad";
import mixpanel from "mixpanel-browser";
import { useFlag, useFlagsStatus } from "@unleash/proxy-client-react";
import { addLoftyPayRoutes } from "loftypay";
import { IdleDetector } from "Components/IdleDetector";
import { HTTP_ENDPOINT, REALTIME_ENDPOINT } from "Constants/index";

const apiClient = makeClient(apiFetch);

const Landing = lazy(() =>
  import("./layout/Landing").then(({ Landing }) => ({ default: Landing })),
);

const Oauth = lazy(() =>
  import("Containers/Settings/Oauth").then(({ Oauth }) => ({ default: Oauth })),
);

const AddPropertyLayout = lazy(() =>
  import("Containers/Property/Add").then(({ AddPropertyLayout }) => ({
    default: AddPropertyLayout,
  })),
);
const CreateContracts = lazy(() =>
  import("Containers/Contracts/CreateContracts/CreateContracts").then(({ CreateContracts }) => ({
    default: CreateContracts,
  })),
);
const SharingPage = lazy(() =>
  import("Containers/Property/Details/SharingPage").then(({ SharingPage }) => ({
    default: SharingPage,
  })),
);

const ProspectApplicationWrapper = lazy(() =>
  import("@rentancy/common/src/Containers/ProspectApplication").then(
    ({ ProspectApplicationWrapper }) => ({
      default: ProspectApplicationWrapper,
    }),
  ),
);

const Impersonation = lazy(() =>
  import("Containers/Impersonate/Impersonation").then(({ Impersonation }) => ({
    default: Impersonation,
  })),
);

const AddParentProperty = lazy(() =>
  import("Containers/ParentProperty/AddParentProperty").then(({ AddParentProperty }) => ({
    default: AddParentProperty,
  })),
);

const ConfirmationEmail = lazy(() =>
  import("Containers/Contracts/EndOfTenancy/ConfirmationEmail/ConfirmationEmail").then(
    ({ ConfirmationEmail }) => ({
      default: ConfirmationEmail,
    }),
  ),
);
const ConfirmationEmailSuccessfully = lazy(() =>
  import("Containers/Contracts/EndOfTenancy/ConfirmationEmail/ConfirmationEmailSuccessfully").then(
    ({ ConfirmationEmailSuccessfully }) => ({
      default: ConfirmationEmailSuccessfully,
    }),
  ),
);
initAdTrack();

const basicRoutes: { path: string; element: JSX.Element }[] = [
  {
    path: "/property/create",
    element: (
      <Authorization isAgent>
        <LazyLoad>
          <AddPropertyLayout isNewProperty />
        </LazyLoad>
      </Authorization>
    ),
  },
  {
    path: "/property/edit/:id",
    element: (
      <Authorization isAgent>
        <LazyLoad>
          <AddPropertyLayout />
        </LazyLoad>
      </Authorization>
    ),
  },
  {
    path: "/contract/create",
    element: (
      <Authorization isAgent>
        <LazyLoad>
          <CreateContracts />
        </LazyLoad>
      </Authorization>
    ),
  },
  {
    path: "/parent-property/add",
    element: (
      <Authorization isAgent>
        <LazyLoad>
          <AddParentProperty />
        </LazyLoad>
      </Authorization>
    ),
  },
  {
    path: "/parent-property/:id/edit",
    element: (
      <Authorization isAgent>
        <LazyLoad>
          <AddParentProperty />
        </LazyLoad>
      </Authorization>
    ),
  },
  {
    path: "/landing",
    element: (
      <LazyLoad>
        <Landing />
      </LazyLoad>
    ),
  },
  {
    path: "/sharinglink",
    element: (
      <LazyLoad>
        <SharingPage />
      </LazyLoad>
    ),
  },
  {
    path: "/impersonate/:action?",
    element: (
      <LazyLoad>
        <Impersonation />
      </LazyLoad>
    ),
  },
  {
    path: "/auth_callback/:type",
    element: (
      <LazyLoad>
        <Oauth />
      </LazyLoad>
    ),
  },
  {
    path: "/tenant-portal/rental-application",
    element: (
      <LazyLoad>
        <ProspectApplicationWrapper urlPrefix={process.env.RENTANCY_BE_ENDPOINT} />
      </LazyLoad>
    ),
  },
  {
    path: "/confirmation-eot-email/?",
    element: (
      <LazyLoad>
        <ConfirmationEmail />
      </LazyLoad>
    ),
  },
  {
    path: "/confirmation-eot-email-success",
    element: (
      <LazyLoad>
        <ConfirmationEmailSuccessfully />
      </LazyLoad>
    ),
  },
];

export const App: FC = () => {
  const classes = useStyles();
  // const { t } = useTranslation();
  const { flagsReady } = useFlagsStatus();
  const loftyPayEnabled = useFlag("LoftyPayUK");
  const AIChatBotEnabled = useFlag("AIChatBot");
  let routes: { path: string; element: JSX.Element }[] = [];

  // todo: remove, what does this for ?
  useEffect(() => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (window as any).dataLayer = (window as any).dataLayer || [];
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (window as any).dataLayer.push({
      event: "pageview",
    });
  }, []);

  //load walkme and Mixpanel
  useEffect(() => {
    const mixpanelToken = process.env.MIXPANEL_TOKEN as string;
    const isDevelopmentMode = process.env.NODE_ENV === "development";

    mixpanel.init(mixpanelToken || " ", {
      track_pageview: "url-with-path-and-query-string",
      persistence: "localStorage",
      opt_out_tracking_by_default: !mixpanelToken,
    });

    if (isDevelopmentMode) {
      return;
    }
    const walkme = document.createElement("script");
    walkme.type = "text/javascript";
    walkme.async = true;
    walkme.src = process.env.WALKME_SCRIPT_URL || "";
    const scriptElement = document.getElementsByTagName("script")[0];
    scriptElement.parentNode && scriptElement.parentNode.insertBefore(walkme, scriptElement);
    window._walkmeConfig = { smartLoad: true };
  }, []);

  //redirect to lofty-works
  useEffect(() => {
    if (document.location.host === "app.rentancy.com") {
      document.location.replace(
        `https://app.loftyworks.com${document.location.pathname}${document.location.search}`,
      );
    }
  }, []);

  if (flagsReady) {
    routes = basicRoutes;

    if (loftyPayEnabled) {
      addLoftyPayRoutes(basicRoutes);
    }
  }

  return (
    <div data-testid="app" className={classes.root}>
      <div className={classes.container}>
        <NetworkGlobal>
          <BrowserRouter>
            <GlobalProvider>
              <UserGlobal>
                <FlowControl>
                  <NotificationProvider>
                    <IntegrationStatusProvider>
                      <DictionariesProvider>
                        <ErrorBoundary>
                          <WebSocketProvider
                            httpDomain={HTTP_ENDPOINT}
                            realtimeDomain={REALTIME_ENDPOINT}
                            tokenFetcher={fetchWSToken}
                          >
                            <Routes>
                              {routes.map((route, index) => (
                                <Route key={index} path={route.path} element={route.element} />
                              ))}
                              <Route path="*" element={<Layout />} />
                            </Routes>
                            {AIChatBotEnabled && <ChatWidget />}
                            <IdleDetector />
                          </WebSocketProvider>
                        </ErrorBoundary>
                      </DictionariesProvider>
                    </IntegrationStatusProvider>
                  </NotificationProvider>
                </FlowControl>
              </UserGlobal>
            </GlobalProvider>
          </BrowserRouter>
        </NetworkGlobal>
      </div>
    </div>
  );
};

const ChatWidget = () => {
  const classes = useStyles();
  const { user } = useUser();
  const navigate = useNavigate();
  const [previewOpen, setPreviewOpen] = useState(false);

  const queryClient = useLazyRef(() => new QueryClient());

  return hasSubscription(user, [SubscriptionType.LOFTYPAY]) ? null : RoleUtils.isAgent(user) ? (
    <>
      <div className={classes.chatwidget}>
        <img
          src={AiBot}
          className={classes.aibot}
          onClick={() => setPreviewOpen(!previewOpen)}
          alt="ai-bot"
        />
      </div>
      <div className={clsx(classes.chat, previewOpen && classes.open)}>
        <div className={classes.header}>
          <div className={classes.title}>
            <Typography
              variant="h4"
              color="text.primary"
              sx={{ fontWeight: 500, textAlign: "center" }}
            >
              LoftyWorks AI
            </Typography>
            <Icon name="beta" size={40} />
          </div>

          <IconButton onClick={() => setPreviewOpen(!previewOpen)}>
            <Icon name="close" />
          </IconButton>
        </div>
        <div className={classes.body}>
          <ClientProvider
            client={apiClient}
            navigate={navigate}
            downloadDocument={downloadDocument}
          >
            <QueryClientProvider client={queryClient}>
              <ChatBot />
            </QueryClientProvider>
          </ClientProvider>
        </div>
      </div>
    </>
  ) : null;
};

const useStyles = makeStyles(theme => ({
  root: {
    minHeight: "100vh",
    display: "flex",
    justifyContent: "space-around",
    [theme.breakpoints.down("lg")]: {
      maxHeight: "100dvh",
      minHeight: "100dvh",
      overflow: "hidden",
    },
  },
  container: {
    backgroundColor: "#ffffff",
    width: "100%",
  },
  chatwidget: {
    position: "absolute",
    bottom: "80px",
    right: "20px",
  },
  aibot: {
    cursor: "pointer",
    width: "50px",
    height: "50px",
  },
  chat: {
    padding: "16px",
    position: "fixed",
    top: "80px",
    bottom: "20px",
    right: "20px",
    width: "500px",
    height: "calc(100vh - 140px)",
    zIndex: 1,
    border: "1px solid #ebecf1",
    borderRadius: "16px",
    boxShadow: "-2px 4px 15px 0px #2024371a",
    backgroundColor: "#FAFBFD",
    transform: "translateX(1000px)",
    transition: "all 0.5s ease-in-out",
  },
  open: {
    transform: "translateX(0)",
  },
  header: {
    display: "flex",
    alignItems: "center",
    width: "100%",
    height: "30px",
  },
  title: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    width: "100%",
    gap: "5px",
  },
  body: {
    height: "100%",
  },
}));
