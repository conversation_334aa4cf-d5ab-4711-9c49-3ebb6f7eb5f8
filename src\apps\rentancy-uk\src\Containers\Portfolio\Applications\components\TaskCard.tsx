import React, { FC, useState, useMemo } from "react";
import { MoreHoriz } from "@mui/icons-material";

import {
  Typography,
  Grid,
  IconButton,
  Box,
  CircularProgress,
  CircularProgressProps,
  Menu,
  MenuItem,
} from "@mui/material";
import { makeStyles } from "@mui/styles";
import { useTranslation } from "react-i18next";
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";
import ArrowDropUpIcon from "@mui/icons-material/ArrowDropUp";
import Complete from "assets/Application/complete.png";
import Cancel from "assets/Application/cancel.png";
import moment from "moment";
import { TaskTable } from "./TaskTable";

function CircularProgressWithLabel(
  props: CircularProgressProps & { value: number; status: string },
) {
  const { status, value, ...restProps } = props;

  let imageSrc: string | null = null;
  if (status === "Cancelled") {
    imageSrc = Cancel;
  } else if (value === 100 || status === "Completed") {
    imageSrc = Complete;
  }

  if (imageSrc) {
    return (
      <Box sx={{ position: "relative", display: "inline-flex" }}>
        <img
          src={imageSrc}
          alt={status === "Cancelled" ? "Cancelled status" : "Completed status"}
          width={50}
          height={50}
        />
      </Box>
    );
  }

  return (
    <Box sx={{ position: "relative", display: "inline-flex" }}>
      <CircularProgress
        variant="determinate"
        sx={{
          color: theme => theme.palette.grey[theme.palette.mode === "light" ? 200 : 800],
        }}
        size={50}
        thickness={4}
        value={100}
      />
      <CircularProgress
        variant="determinate"
        value={value}
        {...restProps}
        sx={{
          position: "absolute",
          left: 0,
        }}
      />
      <Box
        sx={{
          top: 0,
          left: 0,
          bottom: 0,
          right: 0,
          position: "absolute",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <Typography
          variant="caption"
          component="div"
          sx={{ color: "primary.main" }}
        >{`${Math.round(value)}%`}</Typography>
      </Box>
    </Box>
  );
}

export const TaskCard: FC = ({
  item,
  type,
  setSelectedApplication,
  setSelectedManagerId,
  showModalByType,
  refetch,
}: {
  item: any;
  type: string;
  setSelectedApplication: (id: string) => void;
  setSelectedManagerId: (id: string[]) => void;
  showModalByType: (type: string) => void;
  refetch?: () => void;
}) => {
  const classes = useStyles();
  const [expand, setExpand] = useState(true);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const { t } = useTranslation();
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleAction = (modalType: "assign" | "cancel" | "complete" | "delete") => {
    handleClose();
    setSelectedApplication(item?.id);
    setSelectedManagerId(item?.managers ?? []);
    showModalByType(modalType);
  };

  const handleAssign = () => handleAction("assign");
  const handleCancel = () => handleAction("cancel");
  const handleComplete = () => handleAction("complete");
  const handleDelete = () => handleAction("delete");

  const progress = useMemo(() => {
    return (item?.tasks.filter(item => item?.status === "Completed")?.length / 15) * 100;
  }, [item?.tasks]);

  return (
    <Grid container className={classes.root}>
      <Grid container item gap={2.5} className={classes.card}>
        <IconButton onClick={() => setExpand(prev => !prev)}>
          {expand ? <ArrowDropDownIcon name="arrow-down" /> : <ArrowDropUpIcon fontSize="medium" />}
        </IconButton>

        <CircularProgressWithLabel
          value={Math.round(progress)}
          size={50}
          thickness={4}
          status={item?.status}
        />
        <Grid item flex={1}>
          <Typography variant="subtitle1" className={classes.title}>
            {item?.primaryTenantName ?? "-"}
          </Typography>
          <Typography variant="body1" className={classes.body}>
            Move-in Date {moment(item?.moveInDate).format("DD MMM YYYY")}
          </Typography>
        </Grid>
        {type === "inProgress" && (
          <Grid item>
            <IconButton onClick={handleClick}>
              <MoreHoriz />
            </IconButton>
          </Grid>
        )}

        <Menu
          id="basic-menu"
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          MenuListProps={{
            "aria-labelledby": "basic-button",
            sx: { width: "180px" },
          }}
        >
          <MenuItem onClick={handleAssign}>{t("Assign")}</MenuItem>
          <MenuItem onClick={handleCancel}>{t("Cancel")}</MenuItem>
          <MenuItem onClick={handleComplete}>{t("Complete")}</MenuItem>
          <MenuItem onClick={handleDelete}>
            <span className={classes.danger}>{t("Delete")}</span>
          </MenuItem>
        </Menu>
      </Grid>
      {expand ? (
        <TaskTable
          type={type}
          applicationId={item?.id}
          data={item?.tasks ?? []}
          refetch={refetch}
        />
      ) : null}
    </Grid>
  );
};

const useStyles = makeStyles(theme => ({
  root: {
    display: "flex",
    flexDirection: "row",
    marginBottom: "15px",
  },
  card: {
    display: "flex",
    background: "#FFF",
    padding: "15px 20px",
    alignItems: "center",
  },
  title: {
    color: theme.palette.text.primary,
  },
  body: {
    color: theme.palette.text.secondary3,
  },
  danger: {
    color: theme.palette.specialColors.red2,
  },
  actionBtn: {
    background: "#5D51E21A",
    width: "120px",
    height: "36px",
  },
  itemList: {
    borderTop: `1px solid ${theme.palette.specialColors.themeBorder}`,
    paddingTop: "0px !important",
  },
  itemListUnCollapsed: {
    borderTopWidth: "0px",
    paddingTop: "0px !important",
  },
}));
