import { Box, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";

export const Ledger = () => {
  const { t } = useTranslation("loftyPay");

  return (
    <Box p={3}>
      <Typography variant="h5" gutterBottom>
        {t("ledger")}
      </Typography>
      <Typography variant="body1" color="textSecondary">
        Ledger functionality will be implemented here.
      </Typography>
    </Box>
  );
};
