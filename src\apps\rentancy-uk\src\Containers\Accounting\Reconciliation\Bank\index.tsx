import { Box, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";

interface Props {
  data?: any[];
  loading?: boolean;
  refetch?: () => void;
}

export const Bank = ({ data, loading, refetch }: Props) => {
  const { t } = useTranslation("loftyPay");

  return (
    <Box p={3}>
      <Typography variant="h5" gutterBottom>
        {t("bank")}
      </Typography>
      <Typography variant="body1" color="textSecondary">
        Bank reconciliation functionality will be implemented here.
      </Typography>
      {loading && (
        <Typography variant="body2" sx={{ mt: 2 }}>
          Loading bank data...
        </Typography>
      )}
      {data && (
        <Typography variant="body2" sx={{ mt: 2 }}>
          Bank accounts: {data.length}
        </Typography>
      )}
    </Box>
  );
};
