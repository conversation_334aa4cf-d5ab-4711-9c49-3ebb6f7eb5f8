import { Header<PERSON>ells } from "@rentancy/ui";
import {
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
} from "@mui/material";
import { makeStyles } from "@mui/styles";
import { FC, useMemo, useRef } from "react";
import { clsx } from "clsx";
import { useTranslation } from "react-i18next";
import { useTableStyles } from "@rentancy/ui";
import { useGlobal } from "@rentancy/common";
import moment from "moment/moment";
import { ApplicationHeaderCellId } from "../types";
import { updateApplicationTask } from "Api/Application/index";

interface StatusConfig {
  label: string;
  color: string;
  bg: string;
}

const STATUS_ENUM: Record<string, StatusConfig> = {
  Completed: {
    label: "Completed",
    color: "rgba(24, 182, 135, 1)",
    bg: "rgba(27, 202, 150, 0.15)",
  },
  Todo: {
    label: "To-Do",
    color: "rgba(93, 81, 226, 1)",
    bg: "rgba(102, 64, 216, 0.15)",
  },
  Failed: {
    label: "Failed",
    color: "#FF285F",
    bg: "#FF285F1A",
  },
};

interface LabelProps {
  status: keyof typeof STATUS_ENUM;
  item: any;
}

const StatusLabel: FC<LabelProps> = ({ status, item }) => {
  const statusConfig = STATUS_ENUM[status];
  const classes = useStyles();
  const { t } = useTranslation();

  if (!statusConfig) {
    console.warn(`Unknown status: ${status}`);
    return null;
  }

  if (status === "Cancelled") {
    return (
      <Tooltip
        arrow
        classes={{ tooltip: classes.statusTooltip, arrow: classes.arrow }}
        title={`Cancellation Reason: ${item?.remark}`}
        placement="bottom"
      >
        <span
          style={{
            padding: "4px 10px",
            lineHeight: "20px",
            borderRadius: "30px",
            color: "#81858D",
            background: "#A0A3AF26",
          }}
        >
          {t("Cancelled")}
        </span>
      </Tooltip>
    );
  }

  return (
    <span
      style={{
        padding: "4px 10px",
        lineHeight: "20px",
        borderRadius: "30px",
        color: statusConfig.color,
        background: statusConfig.bg,
      }}
    >
      {statusConfig.label}
    </span>
  );
};

const SHOW_MENU = [
  "Register with Scheme",
  "Confirm Tenancy Details",
  "Send Agreement",
  "Mark When Signed",
  "Notify Local Council",
  "Arrange Key Collection",
  "Confirm Key Handover",
  "Send Reference Form to Applicant",
];

const MenuAction = ({
  type,
  data,
  applicationId,
  refetch,
}: {
  type: string;
  data: any;
  applicationId: string;
  refetch: () => void;
}) => {
  const classes = useStyles();
  const { emitter } = useGlobal();
  const { t } = useTranslation();
  const item = data;

  const showAction = SHOW_MENU.includes(data?.name) && type === "inProgress";

  const handleComplete = async () => {
    await updateApplicationTask({ applicationId, taskId: data?.id, status: "Completed" });
    refetch();
  };

  const handleCancel = () => {
    updateApplicationTask({ applicationId, taskId: data?.id, status: "Failed" });
    refetch();
  };

  const handleView = () => {
    if (!SHOW_MENU.includes(item?.name) && type === "inProgress") {
      emitter?.emit("openTaskDetails", {
        ...item,
        applicationId,
        taskId: item?.id,
        type: item?.name,
        category: item?.category,
      });
    }
  };

  return (
    <TableCell style={{ textAlign: "right" }}>
      {showAction ? (
        <>
          <span className={clsx(classes.action, classes.actionContainer)} onClick={handleComplete}>
            {t("Completed")}
          </span>
          <span className={classes.action} onClick={handleCancel}>
            {t("Failed")}
          </span>
        </>
      ) : (
        <>
          <span className={clsx(classes.action, classes.status)} onClick={handleView}>
            {t("View")}
          </span>
        </>
      )}
    </TableCell>
  );
};

export const TaskTable = ({
  type,
  data,
  applicationId,
  refetch,
}: {
  type: string;
  data?: any;
  applicationId: string;
  refetch: () => void;
}) => {
  const classes = useStyles();
  const tableClasses = useTableStyles();

  const { t } = useTranslation();
  const container = useRef<HTMLDivElement>(null);

  const headerCells: HeaderCells<ApplicationHeaderCellId> = useMemo(
    () => [
      {
        id: "status",
        label: t("modules.application.tablecolumn.status"),
        sortable: false,
      },
      {
        id: "category",
        label: t("modules.application.tablecolumn.category"),
        sortable: false,
        style: {},
      },
      {
        id: "task",
        label: t("modules.application.tablecolumn.task"),
        sortable: true,
        sort: "movingDate",
        style: {},
      },
      {
        id: "lastDate",
        label: t("modules.application.tablecolumn.lastDate"),
        sortable: false,
        style: {},
      },
      {
        id: "action",
        label: "",
        sortable: false,
        style: {},
      },
    ],
    [t],
  );

  return (
    <Grid container className={classes.root}>
      <Grid className={tableClasses.tableWrapper}>
        <TableContainer
          ref={container}
          className={classes.tableContainer}
          sx={theme => ({
            "& tbody > tr:last-child > td": {
              borderBottom: `1px solid ${theme.palette.specialColors.grey[200]}`,
            },
          })}
        >
          <Table aria-label="simple table" stickyHeader>
            <TableHead>
              <TableRow>
                {headerCells.map(item => {
                  return (
                    <TableCell
                      key={item.id}
                      sortDirection={false}
                      className={classes.head}
                      style={{ ...item.style }}
                    >
                      <span style={item.style}>{item.label}</span>
                    </TableCell>
                  );
                })}
              </TableRow>
            </TableHead>
            <TableBody>
              {data.map((item: any) => (
                <TableRow
                  key={item.id}
                  className={clsx(classes.wrapperRow)}
                  data-testid="property-table-row"
                >
                  <TableCell>
                    <StatusLabel status={item?.status} item={item} />
                  </TableCell>
                  <TableCell>{item?.category ?? ""}</TableCell>
                  <TableCell>{item?.name ?? "-"}</TableCell>
                  <TableCell>{moment(item?.updatedAt).format("DD MMM YYYY") ?? "-"}</TableCell>
                  <MenuAction
                    data={item}
                    type={type}
                    applicationId={applicationId}
                    refetch={refetch}
                  />
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Grid>
    </Grid>
  );
};

export const useStyles = makeStyles(theme => ({
  root: {
    display: "flex",
    flexDirection: "column",
    flexWrap: "nowrap",
  },
  tableContainer: {
    border: "none",
    flex: 1,
    overflowX: "auto",
    "& tbody ": {
      position: "relative",
      "& > tr > td > *": {
        width: "max-content",
      },
    },
  },
  statusTooltip: {
    backgroundColor: theme.palette.specialColors.grey[800],
    fontSize: 12,
    color: theme.palette.specialColors.grey[400],
    padding: theme.spacing(1, 0, 1, 2),
  },
  tooltip: {
    backgroundColor: theme.palette.specialColors.grey[800],
    fontSize: 12,
    color: theme.palette.specialColors.grey[400],
    padding: theme.spacing(1, 4),
  },
  arrow: {
    "&::before": {
      backgroundColor: theme.palette.specialColors.grey[800],
    },
  },
  head: {
    fontWeight: 600,
    backgroundColor: "#fff",
  },
  propertyAvatar: {
    width: 24,
    height: 18,
  },
  avatar: {
    width: theme.spacing(3.75),
    height: theme.spacing(3.75),
    marginRight: 12,
  },
  wrapperRow: {
    cursor: "pointer",
    textDecoration: "unset",
    overflow: "hidden",
    "& .MuiAvatar-root": {
      width: "30px",
      height: "30px",
    },
  },
  landlord: {
    "& p": {
      overflow: "hidden",
      maxWidth: "100px",
      whiteSpace: "nowrap",
      textOverflow: "ellipsis",
    },
  },
  action: {
    cursor: "pointer",
    color: "rgba(93, 81, 226, 1)",
  },
  status: {
    padding: theme.spacing(0.5, 0.5),
  },
  actionContainer: {
    padding: theme.spacing(0, 2.5),
  },
  borderTop: {
    borderTop: `1px solid ${theme.palette.specialColors.grey[200]}`,
  },
  noDataWrapper: {
    position: "absolute",
    display: "grid",
    placeItems: "center",
    inset: "150px 0 0 0",
    color: theme.palette.specialColors.grey[400],
    rowGap: theme.spacing(1.25),
  },
}));
