enum OrganisationLedgerType {
    XERO
    LOFTYWORKS
}

enum OrganisationSubscriptionType {
    LOFTYWORKS
    LOFTYPAY
    FULL
}

enum PaymentStatus{
    BLOCKED
    ACTIVE
}

type Organisation
@model(queries: {get: "getOrganisation"}, mutations: {update: "updateOrganisation"}, subscriptions: null)
@key(name: "gsi-ByZooplaBranchId", fields: ["zooplaBranchID"])
{
    id: ID!  
    emailId: String
    name: String!
    email: AWSEmail
    phone: String
    whatsAppAddress: String
    description: String
    logo: String
    emailStatus: String
    workspaceCountry: String
    city: String
    connectedFinanceIntegration: String
    postcode: String
    state: String
    country: String
    addressLine1: String
    addressLine2: String
    addressLine3: String
    adminUser: String! 
    botUser: String
    rentancyMail: String
    customMail: String
    invoiceConversation: String
    reportConversation: String
    createdAt: AWSDateTime 
    updatedAt: AWSDateTime 
    vouchToken: String
    website: String
    utmCustomerAttributionSource: String
    emailTemplates: [MailSample]
    currency: String
    timeZoneName: String!
    utcMinuteOffset: Int!
    commissionBillVatNumber: String
    addCommissionBillVat: Boolean
    addCommissionBillVatInclusive: Boolean
    dailyNotificationsEnabled: Boolean
    payoutStatementVersion: PayoutStatementVersion
    payer: Boolean     
    type: OrganisationType
    ledgerCodes: [LedgerCode]     
    templateLetterCategories: [String]
    incomeLedgerCodeNames: [String]!
    invoiceTemplates: [InvoiceTemplate]
    trackingCategories: [RentancyTrackingCategory]
    clientBatchPayments: [Int]
    enableJournal: Boolean
    journalDate: Int
    journalPeriod: JournalPeriod
    docusignAccessToken: String
    docusignAccessTokenExpiry: String
    docusignRefreshToken: String
    docusignAccountId: String
    docusignAccountUrl: String
    stripeSubscribed: Boolean
    stripeCustomerId: String
    stripeChargeDayOfTheMonth: Int
    stripeFreeTrialCompleted: Boolean
    stripePaymentMethodId: String
    stripeOutstandingPaymentIntentId: String
    stripeHasOutstandingPaymentIntent: Boolean
    stripeOutstandingPaymentOccuredAt: AWSDateTime
    chimeRefreshToken: String
    chimeAccessToken: String
    chimeExpirationDate: AWSDateTime
    chimeCode: String
    chimeCreatedAt: AWSDateTime
    rentancyApiKey: String
    rentancyApiKeyGenerationDate: AWSDateTime
    clientBatchPaymentsOnDemand: Boolean
    overseasResidentBillVAT: Boolean
    defaultCountryTaxBotId: String
    balanceTransferContactUserId: String
    openingBalanceContactUserId: String
    depositSchemeLedgerContactUserId: String
    contactComments: [ContactComment] @connection(name: "OrganisationContactComments")
    rightmoveBranchID: String
    zooplaBranchID: String
    #Possible values: null, 'landlordStatementTemplate'. If landlordStatementTemplate is selected that pdf will gets generated
    landlordStatementTemplateType: String
    receiveMarketingEmailConsent: Boolean
    landingPageStatus: String
    landingPageImages: [Document]
    payoutVersion: PayoutVersion
    transUnionLandlordId: String
    transUnionConnectionEnabled: Boolean
    users: [OrganisationUser] @connection(name: "OrgranisationUsers")
    boards: [Board] @connection(name: "OrgranisationBoards")
    columns: [Column] @connection(name: "OrgranisationColumns")
    tasks: [Task] @connection(name: "OrgranisationTasks", sortField: "createdAt")
    activity: [Activity] @connection(name: "OrganisationActivities", sortField: "updatedDate")
    properties: [Property] @connection(name: "OrganisationProperties")
    tenancies: [Tenancy] @connection(name: "OrganisationTenancies")
    conversations: [ConvoLink] @connection(name: "OrganisationLinks")
    documentTemplate: [DocumentTemplate] @connection(name: "organisationDocumentTemplates")
    messages: [Message] @connection(name: "OrganisationMessages")
    documents: [Document] @connection(name: "OrganisationDocuments", sortField: "expiry")
    emails: [EmailMessage] @connection(name: "OrganisationEmails")
    supplierOrganisations: [SupplierOrganisation] @connection(name: "OrganisationSupplierOrganisations", sortField: "name")
    tags: [SupplierOrganisationTag] @connection(name: "OrganisationTags")
    invitations: [Invitation] @connection(name: "OrganisationInvitations")
    addresses: [Address] @connection(name: "OrganisationAddresses")
    invoices: [Invoice] @connection(name: "OrganisationInvoices", sortField: "date")
    invoiceAlocations: [InvoiceAllocation] @connection(name: "OrganisationInvoiceAllocations")
    transactions: [Transaction] @connection(name: "OrganisationTransactions", sortField: "date")
    transfers: [Transfer] @connection(name: "OrganisationTransfers", sortField: "date")
    payments: [Payment] @connection(name: "OrganisationPayments", sortField: "date")
    accounts: [Account] @connection(name: "OrganisationAccounts")
    tenancySettings: TenancySettings @connection(name: "OrganisationTenancySettings")
    taskLabels: [TaskLabel] @connection(name: "OrganisationTaskLabels", sortField: "name")
    journals: [Journal] @connection(keyName: "gsi-OrganisationChanges")
    problemCards: ModelOrganisationProblemReportsConection @function(name: "tenancyService-${env}")
    convos: [Conversation] @connection(name: "OrganisationConversations")
    reminders: [Reminder] @connection(name: "OrganisationReminders")
    applicationQuestionSetting: ApplicationQuestionSetting! @connection(name: "ApplicationSetting")
    rentInvoiceHistory: [RentInvoiceHistory] @connection(name: "RentInvoiceHistoryOrganisation")
    taskReminders: [TaskReminder] @connection(name: "OrganisationTaskReminders")
    documentSuppliers: [DocumentSuppliers] @connection(name: "OrganisationDocumentSuppliers")
    taskChecklist: [OrganisationTaskChecklist] @connection(name: "OrganisationChecklists")
    portfolioHistory: [PortfolioHistory] @connection(name: "OrganisationPortfolioHistory")
    xeroJournals: [XeroJournal] @connection(name: "OrganisationXeroJournals")
    organisationReportingHistory: [ReportingHistory] @connection(name: "OrganisationReportingHistory", sortField: "labelDateQuery")
    workOrders: [WorksOrder] @connection(name: "OrganisationWorkOrders")
    docusignTemplates: [DocusignTemplates] @connection(name: "OrganisationDocusignTemplates")
    docusignSendDocumentEnvelopes: [DocusignSendDocumentEnvelopes] @connection(name: "OrganisationDocusignSendEnvelopes")
    overPayments: [OverPayment] @connection(name: "OrganisationOverPayments", sortField: "status")
    parentPropertyEntities: [ParentPropertyEntity] @connection(name: "ParentPropertyEntityOrganisation")
    taskSchedulers: [TaskScheduler] @connection(name: "organisationTaskSchedulers")
    automatedTasks: [AutomatedTasks] @connection(name: "OrganisationAutomatedTasks")
    templateLetters: [TemplateLetter] @connection(name: "OrganisationTemplateLetters")
    hideImportExportPage: Boolean
    # EXPIRED, UNEXPIRED
    expiredFlag: String
    zillowIntegration: Boolean
    # New attributes for conversation modules
    conversationTags: [ConversationTag]
    nationalProcessingMetadata: NPMetadata     
    ledgerType: OrganisationLedgerType

    # Currently that attribute helps to recognize what type of product is used ( LoftyWorks - LoftyPay - Full)
    subscriptionType: OrganisationSubscriptionType
}

type NPMetadata {
    apiKey: String
    merchantId: String
    siteId: String
    rechargePercentage: Int # actual percentage * 10000
    maxCharge: Int # actual charge * 100
    ACHSwitch: Boolean # null, false -> off
    CreditCardSwitch: Boolean # null, false -> off
}

type ConversationTag {
    id: ID!
    name: String!
}

enum PayoutStatementVersion {
    DEFAULT
    SPLIT_OWNERSHIP
}

enum JournalPeriod {
    PRIOR_MONTH
    CURRENT_MONTH
}

enum PayoutVersion {
    PROPERTY
    LANDLORD
}

type LoftyOrganisationIntegration
@model(queries: null, mutations: null, subscriptions: null)
@key(name: "gsi-ByLoftyAgentId", fields: ["loftyAgentId"])
@key(name: "gsi-ByTeamId", fields: ["teamId"])
@key(name: "gsi-ByOrganisationId", fields: ["organisationId"])
{
    id: ID!
    customerCode: String
    loftyAgentId: String
    loftyAgentBaseId: String

    email: String
    phone: String
    cognitoId: String
    teamId: String
    organisationName: String

    loftyWorksAgentId: String
    loftyWorksAgentBaseId: String
    organisationId: ID
    userId: ID
    createdAt: AWSDateTime
    updatedAt: AWSDateTime
}

type OrganisationStripeCharges
@model(queries: null, mutations: {create: "createOrganisationStripeCharge"}, subscriptions: null)
@key(name: "gsi-ByOrganisation", fields: ["organisationId"])
@key(name: "gsi-ByStripePaymentIntent", fields: ["stripePaymentIntentId"])
{
    id: ID!  
    organisationId: ID!  
    createdAt: AWSDateTime!  
    updatedAt: AWSDateTime!  
    occuredAt: AWSDateTime!  
    currency: String!  
    number: String  
    amount: Int!  
    propertyCount: Int!  
    eventType: String!  
    chargeType: String!  
    data: String!  
    error: String  
    errorMessage: String  
    success: Boolean  
    stripePaymentIntentId: String!  
}

type RentancyApiHistory
@model(queries: null, mutations: null, subscriptions: null)
@key(name: "gsi-ByOrganisationAndDate", fields: ["organisationId", "requestDate"])
{
    id: ID!
    organisationId: ID!
    requestDate: String!
    requestStartedAt: String
    requestEndedAt: String
    status: Int
    method: String
    path: String
    queryParameters: String
    body: String
}

type ReportApplicant {
    applicantName: String
    lettingsNegotiator: String # UK
    propertyManager: String # US
    leadStatus: String
    lastUpdatedDate: String
    viewingsArranged: String
    nextViewingDate: String
    date: String
}

input ReportApplicantInput {
    userId: ID
    startDate: String
    endDate: String
    sortBy: String
}

type DocusignTemplates
@model(queries: null, mutations: null, subscriptions: null)
@key(name: "gsi-DocusignTemplateId", fields: ["docusignTemplateId"])
{
    id: ID!
    name: String!
    docusignTemplateId: String!
    roles: [String]!
    organisation: Organisation! @connection(name: "OrganisationDocusignTemplates")
    documentTemplates: [DocumentTemplate] @connection(name: "documentTemplateDocusignTemplate")
    sendDocumentTemplates: [DocusignSendDocumentEnvelopes] @connection(name: "DocusignTemplateSendDocuments")
}

type DocusignSendDocumentEnvelopes
@model(queries: null, mutations: null, subscriptions: null)
@key(name: "gsi-DocusignEnvelopeId", fields: ["envelopeId"])
{
    id: ID!
    roles: String!
    envelopeId: String!
    organisation: Organisation! @connection(name: "OrganisationDocusignSendEnvelopes")
    docusignTemplate: DocusignTemplates @connection(name: "DocusignTemplateSendDocuments")
    user: User @connection(name: "DocusignTemplateSendUser")
    tenancy: Tenancy @connection(name: "DocusignTemplateSendTenancy")
    property: Property @connection(name: "DocusignTemplateSendProperty")
}


type OrganisationTaskChecklist
@model(mutations: {create: "createOrganisationTaskChecklist", update: "updateOrganisationTaskChecklist", delete: null}, subscriptions: null)
{
    id: ID!
    name: String!
    organisation: Organisation! @connection(name: "OrganisationChecklists")
    taskChecklist: [TaskChecklist] @connection(name: "organisationTaskChecklists")
    defaultChecklists: [Checklist]!
}

type TaskChecklist
@model(queries: null, mutations: {create: "createTaskChecklist", update: "updateTaskChecklist", delete: "deleteTaskChecklist" }, subscriptions: null)
@searchable
{
    id: ID!
    organisationTaskChecklist: OrganisationTaskChecklist @connection(name: "organisationTaskChecklists")
    task: Task @connection(name: "taskChecklists")
    checklists: [Checklist]!
    createdAt: AWSDateTime
    updatedAt: AWSDateTime
}

type Checklist {
    name: String!
    status: Boolean!
    updatedAt: String
    userInitials: String
}

type Reminder
@model(queries: null, mutations: {update: "updateReminder"}, subscriptions: null)
{
    id: ID!
    reminderOrganisationId: String!
    organisation: Organisation @connection(name: "OrganisationReminders")
    notifyStartDate: Boolean!
    startDateNotificationDays: [Int]
    notifyReviewDate: Boolean!
    reviewDateNotificationDays: [Int]
    notifyEndDate: Boolean!
    endDateNotificationDays: [Int]
    notifyBreakDate: Boolean!
    breakDateNotificationDays: [Int]
    notifyDocumentExpiry: Boolean!
    documentExpiryNotificationDays: [Int]
    notifyTenancyRenewal: Boolean!
    tenancyRenewalNotificationDays: [Int]
    notifyInventoryWarranty: Boolean!
    inventoryWarrantyNotificationDays: [Int]
}

type TaskReminder
@model(queries: {get: "getTaskReminder"}, mutations: {update: "updateTaskReminder"}, subscriptions: null)
{
    id: ID!
    reminderItems: [ReminderItem]!
    createdAt: AWSDateTime
    updatedAt: AWSDateTime
    organisation: Organisation! @connection(name: "OrganisationTaskReminders")
}

type ReminderItem {
    name: String!
    enabled: Boolean
    firstReminder: Int
    secondReminder: Int
    parentType: ParentType
}

type SupplierProperty
@model(
    queries: null,
    mutations: null,
    subscriptions: null
)
@key(fields: ["supplierId", "propertyId"])
@key(name: "gsi-ByProperty", fields: ["propertyId"])
{
    supplierId: ID!
    propertyId: ID!
}

type User
@model(queries: {list: "listUsers"}, mutations: {update: "updateUser", create: "createUser"})
@searchable(queries: {search: null})
@key(name: "gsi-ByOrganisation", fields: ["currentOrganisation"])
@key(name: "gis-ByEmail", fields: ["email"])
@key(name: "gsi-ByCognitoId", fields: ["cognitoId"])
@key(name: "gsi-ByCognitoEmail", fields: ["cognitoEmail"])
@key(name: "gsi-ByXeroId", fields: ["xeroId"])
@key(name: "gsi-ByDenormalizedEmails", fields: ["denormalizedEmails"])
{
    id: ID  
    xeroId: String
    cognitoId: String
    cognitoEmail: AWSEmail
    reference: String
    addressLine1: String
    addressLine2: String
    addressLine3: String
    income: String
    incomeFrequency: Period
    locality: String
    region: String
    socialSecurityNumber: String
    employmentStatus: String
    vat: String
    email: String
    fname: String
    sname: String
    companyName: String
    emails: [ContactEmail]
    phones: [ContactPhone]
    whatsAppNumber: String
    title: String
    leadId: String
    createdIn: UserCreationHistory
    fromChime: Boolean
    # don't use createdDate, use createdAt instead
    createdDate: AWSDateTime
    homeAddress: Address @function(name: "userService-${env}")
    postalAddress: Address @function(name: "userService-${env}")
    internalNotes: String
    newInternalNotes: [ConversationInternalNote]
    public: Boolean!
    forceSignOut: Boolean
    nameConfirmed: Boolean
    type: UserType
    roles: [Role]
    tags: [String]
    metadata: Metadata  
    currentOrganisation: String  
    onboardingStep: Int
    onboardingSeen: Boolean
    source: String
    overseasResident: Boolean
    overseasResidentExemptionCertificate: String
    overseasResidentExemptionDate: AWSDateTime
    overseasResidentTax: String
    beneficiaryName: String
    bankName: String
    bankAccountNumber: String
    bankAccountSortCode: String
    bankAccountType: String # CHECKING BUSINESS SAVINGS
    bankIBAN: String
    bankSWIFT: String
    bankRoutingNumber: String
    paymentStatus: PaymentStatus
    bankAddress: String
    transUnionRenterId: String
    disabled: Boolean  
    ownedProperties: [OwnedProperty]
    business: Business @connection(name: "UserBusiness")
    organisations: [OrganisationUser]  @connection(name: "UserOrganisations")
    boards: [Board] @connection(name: "UserBoards")
    tasks: [Task] @connection(name: "UserTasks")
    preferences: UserPreferences @connection(name: "UserPreferences")
    documentTemplate: [DocumentTemplate] @connection(name: "userDocumentTemplates")
    activity: [Activity] @connection(name: "UserActivities", sortField: "updatedDate")
    properties: [Property] @connection(name: "PropertyOwner")
    image: File @connection(name: "UserProfile")
    conversations: [ConvoLink] @connection(name: "UserLinks")
    messages: [Message] @connection(name: "UserMessages")
    # Connection was removed here
    supplierOrganisation: SupplierOrganisation
    problemCards: [ProblemCard] @connection(name: "UserProblemCards")
    journals: [Journal] @connection(keyName: "gsi-UserChanges")
    documents(sortDirection: ModelSortDirection, limit: Int, filter: ModelDocumentFilterInput): ModelDocumentConnection @function(name: "attachmentsService-${env}")
    invitations: [Invitation] @connection(name: "UserInvitation")
    invoiceHistory: [RentInvoiceHistory] @connection(name: "RentInvoiceHistoryUser")
    documentSuppliers: [DocumentSuppliers] @connection(name: "UserDocumentSuppliers")
    associatedProperties: [Property] @function(name: "userService-${env}")
    associatedTenancies: [Tenancy] @function(name: "userService-${env}")
    portfolioHistory: [PortfolioHistory] @connection(name: "UserPortfolioHistory")
    taskComments: [TaskComment] @connection(name: "UserTaskComments")
    contactComments: [ContactComment] @connection(name: "UserContactComments")
    workOrders: [WorksOrder] @connection(name: "workOrderUser")
    reportedWorkOrders: [WorksOrder] @connection(name: "workOrderReporterUser")
    statements: [Statement] @connection(name: "ClientStatements")
    applications: [PropertyApplication] @connection(name: "UserApplications")
    tenantApplications: [PropertyApplication] @connection(name: "TenantApplications")
    docusignTemplateSend: [DocusignSendDocumentEnvelopes] @connection(name: "DocusignTemplateSendUser")

    lastName: String
    # SSN:social security number, ITIN:personal tax identification number
    ssnOrItin: String
    driverLicenseState: String
    driverLicenseNumber: String
    dateOfBrithday: AWSDateTime
    maritalStatus: String
    gender: String
    # order for agents
    agentOrder: Int
    applicationAssign: [PropertyApplication] @connection(name: "UserApplicationAssign")
    createdAt: AWSDateTime
    applicantInvestigationReference: [InvestigationReference] @connection(name: "InvestigationReferenceApplicants")
    guarantorInvestigationReference: InvestigationReference @connection(name: "InvestigationReferenceGuarantor")
    applicantMetaData: ApplicantMetaData

    # New attributes for conversation modules
    pinConversations: [PinConversation]
    doNotDisturbConversationIds: [ID]
    nonAutoSyncTrigger: Boolean # true: when update the data, it won't trigger auto sync to Lofty.
    loftyAgentBaseId: String
    updatedBy: String

    denormalizedEmails: String
    # Subtype association
    category: SupplierCategory
    supplierProperty: [SupplierProperty] @connection(fields: ["id"])
    subtype: [Subtype!]

    rightToRent: RightToRent
    checkedBy: String
    checkedAt: AWSDateTime
}

enum RightToRent {
    PASSED
    PENDING
}

enum SupplierCategory {
    CONTRACTOR
    UTILITY
    OTHER
}

enum Subtype {
    # Utilities
    ELECTRICITY
    WATER
    GAS
    TV
    INTERNET
    PHONE
    # Contractors
    HANDYMAN
    ELECTRICIAN
    CLEANER
    PLUMBER
    BUILDER
    INVENTORY_CLERK
}

type PaginatedUsers {
    page: Int!
    pageSize: Int!
    totalItems: Int!
    totalPages: Int!
    users: [User]
}

type UserClientSettings
@model
@key(name: "gsi-ByUser", fields: ["userId"])
{
    id: ID!
    userId: ID!
    expiringTenanciesPageVisitedAt: AWSDateTime
}

type PreUser
@model(queries: null, mutations: null, subscriptions: null)
@searchable
{
    id: ID!
    fname: String
    sname: String
    email: String
    phone: String
    createdAt: AWSDateTime
    updatedAt: AWSDateTime
}

type PinConversation {
    conversationCategory: ConversationCategory  # SHARED,PERSONAL,ARCHIVED
    pinConversationIds: [ID]
}

type OwnedProperty {
    parentProperty: Boolean!
    propertyId: String!
    percentage: Int!
}

type ApplicantMetaData {
    status: ApplicantStatus
    # id in type User
    lettingsNegotiators: [ID]
    expectedMoveIn: AWSDateTime
    monthlyIncome: Int
    expectedMaxRent: Int
    expectedMinRent: Int
    expectedMinBedRooms: Int
    expectedMinBathRooms: Float
    expectedCity: String
    expectedState: String
    occupantCount: Int
    extraNotes: String
    allowPets: [String]
    guarantorId: ID
    applicantType: String # lofty leadType
}

enum ApplicantStatus {
    NEW
    CONTACT_MADE
    APPLICANT_QUESTIONNAIRE_SENT
    VIEWING_SCHEDULED
    OFFER_MADE
    OFFER_ACCEPTED
    START_REFERENCING
    LOST_LEAD
    NEW_LEADS
    ATTEMPTING_CONTACT
    NURTURING_COLD
    WARM
    HOT
    APPOINTMENT_SET
    SHOWING
    PENDING
    CLOSED
    BAD_LEADS
    DO_NOT_CONTACT
    NULL
}

type ApplicantScheduleViewing
@model(queries: null, mutations: {delete: "deleteApplicantScheduleViewing"}, subscriptions: null)
@key(name: "gsi-ByApplicantId", fields: ["applicantId"])
@key(name: "gsi-ByOrganisationId", fields: ["organisationId"])
{
    id: ID!
    organisationId: ID!
    propertyId: ID!
    # id of applicant in type User
    applicantId: ID!
    applicantPhoneNumber: String
    # id of property manager in type User
    assignedToIds: [ID!]!
    startDate: AWSDateTime
    endDate: AWSDateTime
    createdAt: AWSDateTime
    createdBy: ID
    updatedAt: AWSDateTime
    updatedBy: ID
    # id in type Task
    taskId: ID
    metadata: ScheduleViewingMetadata # {gmailId:'',outlookId:''}
}

type ScheduleViewingMetadata {
    gmailId: String
    outlookId: String
}

type InterestedProperty
@model(queries: null, mutations: {delete: "deleteInterestedProperty"}, subscriptions: null)
@key(name: "gsi-ByInterestedUserId", fields: ["interestedUserId"], queryField: "listByInterestedUserId")
{
    id: ID!
    organisationId: ID!
    propertyId: ID!
    property: Property @connection(fields: ["propertyId"])
    unitId: ID
    # id in type User
    interestedUserId: ID!
    # the platform that use know property
    source: InterestedPropertySource
    createdAt: AWSDateTime
    updatedAt: AWSDateTime
}

enum InterestedPropertySource{
    RIGHT_MOVE
    ZOOPLA
}

type OrganisationUserMetaData
@model(queries: {get: "getOrganisationUserMetaData"}, mutations: null, subscriptions: null)
{
    id: ID!
    watchHistory: [PropertyVisit]
    updateAt: AWSDateTime
}

type PropertyVisit {
    propertyId: String
    addressLine1: String
    visitDate: AWSDateTime
}

type Address
@searchable
@model(queries: null, mutations: {create: "createAddress", update: "updateAddress"}, subscriptions: null)
@key(name: "gsi-ByParentIdAndParentType", fields: ["parentId", "parentType"])
{
    id: ID!
    addressLine1: String
    addressLine2: String
    addressLine3: String
    city: String
    postcode: String
    state: String
    country: String
    type: AddressType!
    parentId: String!
    parentType: ParentType!
    organisation: Organisation! @connection(name: "OrganisationAddresses")
    createdAt: AWSDateTime
    updatedAt: AWSDateTime
}

type UserPreferences
@model(queries: null, mutations: null, subscriptions: null)
@key(name: "gsi-ByUserId", fields: ["userPreferencesUserId"])
{
    id: ID!
    allowedNotifications: [NotificationType!]!
    userPreferencesUserId: ID!
    user: User! @connection(name: "UserPreferences", fields: ["userPreferencesUserId"])
}

type OrganisationUser
@model(queries: {get: "getOrganisationUser"}, mutations: {update: "updateOrganisationUser"}, subscriptions: null)
{
    id: ID!
    organisation: Organisation!  @connection(name: "OrgranisationUsers")
    user: User! @connection(name: "UserOrganisations")

    dashboardWidgets: [[DashboardWidgetType]]
    onboardingComplete: Boolean
    hideAdvertisementBanner: Boolean

    organisationUserOrganisationId: String!
    organisationUserUserId: String!
}

enum DashboardWidgetType {
    COMMUNICATION
    CONTRACTOR_VENDOR
    LANDLORD
    DEPOSIT
    TASK
    TENANCY
    CERTIFICATE
    PROPERTY,
    RENT
    LEAD
    INVENTORY
}

type Integration
@model(queries: null, mutations: null, subscriptions: null)
@key(name: "gsi-OrganisationUserIntegration", fields: ["organisationId", "userId"])
@key(name: "gsi-OrganisationIntegrations", fields: ["organisationId"])
@key(name: "gsi-TenantIntegration", fields: ["tenantId"])
@key(name: "gsi-ByState", fields: ["state"])
@key(name: "gsi-ByEmailAddress", fields: ["emailAddress"])
@key(name: "gsi-BySubscriptionId", fields: ["subscriptionId"])
{
    id: ID!
    tenantId: String
    tenantName: String
    connectionId: String
    state: String
    refreshToken: String
    accessToken: String
    expirationDate: AWSDateTime
    startDate: AWSDateTime
    organisationId: String!  
    userId: String!  
    url: String
    remainingCallAmount: Int
    type: IntegrationService  
    status: IntegrationStatus  
    metadata: [IntegrationMetadata]
    allowedContactGroups: [String]
    createdAt: AWSDateTime
    updatedAt: AWSDateTime  
    # type === GMAIL or OUT_EMAIL
    authorityType: AuthorityType  
    emailAddress: String   # gmail address or outlook address
    historyId: Int # gmail historyId
    subscriptionId: String # outlook subscriptionId
    stripeMetadata: StripeMetadata # for stripe
    checkbookMetadata: CheckbookMetadata # for checkbook
}

type CheckbookMetadata {
    plaidRecordId: String
    plaidAccessToken: String
    cursor: String
    syncDate: AWSDateTime
}

type StripeMetadata {
    connectedAccountId: String
    rechargePercentage: Int # actual percentage * 10000
    maxCharge: Int # actual charge * 100
}

enum AuthorityType {
    SHARED
    PERSONAL
}

type Account
@model(queries: null, mutations: {update: "updateAccount"}, subscriptions: null)
@key(name: "gsi-ByAccountId", fields: ["accountId"])
{
    id: ID!
    accountId: ID!
    code: String
    balance: String
    name: String
    type: String
    rentancyLedgerCode: LedgerCode
    bankAccountNumber: String
    status: String
    description: String
    bankAccountType: String
    currency: String
    taxType: String
    enablePaymentsToAccount: Boolean
    showInExpenseClaims: Boolean
    hasAttachments: Boolean
    reportingCode: String
    reportingCodeName: String
    class: String
    systemAccount: String
    createdAt: AWSDateTime
    updatedAt: AWSDateTime
    organisation: Organisation! @connection(name: "OrganisationAccounts")
    transactions: [Transaction] @connection(name: "AccountTransactions", fields: ["accountId"])
    payments: [Payment] @connection(name: "AccountPayments", fields: ["accountId"])
    transfers: [Transfer] @connection(name: "AccountTransfers", fields: ["accountId"])
}

type Invoice
@model(queries: null, mutations: null, subscriptions: null)
@searchable(queries: {search: null})
@key(name: "gsi-ByInvoiceId", fields: ["invoiceId"])
{
    id: ID!
    invoiceId: ID!
    number: String
    reference: String
    type: InvoiceType
    status: String
    contactId: String
    contactFname: String
    contactSname: String
    contactCompanyName: String
    lineAmountTypes: String
    subTotal: String
    totalTax: String
    total: String
    amountDue: String
    amountPaid: String
    clientBalance: String
    totalDiscount: String
    amountCredited: String
    allocatedAmount: String
    unAllocatedAmount: String
    trackingOptionName: String
    allocationStatus: AllocationStatus
    currency: String
    sentToContact: Boolean
    hasAttachments: Boolean
    expectedPaymentDate: AWSDateTime
    plannedPaymentDate: AWSDateTime
    fullyPaidOnDate: AWSDateTime
    date: AWSDateTime
    dueDate: AWSDateTime
    createdAt: AWSDateTime
    updatedAt: AWSDateTime
    warnings: [String]
    balanceTransfer: Boolean
    floatSummary: LandlordBillFloatSummary
    approvedBy: [User] @function(name: "integrationService-${env}")
    user: User @function(name: "integrationService-${env}")
    organisation: Organisation! @connection(name: "OrganisationInvoices")
    lineItems: [InvoiceLineItem] @connection(name: "InvoiceLineItems")
    properties: [InvoiceProperty] @connection(name: "InvoiceProperties")
    tenancies: [InvoiceTenancy] @connection(name: "InvoiceTenancies")
    allocations: [InvoiceAllocation] @connection(name: "InvoiceAllocations")
}

type OverPayment
@model(queries: null, mutations: null, subscriptions: null)
@key(name: "gsi-ByOverPaymentId", fields: ["overPaymentId"])
@searchable
{
    id: ID!
    type: String
    reference: String
    remainingCredit: String
    contactId: String
    status: String
    lineAmountTypes: String
    subTotal: String
    totalTax: String
    total: String
    currency: String

    overPaymentId: String!
    organisation: Organisation! @connection(name: "OrganisationOverPayments")
}

type XeroJournal
@model(queries: null, mutations: null, subscriptions: null)
@searchable
@key(name: "gsi-JournalId", fields: ["journalId"])
{
    id: ID!
    journalId: ID!
    sourceId: String
    sourceType: String
    reference: String
    date: AWSDateTime
    createdDate: AWSDateTime
    createdAt: AWSDateTime
    updatedAt: AWSDateTime
    number: Int
    lines: [XeroJournalLine]
    organisation: Organisation! @connection(name: "OrganisationXeroJournals")
}

type XeroJournalLine {
    lineId: ID!
    accountId: ID!
    accountCode: String
    accountType: String
    accountName: String
    description: String
    netAmount: String
    grossAmount: String
    taxAmount: String
    taxType: String
    taxtName: String
    trackingCategories: [TrackingCategory]
}

type TrackingCategory {
    categoryId: String
    optionId: String
    name: String
}

type RentInvoiceHistory
@model(queries: null, mutations: null, subscriptions: null)
@key(name: "gsi-ByXeroId", fields: ["xeroId"])
{
    id: ID!
    xeroId: String
    periodFromDate: AWSDateTime
    periodEndDate: AWSDateTime
    type: RentHistoryType
    againstUser: User @connection(name: "RentInvoiceHistoryUser")
    tenancy: Tenancy @connection(name: "RentInvoiceHistoryTenancy")
    property: Property @connection(name: "RentInvoiceHistoryProperty")
    organisation: Organisation @connection(name: "RentInvoiceHistoryOrganisation")
    tenantId: String
    successful: Boolean
    message: String
}

enum RentHistoryType {
    TENANCY_INVOICE
    LANDLORD_COMMISSION
}

enum OrganisationType {
    LANDLORD
    AGENT
    OCCUPIER
}

type Statement
@searchable
@model(queries: null, mutations: null, subscriptions: null)
@key(name: "gsi-OrganisationStatements", fields: ["statementOrganisationId"])
@key(name: "gsi-LandlordBillStatements", fields: ["statementLandlordBillId"])
{
    id: ID!
    reference: String
    type: StatementType
    fileKey: String
    finalisedBy: String
    sent: Boolean
    payedOut: Boolean
    billsUpdated: Boolean
    sentBy: String
    payedOutBy: String
    billsUpdatedBy: String
    approved: Boolean
    from: AWSDateTime
    to: AWSDateTime
    sentDate: AWSDateTime
    payedOutDate: AWSDateTime
    billsUpdatedDate: AWSDateTime
    createdAt: AWSDateTime
    updatedAt: AWSDateTime
    statementOrganisationId: String
    statementLandlordBillId: String
    billsUpdateResult: [BillUpdateResult]
    property: Property @connection(name: "PropertyStatements")
    client: User! @connection(name: "ClientStatements")
}

enum StatementType {
    PROPERTY
    CLIENT
}

type BillUpdateResult {
    invoiceId: String
    status: String
    amountDue: String
    user: User
}

type InvoiceAllocation
@model(queries: null, mutations: {create: "createInvoiceAllocation", delete: "deleteInvoiceAllocation"}, subscriptions: null)
@key(name: "gsi-ByPropertyId", fields: ["invoiceAllocationPropertyId"])
{
    id: ID!
    amount: String
    description: String
    allocationDate: AWSDateTime
    createdAt: AWSDateTime
    updatedAt: AWSDateTime
    invoiceAllocationPropertyId: String
    invoice: Invoice @connection(name: "InvoiceAllocations")
    property: Property @connection(name: "InvoiceAllocationProperty")
    tenancy: Tenancy @connection(name: "InvoiceAllocationTenancy")
    organisation: Organisation! @connection(name: "OrganisationInvoiceAllocations")
}

type InvoiceProperty
@searchable(queries: {search: null})
@model(queries: null, mutations: null, subscriptions: null)
@key(name: "gsi-OrganisationInvoiceProperties", fields: ["invoicePropertyOrganisationId"])
{
    id: ID!
    invoicePropertyOrganisationId: String
    invoice: Invoice! @connection(name: "InvoiceProperties")
    property: Property! @connection(name: "PropertyInvoices")
}

type InvoiceTenancy
@model(queries: null, mutations: null, subscriptions: null)
{
    id: ID!
    invoice: Invoice! @connection(name: "InvoiceTenancies")
    tenancy: Tenancy! @connection(name: "TenancyInvoices")
}

type InvoiceLineItem
@searchable(queries: {search: null})
@model(queries: {get: "getInvoiceLineItem"}, mutations: {update: "updateInvoiceLineItem"}, subscriptions: null)
@key(name: "gsi-ByLineItemId", fields: ["lineItemId"])
@key(name: "gsi-ByTrackingName", fields: ["trackingName", "invoiceLineItemOrganisationId"])
@key(name: "gsi-OrganisationLineItems", fields: ["invoiceLineItemOrganisationId", "parentDate"])
{
    id: ID!
    lineItemId: String!
    description: String
    quantity: String
    unitAmount: String
    itemCode: String
    accountCode: String
    taxType: String
    taxAmount: String
    lineAmount: String
    discountRate: String
    trackingName: String
    discountAmount: String
    createdAt: AWSDateTime
    updatedAt: AWSDateTime
    invoiceTotal: String
    invoicePaidAmount: String

    parentAmountCredited: String
    parentTotal: String
    parentAmountDue: String
    parentPaidAmount: String
    parentDueDate: String
    parentReference: String
    parentStatus: String
    isIncome: Boolean
    isParentIncome: Boolean
    parentDate: String
    taxExclusive: Boolean
    balanceTransfer: Boolean

    invoiceLineItemOrganisationId: String!
    invoice: Invoice @connection(name: "InvoiceLineItems")
    transaction: Transaction @connection(name: "TransactionLineItems")
    documents: [Document] @connection(name: "LineItemDocuments")
}

type Transaction
@model(queries: null, mutations: null, subscriptions: null)
@key(name: "gsi-ByTransactionId", fields: ["transactionId"])
{
    id: ID!
    transactionId: String!
    prePaymentId: String
    overPaymentId: String
    batchPaymentId: String
    type: String
    contactId: String
    reconciled: Boolean
    hasAttachments: Boolean
    date: AWSDateTime
    reference: String
    currency: String
    currencyRate: String
    url: String
    status: String
    lineAmountTypes: String
    subTotal: String
    totalTax: String
    total: String
    createdAt: AWSDateTime
    updatedAt: AWSDateTime
    balanceTransfer: Boolean
    user: User @function(name: "integrationService-${env}")
    organisation: Organisation! @connection(name: "OrganisationTransactions")
    account: Account! @connection(name: "AccountTransactions")
    lineItems: [InvoiceLineItem] @connection(name: "TransactionLineItems")
    transfer: Transfer @connection(name: "TransferTransaction")
}

type Transfer
@model(queries: null, mutations: null, subscriptions: null)
@key(name: "gsi-ByTransferId", fields: ["transferId"])
{
    id: ID!
    transferId: String!
    amount: Int
    hasAttachments: Boolean
    date: AWSDateTime
    fromAccount: String
    fromTransaction: String
    createdAt: AWSDateTime
    updatedAt: AWSDateTime
    organisation: Organisation! @connection(name: "OrganisationTransfers")
    # Incoming transfers to this account
    account: Account @connection(name: "AccountTransfers")
    transaction: Transaction @connection(name: "TransferTransaction")
}

type Payment
@model(queries: null, mutations: null, subscriptions: null)
@key(name: "gsi-ByPaymentId", fields: ["paymentId"])
{
    id: ID!
    paymentId: String!
    invoiceId: String!
    batchPaymentId: String
    bankAmount: String
    amount: String
    reference: String
    currencyRate: String
    paymentType: String
    status: String
    hasAccount: Boolean
    reconciled: Boolean
    date: AWSDateTime
    createdAt: AWSDateTime
    updatedAt: AWSDateTime
    organisation: Organisation! @connection(name: "OrganisationPayments")
    account: Account! @connection(name: "AccountPayments")
    invoice: Invoice @function(name: "integrationService-${env}")
}

type IntegrationMetadata {
    retryCount: Int
    errorMessage: String
    lastUpdatedDate: AWSDateTime
    recourseType: ResourceType
}

type Board
@model(queries: {get: "getBoard"}, mutations: {update: "updateBoard"}, subscriptions: null)
{
    id: ID!
    referenceId: String
    name: String!
    parentType: ParentType
    parentId: String
    description: String
    taskCount: Int
    taskRefId: String
    columnCount: Int
    createdAt: AWSDateTime
    updatedAt: AWSDateTime
    boardOrganisationId: String
    organisation: Organisation @connection(keyName: "organisationId", name: "OrgranisationBoards")
    user: User @connection(keyName: "userId", name: "UserBoards")
    columns: [Column] @connection(name: "BaordColumns", sortField: "index")
    tasks(sortDirection: ModelSortDirection, limit: Int, filter: ModelTaskFilter): [Task] @function(name: "boardService-${env}")
}

type Column
@model(queries: {list: "listColumns"}, mutations: null, subscriptions: null)
{
    id: ID!
    index: Int
    name: String!
    description: String
    taskCount: Int
    columnBoardId: String
    columnOrganisationId: String
    board: Board @connection(name: "BaordColumns")
    tasks: [Task]
    organisation: Organisation @connection(name: "OrgranisationColumns")
}

type Task
@searchable
@key(name: "gsi-ColumnTasks", fields: ["taskColumnId", "index"])
@key(name: "gsi-BoardTasks", fields: ["taskBoardId", "index"])
@key(name: "gsi-ByParentIdAndParentType", fields: ["parentId", "parentType"])
@model(mutations: null, subscriptions: null)
{
    id: ID!  
    referenceId: String
    index: Int
    name: String  
    description: String  
    type: TaskType
    status: TaskStatus
    owner: String
    parentType: ParentType
    parentId: String
    parentName: String
    startDate: AWSDateTime
    endDate: AWSDateTime
    deadline: AWSDateTime
    createdAt: AWSDateTime
    updatedAt: AWSDateTime
    time: Boolean
    disabled: Boolean
    taskColumnId: String
    taskBoardId: String  
    taskOrganisationId: String
    shareToExternalCalendar: Boolean
    label: TaskLabel @connection(name: "TaskLabels")
    board: Board @function(name: "boardService-${env}")
    column: Column @function(name: "boardService-${env}")
    user: User @connection(name: "UserTasks")
    linkedContractor: User @function(name: "boardService-${env}")
    problemCard: ProblemCard @connection(name: "ProblemCardTasks")
    organisation: Organisation @connection(name: "OrgranisationTasks")
    checklists: [TaskChecklist] @connection(name: "taskChecklists")
    attachments: [Document] @connection(name: "taskAttachments")
    images: [Document] @function(name: "attachmentsService-${env}")
    property: Property @function(name: "boardService-${env}")
    parentPropertyEntity: ParentPropertyEntity @function(name: "boardService-${env}")
    comments: [TaskComment] @connection(name: "taskComment")
    worksOrders: [WorksOrder] @connection(name: "worksOrdersTask")
    metadata: ScheduleViewingMetadata # google calendar event and outlook calendar event
    note: String
}

type TaskScheduler
@searchable
@model(queries: {get: "getTaskScheduler"}, mutations: {create: "createTaskScheduler", delete: "deleteTaskScheduler", update: "updateTaskScheduler"}, subscriptions: null)
{
    id: ID!
    organisation: Organisation!  @connection(name: "organisationTaskSchedulers")
    active: Boolean!
    title: String!
    property: Property!  @connection(name: "propertyTaskSchedulers")
    label: TaskLabel @connection(name: "SchedulerTaskLabels")
    frequency:  FrequencyItem!
    repeatForever: Boolean!
    lastCreatedAt: AWSDateTime!
    startDate: AWSDateTime
    endDate: AWSDateTime
    assignedTo: [ID]
}

type AutomatedTasks
@searchable
@model(queries: {list: null}, mutations: {update: "updateAutomatedTasks"}, subscriptions: null)
{
    id: ID!
    organisation: Organisation!  @connection(name: "OrganisationAutomatedTasks")
    label: TaskLabel @connection(name: "LabelAutomatedTasks")
    eventName: String
    eventValue: String
    assignee: ID
    description: String
    active: Boolean
    createdAt: AWSDateTime
    updatedAt: AWSDateTime
}

type FrequencyItem {
    count: Int!
    frequencyPeriod: taskPeriod!
}
enum taskPeriod {
    DAYS
    WEEKS
    MONTHS
    YEARS
}

type WorksOrder
@model(queries: null, subscriptions: null)
{
    id: ID!
    billingName: WorksOrderUserType
    supplierContact: User @connection(name: "workOrderUser")
    task: Task @connection(name: "worksOrdersTask")
    organisation: Organisation! @connection(name: "OrganisationWorkOrders")
    reportedBy: User @connection(name: "workOrderReporterUser")
    priority: String
    quote: Int
    maximumCost: Int
    actualCost: Int
    description: String
    accessthrought: WorksOrderUserType
    startDate: AWSDateTime
    mainContact: WorksOrderUserType
    clientApprovalRequired: Boolean
    clientApprove: Boolean
    clientAcceptanceStatus: ClientWorksOrderAcceptanceStatus
    workOrderOpen: Boolean
    approvedUserNmame: String
    updatedAt: AWSDateTime
    createdAt: AWSDateTime
    approveTime: AWSDateTime
    reference: String
    rejectReason: String
    approvalStatus: WorksOrderApprovalStatus
}

enum WorksOrderUserType{
    LANDLORD
    AGENT
    TENANT
}

enum WorksOrderApprovalStatus {
    APPROVED
    DECLINED
}

type TaskComment
@model(queries: null, mutations: {create: "createTaskComment"}, subscriptions: null)
{
    id: ID!
    user: User @connection(name: "UserTaskComments")
    task: Task @connection(name: "taskComment")
    content: String!
    lastUpdatedAt: AWSDateTime
}

type ContactComment
@model(mutations: { create: "createContactComment"}, queries: null, subscriptions: null)
{
    id: ID!
    userContact: User! @connection(name: "UserContactComments")
    content: String!
    user: User @function(name: "userService-${env}")
    userAuthorId: String!
    contactCommentOrganisationId: String!
    organisation: Organisation! @connection(name: "OrganisationContactComments")
    lastUpdatedAt: AWSDateTime
}

type TaskLabel
@model(queries: {get: "getTaskLabel"}, mutations: {create: "createTaskLabel", update: "updateTaskLabel", delete: null}, subscriptions: null)
{
    id: ID!
    name: String
    iconName: String
    createdAt: AWSDateTime
    updatedAt: AWSDateTime
    organisationTaskChecklistIds: [ID]
    index: Int
    isDefault: Boolean
    scheduledTasks: [TaskScheduler] @connection(name: "SchedulerTaskLabels")
    automatedTasks: [AutomatedTasks] @connection(name: "LabelAutomatedTasks")
    tasks: [Task] @connection(name: "TaskLabels")
    organisation: Organisation! @connection(name: "OrganisationTaskLabels")
}

type EmailMessage
@key(name: "gsi-ByThreadId", fields: ["externalThreadId"])
@key(name: "gsi-ByMessageId", fields: ["externalMessageId"])
@model(queries: null, mutations: null, subscriptions: null)
{
    id: ID!
    organisation: Organisation!  @connection(name: "OrganisationEmails")
    parentId: String
    parentType: ParentType
    to: String
    cc: String
    bcc: String
    toUserIds: [ID]
    ccUserIds: [ID]
    bccUserIds: [ID]
    fromUserId: ID
    from: String
    subject: String
    body: String
    status: Int
    reply: Boolean
    attachments: [EmailAttachment] @connection(name: "EmailAttachments")
    # Newly added atrributes for the conversation
    emailMessageOrganisationId: ID
    emailMessageConversationId: ID!
    conversation: Conversation @connection(name: "ConvoEmails")
    deleted: Boolean
    createdAt: String
    updatedAt: String
    externalMessageId: String # messageId in gmail or outlook
    externalThreadId: String # email threadId, used for judging whether it is a reply to an existing email
}

type EmailAttachment
@model(queries: null, mutations: {create: "createEmailAttachment"}, subscriptions: null)
@key(name: "gsi-ByEmailAttachmentEmailMessageId", fields: ["emailAttachmentEmailMessageId"])
{
    id: ID!
    document: Document @connection(name: "EmailDocument")
    emailAttachmentDocumentId: ID
    emailMessage: EmailMessage @connection(name: "EmailAttachments")
    emailAttachmentEmailMessageId: ID
    createdAt: AWSDateTime
    updatedAt: AWSDateTime
}

enum ScheduledEmailStatus {
    SCHEDULED,
    SENT,
    BLOCKED
}


type ScheduledEmail
@key(name: "gsi-ByScheduleAt", fields: ["status", "scheduleAt"])
@key(name: "gsi-ByOrganisationId", fields: ["organisationId"])
@model(queries: {list: "listScheduledEmails"}, mutations: {create: "createScheduledEmail", update: "updateScheduledEmail", delete: "deleteScheduledEmail"}, subscriptions: null)
{
    id: ID!
    organisationId: ID
    conversationId: ID
    to: String
    cc: String
    bcc: String
    toUserIds: [ID]
    ccUserIds: [ID]
    fromUserId: ID
    from: String
    subject: String
    body: String
    attachmentIds: [String!]
    emailProvider: IntegrationService # only support Google gmail, Microsoft outlook email
    status: ScheduledEmailStatus
    attachments: [EmailAttachment]
    scheduleAt: AWSDateTime
    deleted: Boolean
    createdAt: String
}

type Permissions
@model(queries: null, mutations: null, subscriptions: null)
 {
    id: ID!
    itemType: PermissionItemType
    groups: [String!]!
    document: Document @connection(name: "DocumentPermissions")
}

type Invitation
@model(queries: null, mutations: null, subscriptions: null)
@key(name: "gsi-ByEmail", fields: ["email"])
{
    id: ID!
    email: AWSEmail!
    user: User @connection(name: "UserInvitation")
    tenancyId: String
    organisation: Organisation @connection(name: "OrganisationInvitations")
    role: String
    inviterId: String!
    inviterName: String!
    verified: Boolean
    token: String
    createdAt: AWSDateTime
    updatedAt: AWSDateTime
}

type Session
@model(queries: {get: "getSession"}, mutations: {create: "createSession", update: "updateSession"}, subscriptions: null)
{
    id: ID!
    updatedDate: String
    status: Boolean
}

type Activity
@model(subscriptions: null, queries: null, mutations: null)
@searchable
{
    id: ID!
    updatedDate: String!
    type: ActivityType!
    action: String!
    link: String!
    parentId: String
    parentType: ParentType
    lastAction: String
    image: String
    title: String
    subTitle: String
    status: String
    readStatus: ActivityStatus
    resolveStatus: ActivityStatus
    memberStatus: ActivityStatus
    sentStatus: ActivityStatus
    pushStatus: ActivityStatus
    onDemand: Boolean
    ignoreUpdate: Boolean
    # Set for CHAT activity only(Used to get user status, need to be removed later)
    associatedUserId: String
    user: User @connection(name: "UserActivities")
    activityUserId: ID
    organisation: Organisation @connection(name: "OrganisationActivities")
    activityOrganisationId: ID
    metadata: ActivityMetadata
    authorityType: AuthorityType # Subscribe to shared emails
}

type File
@model(queries: null, mutations: {create: "createFile", update: "updateFile"}, subscriptions: null)
@key(name: "gsi-ByUserId", fields: ["fileUserId"])
{
    id: ID!
    key: String!
    description: String
    mimeType: String!
    owner: String
    fileUserId: String
    user: User! @connection(name: "UserProfile")
    nonAutoSyncTrigger: Boolean # true: when update the data, it won't trigger auto sync to Lofty.
}

type Conversation
@model(
    mutations: { create: "createConvo", update: "updateConvo"}
    queries: { get: "getConvo", list: "listConvo" }
    subscriptions: null
)
@key(name: "gsi-ByParentIdParentType", fields: ["parentId", "parentType"])
@key(name: "gsi-ByPropertyIdAndDate", fields: ["conversationPropertyId", "createdAt"])
@searchable
{
    id: ID!  
    messages: [Message] @connection(name: "ConvoMsgs", sortField: "createdAt")  
    emails: [EmailMessage] @connection(name: "ConvoEmails", sortField: "createdAt")  
    associated: [ConvoLink] @connection(name: "AssociatedLinks")
    documents(filter: ModelDocumentFilterInput, sortDirection: ModelSortDirection, limit: Int, nextToken: String): ModelDocumentConnection @function(name: "attachmentsService-${env}")
    name: String!
    members: [String!]!
    mutator: String
    lastMessage: String
    uniqueNumber: Int
    internalNotes: String
    createdAt: String
    updatedAt: String
    # TODO this should be enum
    type: String
    parentType: ParentType
    parentId: String
    resolved: Boolean
    conversationOrganisationId: String
    conversationSourceContactId: String
    conversationAssigneeId: String
    conversationPropertyId: String
    conversationTaskId: String
    conversationProblemCardId: String
    ignoreUpdate: Boolean
    organisation: Organisation @connection(name: "OrganisationConversations")
    conversationSourceContact: User @function(name: "attachmentsService-${env}")
    conversationAssignee: User @function(name: "attachmentsService-${env}")
    conversationProperty: Property @function(name: "attachmentsService-${env}")
    conversationTask: Task @function(name: "attachmentsService-${env}")
    conversationProblemCard: ProblemCard @function(name: "attachmentsService-${env}")

    # New attributes for conversation modules
    contactType: ContactType  # TENANT,LANDLORD
    lastVisitedTimes: [ConversationLastVisitedTime]
    conversationCategory: ConversationCategory  # SHARED,PERSONAL,ARCHIVED
    oldConversationCategory: ConversationCategory  # SHARED,PERSONAL
    subject: String
    readMembers: [String]
    deletedMembers: [String]
    conversationType: ConversationType  # SMS,EMAIL,INTERNAL(CHAT/GROUP CHAT),PORTAL(CHANNEL CHAT),WHATSAPP
    createdBy: String
    updatedBy: String
    selectedTagIds: [ID]
    selectedTagNames: [String] @function(name: "conversationService-${env}")
    visibleTimes: [ConversationVisibleTime]
    lastMessageTime: String
    newInternalNotes: [ConversationInternalNote]
    parentPropertyIds: [ID!]
    parentProperties: [ParentPropertyEntity!] @function(name: "conversationService-${env}")
    propertyIds: [ID!]
    properties: [Property!] @function(name: "conversationService-${env}")
    taskIds: [ID!]
    tasks: [Task!] @function(name: "conversationService-${env}")
    mainEmailAddress: String # used for separate different share or personal email accounts
}

type ConversationLastVisitedTime {
    userId: String
    lastVisitedTime: String
}

enum ContactType{
    TENANT
    LANDLORD
}

enum ConversationCategory{
    SHARED
    PERSONAL
    ARCHIVED
}

enum ConversationType{
    SMS
    EMAIL
    INTERNAL
    PORTAL
    WHATSAPP
}

type ConversationVisibleTime {
    userId: String
    visibleTime: String
}

type ConversationInternalNote {
    id: ID
    userId: String
    authorName: String
    content: String
    editTime: String
}

# TODO add permission on members
type Message
@model(subscriptions: null, queries: null, mutations: {create: "createMessage", update: "updateMessage"})
@key(name: "gsi-ByWhatsAppId", fields: ["whatsAppId"])
{
    id: ID!
    whatsAppId: ID
    author: User @connection(name: "UserMessages", keyField: "authorId")
    organisation: Organisation @connection(name: "OrganisationMessages")
    messageOrganisationId: ID
    authorId: String
    # If message type is PROBLEM_CARD content must be ProblemCard ID
    content: String!
    conversation: Conversation @connection(name: "ConvoMsgs")
    messageConversationId: ID!
    status: String
    type: MessageType
    deleted: Boolean
    createdAt: String
    updatedAt: String
}

type TypingStatusMessage
@model(queries: null, subscriptions: null, mutations: {create: "createTypingStatusMessage"})
{
    id: ID!
    messageConversationId: String!
    authorId: String
    author: String
    status: String
}

type ConvoLink
@model(
    mutations: { create: "createConvoLink", update: "updateConvoLink", delete: "deleteConvoLink"}
    queries: null
    subscriptions: null
)
@key(name: "gsi-userConversation", fields: ["convoLinkUserId", "convoLinkConversationId"])
{
    id: ID!
    user: User @connection(name: "UserLinks")
    organisation: Organisation @connection(name: "OrganisationLinks")
    convoLinkOrganisationId: ID!
    convoLinkUserId: ID
    conversation: Conversation @connection(name: "AssociatedLinks")
    convoLinkConversationId: ID!
    createdAt: String
    updatedAt: String
}

enum LeaseTerm {
    SHORT_TERM
    LONG_TERM
    EITHER
    # LeaseTerm for US
    MONTHLY
    HALF_YEARLY
    YEARLY
    CUSTOM
}

type TemplateLetter
@model(queries: {get: "getTemplateLetter"}, subscriptions: null)
@searchable
{
    id: ID!
    letterName: String!
    contactType: UserType!
    category: String!
    letterSubject: String
    letterContent: String
    organisation: Organisation! @connection(name: "OrganisationTemplateLetters")
}

type LandlordPropertySettings {
    landlordUserId: ID
    targetFloat: Float
}

enum PropertyType {
    OFFICE_SPACE
    OFFICE_BUILDING
    RENTAL_UNIT
    UNIT
    SHOP
    INDUSTRIAL_UNIT
    CAR_PARK
    MAISONETTE
    MANSION
    BUNGALOW
    COTTAGE
    TERRACED
    MEWS
    END_OF_TERRACE
    DETACHED
    SEMI_DETACHED
    APARTMENT
    FLAT
    GARAGE
    FARM
    FLATS
    ROOMS
    ROOM
    APARTMENT_BLOCK
    RESIDENTIAL_BLOCK
    GARAGE_BLOCK
    CONDO
    HISTORIC_HOUSE
    CHURCH
    HOUSE
    GROUNDS
    GARDEN
    STABLES
    DEVELOPMENT
    SCHOOL
    TEACHING_BLOCK
    SPORTS_GROUND
    GYM
    LIBRARY
    UNION
    HEALTH_CENTER
    SWIMMING_POOL
    MAINTENANCE
    HANGER
    CONTROL_TOWER
    TERMINAL_BUILDING
    FIRE_STATION
    APRON
    TAXIWAY
    RUNWAY
    FUEL_STATION
    WORKSHOP
    OFFICE
    CONTAINER
    YARD
    PARKING
    STORAGE
    RETAIL
}

type Utility {
    name: String!
    supplierId: String
    metric: Float
}

type PropertyInventoryItem {
    name: String!
    brand: String
    model: String
    serialNumber: String
    purchaseDate: AWSDateTime
    note: String
}

enum PropertyMimimumTenancyTerm {
    THREE_MONTHS
    SIX_MONTHS
    NINE_MONTHS
    YEAR
    ROLLING
}

type Property
@model(queries: {get: "getProperty"}, subscriptions: null, mutations: {update: "updateProperty", delete: "deleteProperty"})
@key(name: "gsi-ByParentId", fields: ["parentId"], queryField: "listPropertyByParentId")
@key(name: "gsi-ByReference", fields: ["reference", "propertyOrganisationId"])
@searchable
{
    id: ID!
    archived: Boolean
    addressLine1: String!
    addressLine2: String
    addressLine3: String
    reference: String
    type: PropertyType
    zipCode: String
    fullBathrooms: Int
    listingType: String
    councilTax: String
    epc: String
    internetConnection: String
    internalNotes: String
    keys: String
    alarm: String
    parking: String
    phone: AWSPhone
    city: String
    postcode: String
    state: String
    country: String
    status: PropertyStatus
    keyFeatures: [String]
    detailedDescription: String
    description: String
    summary: String
    coverImage: String
    images: [String]
    source: String
    minimumBalance: Float
    createdAt: AWSDateTime
    updatedAt: AWSDateTime
    primaryLandlordId: String
    latitude: Float
    longitude: Float
    marketedLatitude: Float
    rightmoveDetails: RightmoveProperty
    zooplaDetails: ZooplaProperty
    marketedLongitude: Float
    splitOwnershipEnabled: Boolean
    certificateNumber: String
    certificateExpirationDate: AWSDateTime
    propertyParentPropertyEntityId: String
    transUnionPropertyId: String
    summaryPageFinanceDetails: PropertySummaryFinanceDetails @function(name: "integrationService-${env}")
    parentPropertyEntity: ParentPropertyEntity @connection(name: "ParentPropertyEntityProperty")
    primaryLandlord: User @function(name: "usersService-${env}")
    organisation: Organisation @connection(name: "OrganisationProperties")
    taskSchedulers: [TaskScheduler] @connection(name: "propertyTaskSchedulers")
    propertyOrganisationId: ID!
    owner: User! @connection(name: "PropertyOwner")
    landlords: [User!] @function(name: "usersService-${env}")
    landlordPropertySettings: LandlordPropertySettings
    managers: [User!] @function(name: "usersService-${env}")
    tenancies: [Tenancy] @connection(name: "PropertyTenancies")
    problemCards(sortDirection: ModelSortDirection): ModelPropertyProblemReportsConnection @function(name: "tenancyService-${env}")
    invoices: [InvoiceProperty] @connection(name: "PropertyInvoices")
    invoiceAllocation: InvoiceAllocation @connection(name: "InvoiceAllocationProperty")
    documents(filter: ModelDocumentFilterInput, sortDirection: ModelSortDirection, limit: Int, nextToken: String): ModelDocumentConnection @function(name: "attachmentsService-${env}")
    invoiceHistory: [RentInvoiceHistory] @connection(name: "RentInvoiceHistoryProperty")
    propertyBudgets: [PropertyBudget] @connection(name: "PropertyBudget")
    portfolioHistory: [PortfolioHistory] @connection(name: "PropertyPortfolioHistory")
    statements: [Statement] @connection(name: "PropertyStatements")
    conversations(filter: String): [Conversation] @function(name: "propertyService-${env}")
    openingBalance: Int
    docusignTemplateSend: [DocusignSendDocumentEnvelopes] @connection(name: "DocusignTemplateSendProperty")

    # Following fields are for property and units in US

    # If this is a property and has units, set to true. for property in EU without unit, set to falses
    hasUnit: Boolean
    # Set to parent property id for units, nil for property.
    parentId: ID
    createdBy: ID
    updateBy: ID
    name: String
    # key features
    bedRooms: Int
    bathRooms: Int
    fullBaths: Int
    #only for US
    halfBaths: Int
    # For UK it's square meters
    sqft: Float
    sqmt: Int
    agents: [User] @function(name: "usersService-${env}")
    utilities: [Utility]
    amenities: [String]
    inventory: [PropertyInventoryItem]
    floorPlans: [String]
    videos: [String]
    councilTaxBand: String
    taxEPCRating: String
    yearBuilt: Int
    doneSteps: [String]
    allowPets: [String]
    startDate: AWSDateTime
    leaseTerm: LeaseTerm
    furnishedStatus: FurnishedType
    duration: Int
    monthlyRent: Float
    rentPeriod: Period
    securityDeposit: Int
    insuranceRequired: String
    petsAllowed: String
    otherRules: String
    agentIds: [String]
    applicationFee: Float
    questions: [String]
    customAmenities: [String]
    zillowPublishStatus: Boolean
    hotpadsUrl: String
    hotpadsComments: String
    rightmovePublishStatus: Boolean
    zooplaPublishStatus: Boolean
    # listing status in zillow, for zillow integration
    listingStatus: ListingStatus

    applications: [PropertyApplication] @connection(name: "PropertyApplications")
    unitApplications: [PropertyApplication] @connection(name: "UnitApplications")

    tours: [PropertyTour] @connection(name: "PropertyTours")
    smokingAllowed: String
    activeUnitsCount: Int
    customType: String
    propertyInvestigationReference: InvestigationReference @connection(name: "InvestigationReferenceProperty")
    loftyMetadata: LoftyMetadata
    nonAutoSyncTrigger: Boolean # true: when update the data, it won't trigger auto sync to Lofty.

    assets: [PropertyAsset] @connection(name: "PropertyAssets")
    supplierProperty: [SupplierProperty] @connection(keyName: "gsi-ByProperty", fields: ["id"])
    rentGuarantee: Boolean
    minimumTenancyTerm: PropertyMimimumTenancyTerm
}

type PropertySummaryFinanceDetails {
    arrearsBalance: Float
    propertyBalance: Float
    totalBalance: Float
    nextRentDueDate: AWSDateTime
}

type LoftyMetadata {
    mlsOrgId: String
    videoLinks: [String]
    listingId: String
    syncDisabled: Boolean
}

enum ParentPropertyPropertiesSortField {
    PROPERTY_ADDRESS
    PROPERTY_TYPE
    PROPERTY_STATUS
}

enum FurnishedType {
    FULLY_FURNISHED
    NOT_FURNISHED
    PART_FURNISHED
    FURNISHED_OPTIONAL
}

type ParentPropertyEntity
@model(mutations: null, subscriptions: null)
@key(name: "gsi-ByReference", fields: ["reference", "parentPropertyEntityOrganisationId"])
@searchable
{
    id: ID!
    name: String!
    archived: Boolean!
    type: ParentPropertyEntityType!
    reference: String!
    addressLine1: String!
    city: String
    postcode: String
    country: String!
    state: String
    notes: String
    splitOwnershipEnabled: Boolean
    primaryLandlordId: String
    properties: [Property]! @connection(name: "ParentPropertyEntityProperty")
    zillowPublishProperties: [String]
    hotpadsUrl: String
    hotpadsComments: String
    landlords: [User!]! @function(name: "usersService-${env}")
    managers: [User!]! @function(name: "usersService-${env}")
    documents(filter: ModelDocumentFilterInput, sortDirection: ModelSortDirection, limit: Int, nextToken: String): ModelDocumentConnection @function(name: "attachmentsService-${env}")
    childPropertyAggregates: ParentPropertyChildAggregates! @function(name: "propertyService-${env}")

    parentPropertyEntityOrganisationId: ID!
    organisation: Organisation! @connection(name: "ParentPropertyEntityOrganisation")
    createdBy: ID!
    updatedAt: AWSDateTime
    createdAt: AWSDateTime!
    images: [String]
    propertyCount: Int
}

type ParentPropertyChildAggregates {
    totalChildProperties: Int!
    totalBedrooms: Int!
    totalBathrooms: Int!
    totalHalfBathrooms: Int!
    totalFullBathrooms: Int!
    # For UK this is square meters
    totalSqft: Int!
    totalRent: Int!
}

enum ParentPropertyEntityType {
    # UK
    HMO
    APARTMENT_BLOCK
    BUSINESS_PARK
    BLOCK
    OFFICE

    # US
    MULTI_FAMILY_HOMES
    DUPLEX
    APARTMENT_COMMUNITY
    APARTMENT_BUILDING
    CONDOS
}

type PropertyApplication
@model(queries: {get: "getPropertyApplication", list: "listPropertyApplications"},
    mutations: {update: "updatePropertyApplication", delete: "deletePropertyApplication"},
    subscriptions: null)
@searchable
@key(name: "gsi-ByReference", fields: ["reference", "organisationId"])
@key(name: "gsi-ByOrganisation", fields: ["organisationId"])
{
    id: ID!
    organisationId: ID!
    property: Property @connection(name: "PropertyApplications")
    unit: Property @connection(name: "UnitApplications")
    contact: User @connection(name: "UserApplications")
    tenant: User @connection(name: "TenantApplications")
    name: String
    phone: String
    email: String
    questionAnswer: ApplicationQuestionAnswer @connection(name: "ApplicationAnswer")
    expectedMoveIn: AWSDateTime
    applyType: PropertyApplicationApplyType
    moveInType: PropertyApplicationMoveInType
    status: PropertyApplicationStatus
    submittedAt: AWSDateTime
    reference: String
    assignedTo: User @connection(name: "UserApplicationAssign")
    specifyFees: String
    additionalMessage: String
    createdAt: AWSDateTime
    updatedAt: AWSDateTime
    createdBy: ID
    updateBy: ID
}

enum PropertyApplicationStatus {
    SENT
    SUBMITTED
    APPROVED
    REJECTED
    IGNORE
}

enum PropertyApplicationApplyType {
    TENANT
    COSIGNER
    PROSPECT
}

enum PropertyApplicationMoveInType {
    ALONE
    COAPPLICANTS
}

type ApplicationQuestionSetting
@model(queries: null, mutations: {create: "createApplicationQuestionSetting", update: "updateApplicationQuestionSetting"}, subscriptions: null)
@key(name: "settingByOrganisationId", fields: ["organisationId"], queryField: "settingByOrganisationId")
{
    id: ID!
    organisationId: ID!
    organisation: Organisation! @connection(name: "ApplicationSetting")
    questionEnables: [Int]
    questionOptionals: [Int]
    questionSettingFiles: [QuestionSettingFile]
    generalInstructions: String
}

type QuestionSettingFile{
    filename: String
    optional: Boolean
}

type RightmoveProperty {
    rightmoveId: String
    rightmoveUrl: String
}

type ZooplaProperty {
    listingLastUpdatedAt: String!
    zooplaUrl: String!
    zooplaStatus: String!
}

type ApplicationQuestionAnswer
@model(queries: null, mutations: null, subscriptions: null)
{
    id: ID!
    propertyApplication: PropertyApplication @connection(name: "ApplicationAnswer")

    questionnaireAnswers: [QuestionnaireAnswer]
}
type QuestionnaireAnswer{
    questionaireId: String
    answer: String
}

type Questionnaire
@model(queries: null, mutations: null, subscriptions: null)
{
    id: ID!
    description: String
    type: QuestionnaireType
}

enum QuestionnaireType {
    ApplicationQuestionnaire
}

type PropertyTour
@model(queries: {get: "getPropertyTour", list: "listPropertyTours"}, subscriptions: null) 
{
    id: ID!
    organisationId: ID!
    property: Property @connection(name: "PropertyTours")
    name: String
    phone: String
    email: String
    availableDates: [AWSDateTime]
    additionalMessage: String
    agentId: ID!
    agentName: String
    createdAt: AWSDateTime
    updatedAt: AWSDateTime
    createdBy: ID
    updateBy: ID
}

enum ListingStatus {
    NOT_LISTED
    IN_REVIEW
    PUBLISHING
    LISTED
    POST_FAILED
    LOGIN_FAILED
}

type LandlordBill
@model(queries: null, mutations: {update: "updateLandlordBill"}, subscriptions: null)
@key(name: "gsi-ByReference", fields: ["landlordBillOrganisationId"])
@key(name: "gsi-ByOriginalInvoiceId", fields: ["originalInvoiceId"])
@searchable
{
    id: ID!
    eventId: String!
    datePaid: AWSDateTime
    originalAmount: Int!
    propertyAddress: String!
    tenancyReference: String!
    billAmount: Int!
    approved: Boolean!
    archived: Boolean
    approvedBy: String
    dateRaised: AWSDateTime
    status: LandlordBillStatus!
    landlordBillOrganisationId: ID!
    originalInvoiceId: String
    landlordId: ID
    invoiceId: String
    propertyId: String
}

type InvoiceWebhookEvents
@model(queries: null, mutations: null, subscriptions: null)
@key(name: "gsi-ByOrganisation", fields: ["invoiceWebhookEventsOrganisationId"])
{
    id: ID!
    createdAt: AWSDateTime
    invoiceId: String
    data: String
    invoiceWebhookEventsOrganisationId: String
}

enum LandlordBillStatus {
    NEW
    COMPLETED
    SKIPPED
}

type PropertyBudget
@model(queries: null, mutations: {create: "createPropertyBudget", delete: "deletePropertyBudget"}, subscriptions: null)
{
    id: ID!
    property: Property! @connection(name: "PropertyBudget")
    name: String!
    # used for ordering on the frontend. 1, 2, 3, 4, 5...
    budgetNumber: Int!
    referenceNumber: String!
    references: String!
}

type ModelOrganisationProblemReportsConection {
    items: [ProblemCard]
    nextToken: String
}

type ModelPropertyProblemReportsConnection {
    items: [ProblemCard]
    nextToken: String
}


type Business
@model(queries: null, mutations: {create: "createBusiness", update: "updateBusiness"}, subscriptions: null)
{
    id: ID!
    name: String!
    addressLine1: String
    addressLine2: String
    addressLine3: String
    city: String
    postcode: String
    state: String
    country: String
    phone: [AWSPhone]
    website: AWSURL
    public: Boolean!
    type: BusinessType!
    employees: [User] @connection(name: "UserBusiness")
}


type AddtionalCost{
    title: String
    cost: String
}

type Tenancy
@model(queries: {get: "getTenancy"}, mutations: null)
@key(name: "gsi-ByReference", fields: ["reference", "tenancyOrganisationId"])
@searchable
{
    id: ID!
    number: String
    address: String
    title: String
    period: Period
    reference: String
    invoiceStartDate: AWSDateTime
    startDate: AWSDateTime
    endDate: AWSDateTime
    expectedExchangeDate: AWSDateTime
    rentReviewDate: AWSDateTime
    reviewNotice: AWSDateTime
    breakClauseDate: AWSDateTime
    rentReview: AWSDateTime
    renewalDate: AWSDateTime
    paymentDay: Int
    deposit: Int
    vouchApplication: [applicationItem]
    applicants: [String]
    rent: Int
    isRentGuaranteeRequired: Boolean
    transferredDate: AWSDateTime
    refundedDate: AWSDateTime
    reservation: Int
    rentIncreaseAmount: Int
    tenantBreakClauseMonths: Int
    landlordBreakClauseMonths: Int
    askingPrice: Int
    agreedPrice: Int
    saleAdminFee: Int
    rentNote: String
    rentIncreaseDate: String
    accounting: Boolean
    landlordVat: Boolean
    autoInvoiceRent: Boolean
    addLandlordDetails: Boolean
    attachInvoicesToContract: Boolean
    shareInvoicesToContract: Boolean
    shareInvoicesWithLandlord: Boolean
    depositReference: String
    tags: [String]
    type: TenancyType

    certificateNumber: String
    depositProtectionScheme: String
    dateDepositRegistered: AWSDateTime
    depositReturned: Boolean
    depositReleased: Boolean
    depositRegistered: Boolean
    depositTransferred: Boolean
    depositDisputed: Boolean
    depositSelectedScheme: String
    depositStatus: DepositStatus

    currency: String! @function(name: "propertyService-${env}")
    agent: User @function(name: "usersService-${env}")
    mutator: String
    primaryTenant: User @function(name: "usersService-${env}")
    tenants: [User] @function(name: "usersService-${env}")
    teamMembers: [User] @function(name: "usersService-${env}")
    landlords: [User] @function(name: "usersService-${env}")
    guarantors: [User] @function(name: "usersService-${env}")
    tenancyChannel: Conversation @function(name: "usersService-${env}")
    settings: TenancySettings @function(name: "tenancyService-${env}")
    openingBalance: Int
    status: TenancyStatus!
    createdAt: AWSDateTime
    updatedAt: AWSDateTime
    archived: Boolean
    lastAction: String
    area: Int
    tenancyOrganisationId: String
    dontCollectRent: Boolean
    enableJournal: Boolean
    enableProRataJournal: Boolean
    breakClauseItems: [BreakClauseItem]
    property: Property  @connection(name: "PropertyTenancies")
    organisation: Organisation @connection(name: "OrganisationTenancies")
    invoices: [InvoiceTenancy] @connection(name: "TenancyInvoices")
    invoiceAllocation: InvoiceAllocation @connection(name: "InvoiceAllocationTenancy")
    documents(filter: ModelDocumentFilterInput, sortDirection: ModelSortDirection, limit: Int, nextToken: String): ModelDocumentConnection @function(name: "attachmentsService-${env}")
    rentInvoiceHistory: [RentInvoiceHistory] @connection(name: "RentInvoiceHistoryTenancy", sortField: "periodFromDate")
    portfolioHistory: [PortfolioHistory] @connection(name: "TenancyPortfolioHistory")
    docusignTemplateSend: [DocusignSendDocumentEnvelopes] @connection(name: "DocusignTemplateSendTenancy")

    tenancyPropertyAssets: [PropertyAsset] @connection(name: "TenancyPropertyAssets")

    rentFrequencies: Period
    permittedOccupiers: [User] @function(name: "usersService-${env}")
    addtionalCosts: [AddtionalCost]

    moveOutDate: AWSDateTime
    utilities: [Utility]
    outstandingPayments: Int

    automaticAllocationOfTenantFundsToRent: Boolean
    automaticRentInvoice: Boolean
    managementFeeEnabled: Boolean
    managementFeeFixed: Int
    managementFeePercentage: Int
    managementFeeStrategy: String
    managementFeeTrigger: String
    propertyManagerId: String

    reminderDays: Int
    envelopeId: String
}

type ExpiringTenancies
@model
@key(name: "gsi-ByTenancyId", fields: ["tenancyId"])
@key(name: "gsi-ByOrganisation", fields: ["organisationId"])
{
    id: ID!
    tenancyId: ID!
    endDate: AWSDateTime
    organisationId: ID!
    status: ExpiringTenancyStatus
    landlordReviewUrl: String
}

enum ExpiringTenancyStatus {
    WAITING_TO_START,
    AWAITING_LANDLORD_RESPONSE,
    AWAITING_PM_RESPONSE,
    AWAITING_TENANT_RESPONSE,
}

type InvestigationReference
@searchable
@model(queries: {get: "getInvestigationReference"}, subscriptions: null)
@key(name: "gsi-ByOrganisation", fields: ["organisationId"])
@key(name: "gsi-ByScreeningRequestRenter", fields: ["screeningRequestRenterId"])
{
    id: ID!
    organisationId: ID!
    screeningRequestRenterId: Int
    applicant: User! @connection(name: "InvestigationReferenceApplicants")
    guarantor: User @connection(name: "InvestigationReferenceGuarantor")
    property: Property @connection(name: "InvestigationReferenceProperty")
    term: Period
    startDate: AWSDateTime
    endDate: AWSDateTime
    deposit: Int
    rent: Int
    providerName: String
    type: InvestigationReferenceType!
    status: InvestigationReferenceStatus!
    vouchApplication: applicationItem
    reportKeys: [String]
    inProgressFor: String
    documents: [String]
    createdBy: ID
    updatedBy: ID
    createdAt: AWSDateTime
    updatedAt: AWSDateTime
    applicationId: String
}

enum InvestigationReferenceType {
    VOUCH
    TRANSUNION
    OTHERS
}

enum InvestigationReferenceStatus {
    DRAFT
    SENT
    IN_PROGRESS
    COMPLETED
    RETIRED
    ACCEPTED
    REJECTED
    CANCELLED
}

type PortfolioHistory
@model(mutations: null, subscriptions: null, queries: {get: "getPortfolioHistory"})
{
    id: ID!
    user: User @connection(name: "UserPortfolioHistory")
    property: Property @connection(name: "PropertyPortfolioHistory")
    tenancy: Tenancy @connection(name: "TenancyPortfolioHistory")
    organisation: Organisation @connection(name: "OrganisationPortfolioHistory")
    createdAt: AWSDateTime
    updatedAt: AWSDateTime
}

type TenancySettings
@model(queries: null, mutations: {update: "updateTenancySettings"}, subscriptions: null)
@key(name: "gsi-OrganisationSettings", fields: ["organisationId"])
@searchable
{
    id: ID!
    rentCommission: Float
    percentageFee: Int
    fixedFee: Int
    feeType: FeeType
    currency: String!
    invoiceRentInAdvanceDays: Int!
    autoInvoiceRent: Boolean!
    createdAt: AWSDateTime
    organisationId: String
    tenancyledgerCode: LedgerCode
    accountCode: LedgerCode
    feeLedgerCode: LedgerCode
    updatedAt: AWSDateTime
    nextInvoiceSendDate: String
    nextInvoiceDueDate: String
    organisation: Organisation! @connection(name: "OrganisationTenancySettings")
    tenancy: Tenancy
    sendInvoiceToTenant: Boolean!
    stationary: String
    contractValue: Int
    eRV: Int
    firstJournalRunDate: String
    lastJournalRunDate: String
    autoApprovalBill: Boolean
    rentDueAutoSettings: RentDueAutoSettings
    lateRentAutoSettings: LateRentAutoSettings
    fundDistribution: [FundDistribution]
    autoJournalArrears: [AutoJournalArrears]
}

type FundDistribution {
    ledger: String
    amount: Int
}

type AutoJournalArrears {
    amount: Int
    ledgerCode: String
    ledgerName: String
}

type RentDueAutoSettings {
    triggerOn: Boolean
    emailSender: String
    emailContent: String
    emailProvider: String
}

type LateRentAutoSettings {
    triggerOn: Boolean
    autoEmail: Boolean
    emailDelayDays: Int
    emailSender: String
    emailContent: String
    emailProvider: String
    autoInvoice: Boolean
    invoiceDelayDays: Int
    amount: Float
    accountCode: LedgerCode
}

type BreakClauseItem {
    who: UserType
    date: AWSDateTime
    noticeDate: AWSDateTime
}

type TenancyStreamItem
@model(queries: null, mutations: null, subscriptions: null)
{
    id: ID!
    tenancy: Tenancy!
    event: TenancyStreamItemType!
    content: String!
}

type DocumentSuppliers
@model(queries: null, mutations: {create: "createDocumentSupplier", delete: "deleteDocumentSupplier"}, subscriptions: null)
{
    id: ID!
    user: User! @connection(name: "UserDocumentSuppliers")
    document: Document! @connection(name: "DocumentDocumentSuppliers")
    organisation: Organisation! @connection(name: "OrganisationDocumentSuppliers")
    createdAt: AWSDateTime
    updatedAt: AWSDateTime
}
type DocumentTemplate
@model(queries: {list: "listDocumentTemplates"}, mutations: {create: "createDocumentTemplate", update: "updateDocumentTemplate", delete: "deleteDocumentTemplate"}, subscriptions: null)
{
    id: ID!
    title: String
    type: String
    other:[String]
    status: DocumentTemplateStatus
    contractTypes: [TenancyType]
    organisation: Organisation @connection(name: "organisationDocumentTemplates")
    user: User @connection(name: "userDocumentTemplates")
    docusignTemplate: DocusignTemplates @connection(name: "documentTemplateDocusignTemplate")
}
type DocumentTemplateTypes
@model(queries: {list: "listDocumentTemplateTypes"}, mutations: {create: "createDocumentTemplateTypes"}, subscriptions: null)
@key(name: "gsi-OrganisationTemplateTypes", fields: ["organisationId","index"])
{
    id: ID!
    name: String
    index: Int!
    createdAt: AWSDateTime
    organisationId: String
}


type Document
@model(queries: {get: "getDocument"}, mutations: {create: "createDocument", update: "updateDocument", delete: "deleteDocument"}, subscriptions: null)
@key(name: "gsi-TenancyAttachments", fields: ["documentTenancyId"])
@key(name: "gsi-PropertyAttachments", fields: ["documentPropertyId"])
@key(name: "gsi-UserDocuments", fields: ["documentUserId"])
@key(name: "gsi-OrganisationDocumentsUnsorted", fields:["documentOrganisationId"])
@key(name: "gsi-ChatAttachments", fields: ["documentConversationId"])
@key(name: "gsi-SupplierOrganisationDocuments", fields: ["documentSupplierOrganisationId"])
@key(name: "gsi-TaskImages", fields: ["imageTaskId"])
@key(name: "gsi-OrganisationImages", fields: ["imageOrganisationId"])
@key(name: "gsi-ParentPropertyEntityId", fields: ["parentPropertyEntityId"])
@key(name: "gsi-EmailAttachment", fields: ["documentEmailAttachmentId"])
@searchable(queries: {search: null})
{
    id: ID!
    key: String!
    name: String
    type: DocumentType
    status: DocumentStatus
    description: String
    mimeType: String!
    archived: Boolean
    createdAt: AWSDateTime
    updatedAt: AWSDateTime
    expiry: AWSDateTime
    done: Boolean
    documentTenancyId: String
    documentDepositRegistrationTenancyId: String
    documentPropertyId: String
    documentSupplierOrganisationId: String
    documentOrganisationId: String
    documentUserId: String
    documentConversationId: String
    documentEmailAttachmentId: String
    imageTaskId: String
    generalDocument: Boolean
    isParentPropertyDocumentShared: Boolean
    imageOrganisationId: String
    parentPropertyEntityId: String
    permission: Permissions @connection(name: "DocumentPermissions")
    tenancy: Tenancy  @function(name: "attachmentsService-${env}")
    property: Property  @function(name: "attachmentsService-${env}")
    user: User @function(name: "attachmentsService-${env}")
    conversation: Conversation @function(name: "attachmentsService-${env}")
    organisation: Organisation @connection(name: "OrganisationDocuments")
    emailAttachment: EmailAttachment @connection(name: "EmailDocument")
    supplierOrganisation: SupplierOrganisation @function(name: "attachmentsService-${env}")
    suppliers: [DocumentSuppliers] @connection(name: "DocumentDocumentSuppliers")
    task: Task @connection(name: "taskAttachments")
    lineItem: InvoiceLineItem @connection(name: "LineItemDocuments")
}

enum DocumentStatus {
    VALID
    EXPIRING
    EXPIRED
    UNCERTAIN
    FAILED
}

enum DocumentType {
    # Compliance Documents - Core
    GAS_SAFETY_CERTIFICATE
    EICR
    EPC
    SMOKE_ALARM
    LEGIONELLA
    FIRE_RISK_ASSESSMENT
    PAT

    # Compliance Documents - Additional
    CARBON_MONOXIDE_ALARM
    BOILER_SERVICE
    INSURANCE_CERTIFICATES
    BUILDING_REGULATIONS
    PROPERTY_LICENSING
    FENSA
    SEWAGE_DRAINAGE
    ASBESTOS_SURVEY
    ASBESTOS_MANAGEMENT
    FIRE_DOOR_INSPECTION
    EMERGENCY_LIGHTING
    SECURITY_SYSTEM
    SOUNDPROOFING
    RENEWABLE_ENERGY

    # UK - General
    METER_READING
    TENANT_RIGHTS
    GUIDE
    ID
    TS_AND_CS
    PROOF_OF_ADDRESS
    PROOF_OF_OWNERSHIP
    APPLICATION_FOR_TENANCY
    REFERENCE
    RIGHT_TO_RENT
    EMPLOYMENT_CONFIRMATION
    MANAGEMENT_CONTRACT
    STUDIES_CONFIRMATION
    LANDLORD_REFERENCE
    CHARACTER_REFERENCE
    WORK_ORDER
    QUOTE
    DEED_OF_ASSIGNMENT
    LICENSE_ALTERATION
    RENT_DEED_DEPOSIT
    LICENSE_TO_ASSIGN
    INSPECTION
    GAS_SAFETY
    PHOTO
    CHECK_IN
    CHECK_OUT
    INSURANCE
    STATEMENT
    BROCHURE
    ESTIMATE
    HEALTH_AND_SAFETY_REPORT
    RENTAL_AGREEMENT
    GAS_CERTIFICATE
    ELECTRICITY_CERTIFICATE
    ENERGY_CERTIFICATE
    INVENTORY_INSPECTION
    PROPERTY_BROCHURE
    WORK_ESTIMATE_REPORT
    ASBESTOS_CHECK
    UTILITY_BILL
    ENGLISH_INVOICE
    FORCE_MAJEURE
    MISCELLANEOUS
    SERVICE_CHARGE_BUDJET
    BLOCK_BUDJET
    FLOOR_PLAN
    INSURANCE_DOCUMENT
    CONTRACT
    WORKS_ESTIMATE
    ASBESTOS_REPORT
    HEALTH_AND_SAFETY_AUDIT
    PROPERTY_DETAILS
    BUDGET
    SUPPLIER_INSURANCE
    METHOD_STATEMENT
    PROPOSAL
    INSTRUCTIONS
    WARRANTY_DOCUMENT
    CHECKIN_REPORT
    CHECKOUT_REPORT
    GUIDANCE_DOCUMENT
    ELECTRICAL_CONDITION
    INVOICE
    OTHER
    CHIMNEY_SWEEP
    CLIENTS_STATEMENT
    CLIENTS_GUIDE
    TENANTS_INSURANCE
    TENANTS_GUIDE
    MARKETING_PHOTO
    SUPPLIER_CONTRACT
    SUPPLIER_INVOICE

    # US
    RENTAL_LICENSE
    RENTERS_INSURANCE
    HOA_VIOLATION_NOTICE
    TENANT_IDENTIFICATION
    INSPECTION_REPORT
    INSURANCE_POLICY
    MAINTENANCE_RECORD
    TITLE_DEED
    WARRANTY_INFORMATION
    RULES_AND_REGULATIONS
    LEASE_TEMPLATES
    HISTORIC_MARKETING_MATERIALS
    UTILITY_INFORMATION
    FINANCIAL_STATEMENTS
    TAX_DOCUMENTS
    VENDOR_CONTRACTS
    LANDLORD_INSURANCE_POLICY
    LANDLORD_TENANT_COMMUNICATIONS
    LEGAL_DOCUMENTS
    LEASE_AGREEMENT
    MOVE_IN_INSPECTION_CHECKLIST
    MOVE_OUT_INSPECTION_CHECKLIST
    RENT_PAYMENT_RECEIPTS
    NOTICE_OF_RENT_INCREASES
    SECURITY_DEPOSIT_RECEIPT_AND_STATEMENT
    RULES_AND_REGULATION_FOR_TENANTS
    NOTICES_OF_ENTRY_AND_MAINTENANCE
    TENANT_PRIVACY_RIGHTS_INFO
    EMERGENCY_CONTACT_INFORMATION
    UTILITY_BILLING_INFORMATION
    TENANT_APPLICATION_FORMS
    TENANT_BACKGROUND_CHECKS
    MAINTENANCE_REQUESTS_AND_RECORDS
    LEGAL_CONTRACTS_AND_AGREEMENTS
    PROPERTY_MAINTENANCE_INVOICES
    PROPERTY_INSPECTION_PHOTOS
    EVICTION_NOTICES_AND_RECORDS
    PROPERTY_PURCHASE_DOCUMENT
    ENVIRONMENTAL_COMPLIANCE_DOCUMENTS
    OTHER_CORRESPONDENCE_AND_COMMUNICATIONS
    MISCELLANEOUS_PROPERTY_DOCUMENTS
    LANDLORD_TAX_DOCUMENTS
}


type Room
@model(queries: {get: "getRoom"}, mutations: null, subscriptions: null)
@key(fields: ["id", "index"])
{
    id: ID!
    name: String!
    icon: String!
    index: Int!
}

type Category
@model(queries: {list: "listCategorys"}, mutations: null, subscriptions: null)
 {
    id: ID!
    name: String!
    icon: String!
    rooms: [String]
    index: Int!
}

type SubCategory
@model(queries: null, mutations: null, subscriptions: null)
 {
    id: ID!
    name: String!
    roomId: String!
    categoryId: String!
    index: Int!
}

type PropertyEmergency
@model(queries: {get: "getPropertyEmergency", }, mutations: null, subscriptions: null)
@key(fields: ["propertyId"])
{
    propertyId: ID!
    emergencies: String
    officeHours: String
    outOfHours: String
}

type CategoryDocument
@model(queries: {get: "getCategoryDocument", }, mutations: null, subscriptions: null)
@key(fields: ["categoryId", "orgnisationId"])
@key(name: "gsi-OrganisationCategoryDocument", fields: ["orgnisationId"])
 {
    categoryId: String!
    orgnisationId: String!
    key: String!
    name: String!
    mimeType: String!
    note: String
}

type ProblemCard
@model(queries: {get: "getProblemCard", }, mutations: {create: "createProblemCard", update: "updateProblemCard", delete: "deleteProblemCard" }, subscriptions: null)
@key(name: "gsi-PropertyProblemCards", fields: ["problemCardPropertyId", "createdAt"])
@key(name: "gsi-OrganisationProblemCards", fields: ["problemCardOrganisationId"])
@searchable
 {
    id: ID!
    number: String
    coverImage: String
    note: String
    problemReport: String
    images: [String]
    roomId: String!
    roomName: String!
    categoryId: String!
    categoryName: String!
    author: ID!
    categoryIcon: String!
    subCategoryName: String!
    createdByUser: User @connection(name: "UserProblemCards", fields: ["author"])
    urgent: Boolean!
    archived: Boolean
    createdAt: AWSDateTime
    updatedAt: AWSDateTime
    problemCardPropertyId: String
    problemCardOrganisationId: String
    property: Property @function(name: "tenancyService-${env}")
    tasks: [Task!] @connection(name: "ProblemCardTasks")
    organisation: Organisation @function(name: "tenancyService-${env}")
    status: ProblemCardStatus
}

enum ProblemCardStatus {
    CANCELLED
    PROCESSING
    RESOLVED
    TODO
}

type Subscriptions
@model(queries: null, mutations: null, subscriptions: null)
@key(fields: ["userId"])
 {
    userId: ID!
    subscription: WebPushSubscription
}

type SupplierOrganisation
@model(queries: {get: "getSupplierOrganisation", }, mutations: {create: "createSupplierOrganisation", update: "updateSupplierOrganisation", delete: "deleteSupplierOrganisation"}, subscriptions: null)
@searchable
{
    id: ID
    name: String
    logo: String
    emails: [String]
    phones: [String]
    addressLine1: String
    addressLine2: String
    addressLine3: String
    websiteAddress: String
    postcode: String
    city: String
    county: String
    internalNotes: String
    tags: [String]
    organisation: Organisation @connection(name: "OrganisationSupplierOrganisations")
    # Connection was removed here @connection(name: "SupplierOrganisationUsers", sortField: "sname")
    users: [User]
    documents(filter: ModelDocumentFilterInput, sortDirection: ModelSortDirection, limit: Int, nextToken: String): ModelDocumentConnection @function(name: "attachmentsService-${env}")
}

type SupplierOrganisationTag
@model(queries: null, mutations: {create: "createSupplierOrganisationTag"}, subscriptions: null)
@searchable
 {
    id: ID!
    name: String!
    organisation: Organisation! @connection(name: "OrganisationTags")
}

type BackendDataMonitor
@model(queries: null, mutations: null, subscriptions: null)
{
    id: ID!
    monitorName: String!
    monitorContent: String!
    createdAt: AWSDateTime
    updatedAt: AWSDateTime
}

type UserLoginRecord
@model(queries: null, mutations: null, subscriptions: null)
{
    id: ID!
    organisationId: ID!
    email: String!
    createdAt: AWSDateTime
}

type UserLoginSession
@model(queries: null, mutations: null, subscriptions: null)
@key(name: "gsi-Jti", fields: ["jti"])
@key(name: "gsi-DeviceEmail", fields: ["deviceId", "email"])
{
    id: ID!
    deviceId: String!
    email: String!
    jti: String!
    accessToken: String!
    refreshToken: String!
    createdAt: AWSDateTime
}

type Journal
@model(queries: null, mutations: null, subscriptions: null)
@key(name: "gsi-UserChanges", fields: ["userId", "createdAt"])
@key(name: "gsi-OrganisationChanges", fields: ["organisationId", "createdAt"])
@key(name: "gsi-ByObjectId", fields: ["objectId", "createdAt"])
@key(name: "gsi-ByParentId", fields: ["parentId", "createdAt"])
 {
    id: ID!
    # User who made a change
    userId: ID!
    userFName: String
    userSName: String
    organisationId: ID!
    objectId: ID
    parentId: ID
    objectType: ObjectType
    parentType: ParentType
    eventType: EventType
    attributeName: String
    previousValue: String
    createdAt: AWSDateTime
    updatedAt: AWSDateTime
}

type ReportingHistory
@model(queries: null, mutations: null, subscriptions: null)
@key(name: "gsi-ByParentIdAndParentType", fields: ["parentId", "parentType"])
{
    id: ID!
    parentType: String
    parentId: String
    label: String!
    #json
    data: String!
    labelDateQuery: String!
    dateRecorded: String!
    createdAt: AWSDateTime
    updatedAt: AWSDateTime
    organisation: Organisation! @connection(name: "OrganisationReportingHistory")
}

type PropertyAsset
@model(
  queries: null,
  mutations: null,
  subscriptions: null
)
@key(name: "gsi-ByPropertyId", fields: ["propertyId"])
@key(name: "gsi-ByOrganisationId", fields: ["organisationId"])
@searchable
{
    id: ID!
    organisationId: ID!
    propertyId: ID
    property: Property @connection(name: "PropertyAssets")
    # Contract is related to Tenancy
    contractId: ID
    contract: Tenancy @connection(name: "TenancyPropertyAssets")

    propertyAssetName: String!
    make: String
    model: String

    purchaseDate: AWSDateTime
    warrantyExpiryDate: AWSDate

    relationship: PropertyAssetRelationship!
    status: PropertyAssetStatus!
    category: PropertyAssetCategory!
    categoryItem: PropertyAssetCategoryItem

    room: String
    condition: String
    description: String
    attachedFiles: [PropertyAssetAttachedFile]

    remindersCount: Int
}

type PropertyAssetAttachedFile {
    key: String!
    name: String!
    type: String!
}

enum AllocationStatus {
    NOT_ALLOCATED
    PARTIALLY_ALLOCATED
    ALLOCATED
    OVER_ALLOCATED
}

enum EventType {
    CREATE
    UPDATE
    DELETE
    FILTER
    INSERT
    SET
}
enum DocumentTemplateStatus{
    PENDING
    APPROVED
    PAUSED
    ARCHIVE
    DECLINED
}
enum TenancyType {
    NA
    RESIDENTIAL
    FIRM_TERM
    MONTH_TO_MONTH
    SUBLET
    VACATION_STAY
    RENEWAL
    JOINT
    RENT_TO_OWN
    DPS_CUSTODIAL
    PROJECT
    APARTMENT
    RESIDENTIAL_CONTRACT
    SECTION_8
    DPS_INSURANCE
    MY_DEPOSITS
    TDS_CUSTODIAL
    TDS_INSURANCE
    REPOSIT
    DISPUTE_SERVICE_CUSTODIAL
    INSURANCE
    HELD_BY_AGENT
    HELD_BY_LANDLORD
    AST
    ASSURED
    CONTRACTUAL
    COMMON_LAW
    LETTING
    LICENSE
    COMMERCIAL
    SERVICE_CHARGE
    HOLIDAYLET
    NONHOUSINGACT
    SALE
    LEASE
    GROUND_RENT
}

enum DepositStatus {
    RECEIVING
    REGISTERING

    #REFUNDING -> Decided in the code. (It was too dificult to keep it consistant with other fields)

    REFUNDED
}

enum ObjectType {
    DOCUMENT
    PROBLEM_CARD
    PROPERTY
    TENANCY
    USER
    ORGANISATION
}

enum UserCreationHistory {
    RENTANCY
    XERO
    CHIME
}

enum TaskType {
    PROBLEM_CARD
}

enum InvoiceType {
    ACCPAY
    ACCREC
}

enum ResourceType {
    CONTACT
    INVOICE
    TRANSACTION
}

enum EventStatus {
    UNKNOWN
    ACCEPTED
    REJECTED
    TENATIVE
}

enum FeeType {
    FIXED
    PERCENTAGE_OF_FULL_AMOUNT
    PERCENTAGE_OF_AMOUNT_RECEIVED
    PERCENTAGE_OF_JOURNAL_AMOUNT_RECEIVED
    NONE
}

enum TaskStatus {
    ACTIVE
    DRAFT
    ARCHIVED
    DELETED
}

enum PropertyGroups {
    OCCUPIED
    OFFER
    VACANT
}

type Metadata {
    portfolio: Int  
    chat: Int  
    task: Int  
}

type ActivityMetadata {
    supplierOrganisationId: String  
    supplierOrganisationName: String  
    tenancyNumber: String  
    primaryTenantName: String  
    primaryTenantImg: String  
    tenancyStartDate: String  
    tenancyBreakDate: String  
    tenancyReviewDate: String  
    tenancyEndDate: String  
    taskDeadline: String  
    currency: String  
    rent: Int  
    tenants: [String]  
    landlords: [String]  
    guarantors: [String]  
    postCode: String  
    propertyType: String  
    listingType: String  
    reference: String  
    parentReference: String  
    tenancyType: String  
    mutator: String  
    status: String  
    address: String  
    city: String  
    state: String  
    title: String  
    parentTitle: String  
    documentType: String  
    documentExpiry: String  
    ownerFullName: String  
    users: String  
}

# If you change this, DONT FORGET TO UPDATE NOTIFICATION'S PROJECT
enum ActivityType {
    PROPERTY
    END_OF_TENANCY
    TENANCY
    REPORT
    CHAT
    GROUP_CHAT
    EMAIL_GROUP_CHAT
    INVITATION
    TASK
    TASK_REMINDER
    PROBLEM_CARD
    CONTRACT_BREAK_REMINDER
    CONTRACT_END_REMINDER
    CONTRACT_START_REMINDER
    CONTRACT_REVIEW_REMINDER
    DOCUMENT_EXPIRY_REMINDER
    BILL_PAID_REMINDER
}

enum ActivityStatus {
    UNREAD
    READ
    STARRED
    PRIORITY
    RESOLVED
    UNRESOLVED
    MEMBER
    NON_MEMBER
    UNSENT
    SENT
    PUSHED
    NOT_PUSHED
}

type ModelMessageConnection {
    items: [Message]  
    nextToken: String  
}

type ModelSupplierOrganisationConnection {
    items: [SupplierOrganisation]  
    nextToken: String  
}

type ModelDocumentConnection {
    items: [Document]
    nextToken: String
}

type ContactEmail {
    email: String
    type: String
}

type ContactPhone {
    cca2: String
    phone: String
    type: String
    code: String # phone code
}

type LedgerCode {
    #name of the ledger code
    name: String
    displayName: String
    code: String
    accounts: [String]
    includedInPayout: Boolean
    income: Boolean
}

type RentancyTrackingCategory {
    name: String
    xeroName: String
}

type applicationItem {
    applicationId: String
    subjectId: String
    correlationId: String
    status: String
    tenantId: String
    createdAt: String
}

type InvoiceTemplate {
    type: String!
    templateName: String!
    rentDescription: String
}

input ModelDocumentFilterInput {
    id: ModelIDInput
    key: ModelStringInput
    name: ModelStringInput
    description: ModelStringInput
    mimeType: ModelStringInput
    createdAt: ModelStringInput
    updatedAt: ModelStringInput
    and: [ModelDocumentFilterInput]
    or: [ModelDocumentFilterInput]
    not: ModelDocumentFilterInput
}

type WebPushSubscription {
    endpoint: String
    expirationTime: String
    keys: WebPushSubscriptionKeys
}

type MailSample {
    type: String
    subject: String
    body: String
}

type WebPushSubscriptionKeys {
    p256dh: String
    auth: String
}

input WebPushSubscriptionInput {
    endpoint: String
    expirationTime: String
    keys: WebPushSubscriptionKeysInput
}

input WebPushSubscriptionKeysInput {
    p256dh: String
    auth: String
}

input UserMetadataInput {
    portfolio: Int
    chat: Int
    task: Int
}

input ModelUserPreferencesFilterInput {
    userPreferencesUserId: ModelIDInput
}

type ModelUserPreferencesConnection {
    items: [UserPreferences]!
}

type ModelSupplierPropertyConnection {
    items: [SupplierProperty]!
}

input CreateUserPreferencesInput {
    allowedNotifications: [NotificationType!]
    userPreferencesUserId: ID!
}

input SyncSupplierPropertiesWithSuppliersInput {
    supplierIds: [ID!]
    propertyId: ID!
}

input SyncSupplierPropertiesWithPropertiesInput {
    supplierId: ID!
    propertyIds: [ID!]
}

input UpdateUserPreferencesInput {
    id: ID!
    allowedNotifications: [NotificationType!]
}

enum IntegrationService {
    XERO
    QUICK_BOOKS
    CHIME
    GOOGLE # used for calendar
    OUTLOOK # used for calendar
    GOOGLE_GMAIL # used for gmail
    OUTLOOK_EMAIL # use for outlook email
    STRIPE
    CHECKBOOK
    TWILIO
}

enum NotificationType {
    EMAIL_CHAT_NOTIFICATIONS
    SMS_CHAT_NOTIFICATIONS
    EMAIL_PROPERTY_NOTIFICATIONS
    SMS_PROPERTY_NOTIFICATIONS
    EMAIL_TENANCY_NOTIFICATIONS
    SMS_TENANCY_NOTIFICATIONS
}

enum MessageStatus {
    SENT
    READ
}

enum MessageType {
    TEXT
    PROBLEM_CARD
    TENANCY_DETAILS
    TENANCY_ATTACHMENTS
    CONVERSATION_ATTACHMENTS
    CONVERSATION_PDF
}

enum ParentType {
    ORGANISATION
    PROPERTY
    PROBLEM_CARD
    END_OF_TENANCY
    TENANCY
    COMPOSITE
    TASK
    EVENT
    EMAIL
    USER
    DOCUMENT
    PARENTPROPERTYENTITY
}

enum AddressType {
    HOME
    POSTAL
}

enum ConnectionStatus {
    SENT
    REJECTED
    ACCEPTED
}

enum TenancyStreamItemType {
    INVITE_EVENT
    DOCUMENT_UPLOAD
    REPAIR_REQUEST
    VISIT_REQUEST
}

enum PropertyStatus {
    VALUATION_DUE
    NOT_AVAILABLE
    UNDER_RENOVATION
    ON_HOLD
    MARKETED
    PENDING_LEASE
    SCHEDULED_MOVE_IN
    SCHEDULED_MOVE_OUT
    LEASE_EXPIRED
    UNDER_MAINTENANCE
    EVICTION_PROCESS
    INSPECTION_DUE
    UNDER_CONTRACT
    LEASED
    LEASING_ONLY
    MILITARY
    LEGAL
    EVICTED
    INSTRUCTED
    ON_THE_MARKET
    UNDER_OFFER
    OFFER_AGREED
    LET
    LET_EXTERNALLY
    SOLD
    TOM
    LOST_TO_LET
    LOST_FOR_SALE
    NOT_INSTRUCTED
    MANAGED
    OPERATED
    VACANT
    NA
    ACTIVE
    INACTIVE
    UNDER_CONTRACTS
    OCCUPIED
    DRAFT
}

enum TenancyStatus {
    DRAFT
    APPLICATION
    REFERENCING
    RTR_CHECK
    CONTRACT
    ON_NOTICE
    VACANT
    TRANSFER_MONEY
    CHECK_IN
    ACTIVE
    NOTICE_GIVEN
    RENEWED
    PERIODIC
    VACATING
    MTM
    VACATED
    CANCELLED
    ARCHIVED
    HEAD_OF_TERMS
    UNDER_OFFER
    OFFER_ACCEPTED
    CONVEYANCING
    EXCHANGE
    SOLD_STC
    HOLDING_OVER
    LET_EXTERNALLY
    UPCOMING
    RENEWING
    EXPIRING
    EXPIRED
}

enum Role {
    TENANT
    COLLEGE
    THIRD_PARTY
    LANDLORD
    AGENT
    GUARANTOR
    HELPDESK
    ADMIN_AGENT
    UNASSIGNED
    MANAGER
    FINANCE
    INBOX
    LOFTYPAY_ADMIN
}

enum UserType {
    TENANT
    LANDLORD
    LAWYER
    BROKER
    LEASING_AGENT
    PROPERTY_MANAGER
    LANDLORD_GUARANTEED_RENT
    OWNER
    SUPPLIER
    OWNER_GRO
    GUARANTOR
    SOLICITOR
    CONSULTANT
    OTHER
    AGENT
    STAFF
    TEAM
    ADMIN
    LOFTYPAY_ADMIN
    NEW
    MANAGER
    FINANCE
    INBOX
    SUSPENDED
    LEASEHOLDER
    APPLICANT
    SYSTEM
    INFORMAL # External users from email or whatsapp
    # TODO for backward compatibility
    HELPDESK
    ADMIN_AGENT
    PROSPECT
    PERMITTED_OCCUPIER
    UNKNOWN_INBOUND
}

enum BusinessType {
    PRIVATE
    PUBLIC
    UNIVERSITY
}

enum PermissionItemType {
    CONVERSATION
}

enum ModelSortDirection {
    ASC
    DESC
}

enum ModelInvoiceField {
    NUMBER
    REFERENCE
    TO
    DUE_DATE
    DATE
    AMOUNT_PAID
    AMOUNT_DUE
}

enum ModelPropertyField {
    REFERENCE
    ADDRESS
    CITY
    POSTCODE
    TYPE
    STATUS
    DATE
    NAME
    LISTINGSTATUS
    PROPERTYCOUNT
}

enum Period {
    DAILY
    WEEKLY
    TWO_WEEKLY
    MONTHLY
    QUARTERLY
    UK_QUARTERLY
    SIX_MONTHLY
    ANNUALLY
    BI_ANNUALLY
    FIVE_YEAR
    TEN_YEAR
    FIFTEEN_YEAR
    TWENTY_YEAR
    TWENTY_FIVE_YEAR
}

enum IntegrationStatus {
    NOT_CONNECTED
    CONNECTED
    FAILED
}

enum BankTransactionItemType {
    PAYMENT
    TRANSACTION
}

input ModelEventFilter {
    organisationId: String
    mine: Boolean
    startDate: String
    endDate: String
}

input ModelTaskFilter {
    users: [String!]
    deadline: ModelStringInput
    properties: [String!]
}

input ModelActivityFilterInput {
    id: ModelIDInput
    parentId: ModelStringInput
    parentType: ModelParentTypeInput
    updatedDate: ModelStringInput
    type: ModelActivityTypeInput
    action: ModelStringInput
    link: ModelStringInput
    image: ModelStringInput
    title: ModelStringInput
    subTitle: ModelStringInput
    status: ModelStringInput
    readStatus: ModelActivityStatusInput
    resolveStatus: ModelActivityStatusInput
    memberStatus: ModelActivityStatusInput
    associatedUserId: ModelStringInput
    activityUserId: ModelIDInput
    activityOrganisationId: ModelIDInput
    and: [ModelActivityFilterInput]
    or: [ModelActivityFilterInput]
    not: ModelActivityFilterInput
}

input ModelIDInput {
    ne: ID
    eq: ID
    le: ID
    lt: ID
    ge: ID
    gt: ID
    contains: ID
    notContains: ID
    between: [ID]
    beginsWith: ID
    attributeExists: Boolean
    attributeType: ModelAttributeTypes
    size: ModelSizeInput
}

enum ModelAttributeTypes {
    binary
    binarySet
    bool
    list
    map
    number
    numberSet
    string
    stringSet
    _null
}

# Enum for PropertyAsset Category Items
enum PropertyAssetCategoryItem {
    #APPLIANCES
    REFRIGERATORS
    DISHWASHERS
    WASHERS_AND_DRYERS
    OVENS
    MICROWAVES
    AIR_CONDITIONERS
    #FURNITURE
    SOFAS
    BEDS
    DINING_TABLES
    CHAIRS
    DESKS
    CABINETS
    #ELECTRONICS
    TELEVISIONS
    COMPUTERS
    PRINTERS
    SECURITY_SYSTEMS
    ROUTERS
    #FIXTURES
    LIGHT_FIXTURES
    PLUMBING_FIXTURES
    DOOR_HANDLES
    BATHROOM_FIXTURES
    CEILING_FANS
    #HVAC
    HEATING_SYSTEMS
    VENTILATION_SYSTEMS
    AIR_CONDITIONING_UNITS
    #OUTDOOR_EQUIPMENT
    LAWN_MOWERS
    SPRINKLER_SYSTEMS
    OUTDOOR_FURNITURE
    GRILLS
    GARDENING_TOOLS
    #TOOLS_AND_MAINTENANCE_EQUIPMENT
    POWER_TOOLS
    HAND_TOOLS
    CLEANING_EQUIPMENT
    LADDERS
    #DECORATIVE_ITEMS
    PAINTINGS
    VASES
    RUGS
    CURTAINS
    DECORATIVE_LIGHTING
    #UTILITIES
    GENERATORS
    WATER_HEATERS
    FUEL_TANKS
    CLEANING_SUPPLIES
    #LOCKBOX
    LOCKBOX
}

enum PropertyAssetCategory {
    APPLIANCES
    FURNITURE
    ELECTRONICS
    FIXTURES
    HVAC
    OUTDOOR_EQUIPMENT
    TOOLS_AND_MAINTENANCE_EQUIPMENT
    DECORATIVE_ITEMS
    UTILITIES
    LOCKBOX
}

# Enum for PropertyAsset Status
enum PropertyAssetStatus {
    ACTIVE
    INACTIVE
}

# Enum for PropertyAsset Relationships
enum PropertyAssetRelationship {
    PROPERTY
    CONTRACT
}

input ModelSizeInput {
    ne: Int
    eq: Int
    le: Int
    lt: Int
    ge: Int
    gt: Int
    between: [Int]
}

input ModelStringInput {
    ne: String
    eq: String
    le: String
    lt: String
    ge: String
    gt: String
    contains: String
    notContains: String
    between: [String]
    beginsWith: String
    attributeExists: Boolean
    attributeType: ModelAttributeTypes
    size: ModelSizeInput
}

input ModelParentTypeInput {
    eq: ParentType
    ne: ParentType
}

input ModelActivityStatusInput {
    eq: ActivityStatus
    ne: ActivityStatus
}

input ModelActivityTypeInput {
    eq: ActivityType
    ne: ActivityType
}

type ModelActivityConnection   {
    unreadCount: Int!
    items: [Activity]
    nextToken: String
}

type OrganisationUnreadActivity {
    organisationId: String!
    unreadCount: Int!
}

type ModelUserActivityConnection {
    total: Int
    items: [Activity]
    unreadCount: Int!
    organisationUnreadCount: [OrganisationUnreadActivity]!
    doNotDisturbConversationIds: [ID]
}

input AddtionalCostInput {
    title: String
    cost: String
}

input CreateDocumentInput {
    key: String!
    name: String
    type: String
    status: String
    description: String
    mimeType: String
    archived: Boolean
    expiry: AWSDateTime
    validFrom: AWSDateTime
    done: Boolean
    documentTenancyId: String
    documentPropertyId: String
    documentSupplierOrganisationId: String
    documentOrganisationId: String
    documentUserId: String
    documentConversationId: String
    documentEmailAttachmentId: String
    imageTaskId: String
    generalDocument: Boolean
    isParentPropertyDocumentShared: Boolean
    imageOrganisationId: String
    parentPropertyEntityId: String
    documentPermissionId: String
    documentTaskId: String
    documentLineItemId: String
}

input CreateTenancyInput {
    title: String
    applicants: [String]
    period: Period
    startDate: AWSDateTime
    tenancyReference: String
    endDate: AWSDateTime
    breakClauseDate: AWSDateTime
    paymentDay: Int
    deposit: Int
    depositProtectionScheme: String
    depositDateRegistered: AWSDateTime
    rent: Int
    isRentGuaranteeRequired: Boolean
    currency: String!
    tenants: [String!]
    teamMembers: [String!]
    landlords: [String!]
    guarantors: [String!]
    status: TenancyStatus!
    propertyId: String!
    rentReview: AWSDateTime
    type: TenancyType
    reservation: Int
    customReference: String
    rentCommission: Float
    percentageFee: Int
    fixedFee: Int
    feeType: FeeType
    autoInvoiceInput: Boolean

    rentFrequencies: Period
    permittedOccupiers: [String]
    addtionalCosts: [AddtionalCostInput]
    documents: [CreateDocumentInput]

    automaticAllocationOfTenantFundsToRent: Boolean
    automaticRentInvoice: Boolean
    managementFeeEnabled: Boolean
    managementFeeFixed: Int
    managementFeePercentage: Int
    managementFeeStrategy: String
    managementFeeTrigger: String
    propertyManagerId: String
}

input UpdateTenancyInput {
    id: ID!
    title: String
    period: Period
    startDate: AWSDateTime
    endDate: AWSDateTime
    breakClauseDate: AWSDateTime
    expectedExchangeDate: AWSDateTime
    renewalDate: AWSDateTime
    paymentDay: Int
    deposit: Int
    depositReference: String
    dateDepositRegistered: AWSDateTime
    reference: String
    rent: Int
    isRentGuaranteeRequired: Boolean
    rentIncreaseAmount: Int
    tenantBreakClauseMonths: Int
    landlordBreakClauseMonths: Int
    askingPrice: Int
    agreedPrice: Int
    saleAdminFee: Int
    rentNote: String
    currency: String
    rentIncreaseDate: String
    primaryTenant: String
    tenants: [String!]
    teamMembers: [String!]
    landlords: [String!]
    guarantors: [String!]
    status: TenancyStatus
    archived: Boolean
    accounting: Boolean
    landlordVat: Boolean
    autoInvoiceRent: Boolean
    addLandlordDetails: Boolean
    attachInvoicesToContract: Boolean
    shareInvoicesToContract: Boolean
    shareInvoicesWithLandlord: Boolean
    rentReview: AWSDateTime
    rentReviewDate: AWSDateTime
    invoiceStartDate: AWSDateTime
    reviewNotice: AWSDateTime
    type: TenancyType
    reservation: Int
    breakClauseItems: [BreakClauseItemInput]
    area: Int
    openingBalance: Int
    tags: [String]
    dontCollectRent: Boolean
    enableJournal: Boolean
    enableProRataJournal: Boolean

    depositReturned: Boolean
    refundedDate: AWSDateTime
    depositReleased: Boolean
    depositRegistered: Boolean
    depositDateRegistered: AWSDateTime
    depositDisputed: Boolean
    certificateNumber: String
    depositProtectionScheme: String
    depositStatus: DepositStatus
    depositTransferred: Boolean
    transferredDate: AWSDateTime

    rentFrequencies: Period
    permittedOccupiers: [String]
    addtionalCosts: [AddtionalCostInput]

    automaticAllocationOfTenantFundsToRent: Boolean
    automaticRentInvoice: Boolean
    managementFeeEnabled: Boolean
    managementFeeFixed: Int
    managementFeePercentage: Int
    managementFeeStrategy: String
    managementFeeTrigger: String
    propertyManagerId: String

    reminderDays: Int
}

input MoveOutTenancyInput {
    id: ID!
    organisationId: String!
    moveOutDate: AWSDateTime!
    status: TenancyStatus!
    utilities: [UtilityInput]
    checkoutReports: [CreateDocumentInput]
    tenantLetter: String
    landlordLetter: String
    primaryTenantId: String
    primaryLandlordId: String
    outstandingPayments: Int
}

input PropertyFloatInput {
    propertyId: String!
    targetFloat: Float!
}

input AddFloatsToPropertyInput {
    landlordId: String!
    floats: [PropertyFloatInput!]!
}

input CheckExistingAddressRequest {
    organisationId: ID!
    addressLine1: String!
    addressLine2: String
    city: String
}

input BreakClauseItemInput {
    who: UserType
    date: AWSDateTime
    noticeDate: AWSDateTime
}

input CreateEventInput {
    name: String!
    title: String
    description: String
    parentId: String
    parentType: ParentType
    allDay: Boolean
    startDate: AWSDateTime
    endDate: AWSDateTime
    when: AWSDateTime
    organisationId: String!
    guests: [AWSEmail]
}

input UpdateEventInput {
    id: ID!
    name: String
    title: String
    description: String
    allDay: Boolean
    status: EventStatus
    startDate: AWSDateTime
    endDate: AWSDateTime
    when: AWSDateTime
    guests: [AWSEmail]
}

input CreateNotificationInput {
    eventId: String!
    when: String!
}

input UpdateNotificationInput {
    id: ID!
    when: String!
}

input DeleteEventInput {
    id: ID!
}

input DeleteNotificationInput {
    id: ID!
}

input UpdateColumnInput {
    id: ID!
    index: Int
    name: String
    description: String
}

input CreateTaskInput {
    name: String
    description: String
    type: TaskType
    status: String
    parentType: ParentType
    parentId: String
    taskColumnId: String
    taskBoardId: String
    taskUserId: ID
    deadline: String
    time: Boolean
    shareToExternalCalendar: Boolean
    taskLabelId: String
    taskProblemCardId: String
    note: String
    images: [String]
}

input CreateColumnInput {
    name: String!
    columnBoardId: String!
    description: String
}

input UpdateTaskInput {
    id: ID!
    index: Int
    name: String
    description: String
    type: TaskType
    status: String
    taskColumnId: ID
    taskUserId: ID
    parentType: ParentType
    parentId: String
    deadline: String
    time: Boolean
    disabled: Boolean
    shareToExternalCalendar: Boolean
    taskLabelId: String
    linkedContractor: String
    note: String
}

input DeleteAttachmentInput {
    id: String!
}

input AttachmentInput {
    key: String!
    name: String
    description: String
    mimeType: String!
}

input SearchInput {
    query: String!
    types: [SearchType!]!
    fields: [String]
    limit: Int!
    nameSorting: ModelSortDirection
}

input SearchUserInput {
    query: String!
    filter: SearchFilter
}

input SearchFilter {
    types: [String]
}

enum SearchType {
    PARENT_PROPERTY_ENTITY
    PROPERTY
    TENANCY
    USER
    CONVERSATION
    PROBLEM_CARD
    DOCUMENT
    SUPPLIER_ORGANISATION
    SUPPLIER_ORGANISATION_TAG
    CONTACT
    ADDRESS
    TASK
    INVOICE
}

input CreateInvitationInput {
    inviterId: String @deprecated(reason: "We won't use it anymore. Inviter ID is going to be taken from the JWT")
    userId: String
    invitedUserEmail: String
    tenancyId: String
    conversationId: String
    organisationId: String
    role: String
    resendManually: Boolean
}

input InvitationFilter {
    inviterId: String
    tenancyId: String
    email: String
    role: String
}

input ContactsFilter {
    memberStatus: String
    parentType: ParentType
    parentId: String
    roles: [String!]
    types: [String!]
    signedUp: Boolean
    search: String
    sortBy: String
    nameConfirmed: Boolean
    email: String
    phone: String
    companyName: String
    page: Int
    pageSize: Int
    hasProperties: Boolean
}

input ExpiringTenanciesFilter {
    search: String
    status: ExpiringTenancyStatus,
    endDate: AWSDateTime
}

input SortInvoiceInput {
    field: ModelInvoiceField!
    direction: ModelSortDirection!
}

input SortPropertyInput {
    field: ModelPropertyField!
    direction: ModelSortDirection!
}

input ListPropertyInput {
    limit: Int
    page: Int
    sortBy: SortPropertyInput
    filter: String
    status: [PropertyStatus]
    listingStatus: [ListingStatus]
    type: String
    groupBy: PropertyGroups
    userLandlordId: ID
    userManagerId: ID
    hasUnit: Boolean
    parentId: String
    landlords: [String]
}

input MatchInterestedPropertyInput {
    limit: Int
    page: Int
    sortBy: SortPropertyInput
    filter: String
    expectedMaxRent: Int
    expectedMinRent: Int
    expectedMinBedRooms: Int
    expectedMinBathRooms: Int
    expectedCity: String
    expectedState: String
    interestedUserId: String
}

type ApplicantScheduleViewingModel {
    scheduleViewing: ApplicantScheduleViewing
    propertyCoverImage: String
    assignToPhoto: UserPhoto
}

type ApplicantModel {
    user: User
    lettingsNegotiators: [UserItem]
}

input ListParentPropertyInput {
    limit: Int
    page: Int
    sortBy: SortPropertyInput
    filter: String
    landlordId: String
}

type UserPhoto {
    id: String
    key: String
}

input GetPropertiesByIdsInput {
    propertyIds: [ID]
}

input GetUnitsByParentIdInput {
    parentId: ID!
}

input UpdateOrganisationExpiredFlagInput {
    organisationIds: [String]!
    expiredFlag: String!
}

input UpdateUserTypeInput {
    userIds: [ID]!
    userType: String!
}

input checkEmailInput {
    organisationId: ID!
    emailAddress: String!
}

type CheckEmailResult {
    success: Boolean
    message: String
    data: String # userId if existed.
}

type SearchResult {
    took: Int!
    count: Int!
    items: [Item!]!
    aggregations: [Aggregation]!
}

type SearchUsersResult {
    took: Int!
    count: Int!
    items: [UserItem!]!
    aggregations: [Aggregation]!
}

type Item {
    type: String!
    content: Content!
    highlight: [Highlight]
}

type UserItem {
    type: String!
    content: User!
    highlight: [Highlight]
    userPhoto: UserPhoto
}

type Content {
    id: String
    tenancyId: String
    propertyId: String
    image: String
    name: String
    subTitle: String
    subType: String
    link: String
}

type Highlight {
    field: String
    value: [String]
}

type Aggregation {
    type: SearchType!
    count: Int!
}

type ModelPropertyEvents {
    count: Int
    from: AWSDateTime
    to: AWSDateTime
    status: Int
    items: [PropertyEvent]
}

type ModelBankAccounts {
    dataFetched: Boolean
    lastUpdatedDate: AWSDateTime
    errorMessage: String
    accounts: [Account]
}

type ModelBankTransactions {
    pageCount: Int
    limit: Int
    total: Int
    items: [BankTransactionItem]

    totalSpent: Float!
    totalReceived: Float!
    totalBalance: Float!
}

type ModelLandlordBill {
    pageCount: Int
    limit: Int
    total: Int
    nextProcessingTime: String
    items: [ModelLandlordBillItem]
}

type ModelStatements {
    pageCount: Int
    limit: Int
    total: Int
    items: [Statement]
    errorMessage: String
}

type ModelLandlordBillItem {
    id: ID!
    eventId: String!
    datePaid: AWSDateTime
    originalAmount: Int!
    propertyAddress: String!
    tenancyReference: String!
    landlordId: String
    landlordName: String
    landlordImage: String
    billAmount: Int!
    approved: Boolean!
    approvedBy: String
    dateRaised: AWSDateTime
    lastFinalizedStatement: Statement
    status: LandlordBillStatus!
    landlordBillOrganisationId: ID!
    ourInvoiceId: String
    propertyId: String
    originalInvoice: Invoice
    propertyBills: [Invoice]
    propertyCount: Int
    tenancyCount: Int
    unfundedItems: Int
    clientBalance: String
    groupedByProperty: [ModelLandlordBillProperty]
    groupedByParentProperty: [ModelLandlordBillParentProperty]
    incomes: [LandlordBillLineItem]
    expenses: [LandlordBillLineItem]
    itemsBroughtForward: [LandlordBillLineItem]
    landlordPercentages: [LandlordPercentageItem]
    floatSummary: [LandlordBillFloatSummary]
}

type LandlordBillFloatSummary {
    property: String
    available: String
    currentFloat: String
    targetFloat: String
}

type ModelLandlordBillProperty {
    property: String
    incomes: [LandlordBillLineItem]
    expenses: [LandlordBillLineItem]
    itemsBroughtForward: [LandlordBillLineItem]
}

type ModelLandlordBillParentProperty {
    parentProperty: String
    incomes: [LandlordBillLineItem]
    expenses: [LandlordBillLineItem]
    itemsBroughtForward: [LandlordBillLineItem]
}

type LandlordBillLineItem {
    date: String
    invoiceId: String
    property: String
    propertyId: String
    description: String
    amount: String
    id: String
    income: Boolean
}

type LandlordPercentageItem {
    landlordId: String
    landlordName: String
    percentage: Float
}

type BankTransactionItem {
    type: BankTransactionItemType
    balance: Float
    payment: Payment
    transaction: Transaction
}

type ModelInvoices {
    dataFetched: Boolean
    pageCount: Int
    limit: Int
    total: Int
    lastUpdatedDate: AWSDateTime
    errorMessage: String
    items: [Invoice]
}

type ModelPropertyInvoiceExpenses {
    dataFetched: Boolean
    pageCount: Int
    limit: Int
    total: Int
    lastUpdatedDate: AWSDateTime
    errorMessage: String
    items: [ModelPropertyInvoiceExpenseItems]
}

type ModelPropertyInvoiceExpenseItems {
    id: ID
    reference: String
    fromUser: User
    date: AWSDateTime
    dueDate: AWSDateTime
    paidAmount: String
    dueAmount: String
    taxExclusive: Boolean
    status: String
}

type ModelInvoicesSummary {
    totalPaid: Float
    totalPaidThisMonth: Float
    due: Float
    arrears: Float
}

type ModelBillsSummary {
    totalPaid: Float
    totalPaidThisMonth: Float
    due: Float
    avgDueDays: Int
}

type PropertyEvent {
    applicantId: String
    date: AWSDateTime
    time: Boolean
    activityName: String
    activityLabel: String
    assignee: User
    status: String
    address: String
    contractName: String
    type: CalendarEventType!
    # either tenancyId, taskId or propertyId depending on type
    parentId: String!
    # either boardId or PropertyId depending on type
    rootId: String!
    reference: String
    documentType: String
    link: String
}

enum CalendarEventType {
    TASK
    TENANCY
    DOCUMENT_EXPIRY
    SCHEDULE_VIEWING
}

enum ContractDashboardSubsection {
    CURRENT
    ARCHIVED
}

type ModelTenancyItem {
    id: ID!
    reference: String
    address: String
    type: TenancyType
    status: TenancyStatus
    primaryTenant: ID
    primaryTenantName: String
    primaryTenantEmail: String
    startDate: AWSDateTime
    endDate: AWSDateTime
    renewalDate: AWSDateTime
    period: Period
    daysRemaining: Int
    amount: Int
    invoiced: Int
    overallInvoiceAmount: Int
    arrears: String
    tenancyPropertyId: ID
    propertyImage: String
}

type DocumentResponse{
    daysLeft: Int
    type: String
    status: String
    expiry: AWSDateTime
    name: String
    permission: Permissions
    key: String
    id: ID
    property: Property
    mimeType: String
    documentPermissionId: String
    documentTenancyId: String
    done: Boolean
}
type DocumentResponseForOtherLists{
    daysLeft: Int
    type: String
    status: String
    expiry: AWSDateTime
    name: String
    permission: Permissions
    key: String
    id: ID
    mimeType: String
    documentPermissionId: String
    documentTenancyId: String
    done: Boolean
    specificId: String
}
type DocumentResponseWrapper{
    propertyList:[DocumentResponse]
    suppliersList:[DocumentResponseForOtherLists]
    clientsList:[DocumentResponseForOtherLists]
    tenantsList:[DocumentResponseForOtherLists]
    otherList:[DocumentResponseForOtherLists]
}

type TenancyStatistics {
    current: Int!
    archive: Int!
    draft: Int!
}

type ModelTenancyList {
    items: [ModelTenancyItem]!
    limit: Int!
    pageCount: Int!
    statistics: TenancyStatistics!
}

type ModelTenantReference {
    items: [ModelTenantReferenceItem]!
    pageCount: Int
    limit: Int
    total: Int
}

type ModelTenantReferenceItem {
    applicationId: String
    reference: String
    type: String
    createdAt: AWSDateTime
    status: String
}

type ModelPropertyList {
    items: [Property]!
    limit: Int!
    pageCount: Int!
    total: Int!
}

type ModelParentPropertyList {
    items: [ParentPropertyEntity]!
    limit: Int!
    pageCount: Int!
    total: Int!
}

type ContractDashboardResponse{
    tenancyId: String
    daysLeft: Int
    deadline: String
    type: TenancyType
    primaryTenant: String
    amount: String
    period: String
    startDate: String
    endDate: String
    status: TenancyStatus
    propertyAddress: String
    tenancyPropertyId: String
    reference: String
}

type ContractDashboardResponseWrapper{
    starting: [ContractDashboardResponse]
    noticeGiven: [ContractDashboardResponse]
    periodic:  [ContractDashboardResponse]
    active: [ContractDashboardResponse]
    archive: [ContractDashboardResponse]
    renewing:  [ContractDashboardResponse]
    draft:  [ContractDashboardResponse]
}

type IntegrationStatusResponse {
    status: String
    tenantName: String
    startDate: String
    remainingCalls: Int
    invoiceLastUpdatedDate: String
    type: String
    updatedAt: String
    emailAddress: String
    authorityType: AuthorityType
    plaidRecordId: String
    userId: String
}

type ExpiringTenanciesResponse {
    items: [ExpiringTenanciesItem]
    limit: Int
    pageCount: Int
    firstVisit: Boolean
}

type ExpiringTenanciesItem {
    propertyAddressLine1: String
    propertyAddressLine2: String
    postCode: String
    zipCode: String
    expiringTenancyStatus: ExpiringTenancyStatus
    endDate: AWSDateTime
    reference: String
    primaryTenantName: String
}

input rentancyLedgerCode {
    name: String
    code: String
}
enum ModelTenancyField {
    REFERENCE
    PROPERTY
    TYPE
    PRIMARY_TENANT
    AMOUNT
    ARREARS
    REMAINS
    STATUS
    START
    END
    PERIOD
    DAYS_LEFT
    DEADLINE
    DONE
}
input BackendDashboardInput {
    organisationId: ID!
    docsExprDocumentType: String
    docsExprDaysRange: Int
    myTasksUserId: ID
    myTasksDaysRange: Int
    onlyTasksType: ID
    contractDaysRange: Int
    contractListType: String
}

input listPropertyApplicationInput {
    organisationId: String!
    propertyId: String
    contactId: String
    status: String
    expectedMoveIn: String
    createdAt: String
    sortBy: String
    prospectId: String
    direction: String
}

input PropertyOwnerInput {
    userId: String
    percentage: Int
}

input CreatePropertyApplicationInput {
    updateBy: String
    applyType: PropertyApplicationApplyType
    createdBy: String
    createdAt: AWSDateTime
    email: String
    additionalMessage: String
    expectedMoveIn: AWSDateTime
    name: String
    moveInType: PropertyApplicationMoveInType
    organisationId: String
    propertyApplicationAssignedToId: String
    phone: String
    propertyApplicationPropertyId: String
    propertyApplicationQuestionAnswerId: String
    propertyApplicationUnitId: String
    specifyFees: String
    status: PropertyApplicationStatus
    updatedAt: AWSDateTime
    prospectId: ID
    tenantId: ID
}
type CommunicationWidgetResponse {
    personal: CommunicationWidgetItem!
    shared: CommunicationWidgetItem!
}
type CommunicationWidgetItem {
    unread: Int!
    processing: Int!
}
type UserUnreadMessagesCountResponse {
    personal: Int!
    shared: Int!
}
type BackendDashboardDocExprResponse {
    date: String
    propertyId: ID
    propertyName: String
}

type BackendDashboardMyTaskResponse {
    date: String
    taskId: ID
    taskName: String
    taskBoardId: String
}

type BackendDashboardTaskResponse {
    archived: String
    deleted: String
}

type backendDashboardContractWrapper{
    property: String
    contract: String
    propertyAddress: String
    date: String
}
type BackendDashboardResponseWrapper {
    docExprList: [BackendDashboardDocExprResponse]
    myTaskList: [BackendDashboardMyTaskResponse]
    taskList: [BackendDashboardTaskResponse]
    contractList: [backendDashboardContractWrapper]
}

input SortTenancyInput {
    field: ModelTenancyField!
    direction: ModelSortDirection!
}

type XeroAccountsResponse {
    type: String
    accounts: [Account]
}

input SearchInvestigationReferenceInput {
    limit: Int
    page: Int
    sortBy: SortInvestigationReferenceInput
    status: InvestigationReferenceStatus
    userId: ID!
    organisationId: ID!
}

input SortInvestigationReferenceInput {
    field: ModelInvestigationReferenceField!
    direction: ModelSortDirection!
}

enum ModelInvestigationReferenceField {
    CREATED_DATE
    STATUS
}

type ModelInvestigationReferenceList {
    items: [InvestigationReference]!
    limit: Int!
    pageCount: Int!
    pageNo: Int!
    total: Int!
}


input ShareReferenceDocumentToLandlordInput {
    investigationReferenceId: ID!
}

input ShareQuestionnaireToApplicantInput {
    applicantId: ID!
    questionLink: String!
}

input SharePropertyToApplicantInput {
    applicantId: ID!
    propertyLink: String!
}

input CreateParentPropertyEntity {
    name: String!
    type: ParentPropertyEntityType!
    addressLine1: String!
    city: String
    postcode: String
    country: String!
    state: String
    notes: String
    splitOwnershipEnabled: Boolean

    propertyIds: [String]!
    primaryLandlordId: String
    landlords: [String]!
    managers: [String]!
    organisationId: String!
    images: [String]
}

input UpdateParentPropertyEntity {
    id: ID!
    name: String
    type: ParentPropertyEntityType
    addressLine1: String
    city: String
    postcode: String
    country: String
    state: String
    notes: String
    splitOwnershipEnabled: Boolean

    propertyIds: [String]
    primaryLandlordId: String
    landlords: [String]
    managers: [String]

    fieldsToUnset: [String]
    images: [String]
}

type GenericOperationResponse {
    object: String
    success: Boolean!
    error: String
}

type InsertPopertyResponse {
    id: String
    success: Boolean!
    error: String
}

input DeleteConversationTagInput {
    organisationId: ID!
    deletedTag: String!
}

input QueryConversationsInput {
    organisationId: ID!
    userId: ID
    fullMatchUserIdFlag: Boolean
    startDate: String
    endDate: String
    unread: Boolean
    recipient:String
    tag: String
    conversationTypes: [String]
    sortType: String
    conversationCategory: String
    memberIds: [ID]
    randomMemberIds: [ID]
    parentPropertyId: ID
    propertyId: ID
    mainEmailAddresses: [String]
    needMemberUserFlag: Boolean
}

type QueryConversationsModel {
    pinConversations: [ConversationModel]
    conversations: [ConversationModel]
}

type ConversationModel {
    conversation: Conversation
    doNotDisturb: Boolean
    memberUsers: [User]
    externalNumber: String
    internalNumber: String
}

input UpdateConversationLinkInput {
    linkId: ID!
    linkType: String!
    conversationId: ID!
    updateType: String!
}

input CheckCreatingConversationInput {
    organisationId: ID!
    userId: ID!
}

type CheckCreatingConversationModel {
    smsFlag: Boolean
    whatsAppFlag: Boolean
    whatsAppAddress: String
    portalFlag: Boolean
    internalFlag: Boolean
    integrationFlag: Boolean
    integrations: [Integration]
}

input ListUsersWithAvatarInput {
    userIds: [ID!]!
}

input CreateContactInput {
    currentOrganisation: String!
    companyName: String!
    fname: String!
    sname: String!
    title: String!
    type: UserType!
    subtype: [String]
    category: String
    emails: [ContactEmailInput]
    phones: [ContactPhoneInput]
    applicantMetaData: ApplicantMetaDataInput
    public: Boolean
}

input ApplicantMetaDataInput {
    status: ApplicantStatus
    # id in type User
    lettingsNegotiators: [ID]
    expectedMoveIn: String
    monthlyIncome: Int
    expectedMaxRent: Int
    expectedMinRent: Int
    expectedMinBedRooms: Int
    expectedMinBathRooms: Float
    expectedCity: String
    expectedState: String
    occupantCount: Int
    extraNotes: String
    allowPets: [String]
    guarantorId: ID
    applicantType: String # lofty leadType
}

input UpdateContactTypeInput {
    id: ID!
    type: UserType!
}

input ContactEmailInput {
    email: String
    type: String
}

input ContactPhoneInput {
    cca2: String
    phone: String
    type: String
    code: String
}

input UpdateContactInput {
    id: ID!
    companyName: String!
    fname: String!
    sname: String!
    title: String!
    type: UserType!
    updatedBy: ID!
    subtype: String
    category: String
    emails: [ContactEmailInput]
    phones: [ContactPhoneInput]
}

input UpdateApplicantMetaDataInput {
    id: ID!
    applicantMetaData: ApplicantMetaDataInput
    updatedBy: ID!
}

type CreateContactResponse {
    success: Boolean
    message: String
    data: User # existed user or new created user.
}

input CompleteHistoryConversationsInput {
    organisationId: ID
}

input AddMemberToConversationInput {
    organisationId: ID!
    currentUserId: ID!
    conversationId: ID!
    memberIds: [ID!]!
}

input DeleteMemberFromConversationInput {
    organisationId: ID!
    currentUserId: ID!
    conversationId: ID!
    memberIds: [ID!]!
}

type GeneralResponse {
    successFlag: Boolean
    errorMessage: String
}

type DepositWidgetResponse {
    toReceiveCount: Int
    toReceiveAmount: String
    toRegisterCount: Int
    toRegisterAmount: String
    toTransferCount: Int
    toTransferAmount: String
    toProcessCount: Int
    toProcessAmount: String
    toReleaseCount: Int
    toReleaseAmount: String
    inDisputeCount: Int
    inDisputeAmount: String

}

type StripePaymentStatusResult {
    id: ID  
    organisationId: ID  
    payStatus: String  
    entityType: String  
    entityId: ID  
}

input ListAuditHistoryInput {
    objectType: ObjectType
    objectId: String
    limit: Int
    page: Int
    startDate: AWSDateTime
    endDate: AWSDateTime
}
type LandlordWidgetResponse {
    landlordsToPay: Int
    amountOwed: String
}

type auditHistoryItem {
    id: String,
    text: String,
    userFname: String,
    userSname: String,
    createdAt: String,
    eventType: String,
    userId: String
}

type auditHistoryResponse {
    itemCount: Int
    items: [auditHistoryItem]
}

type DashboardTaskWidgetResponse {
    totalUnfinishedTasks: Int!
    unassignedTasks: Int!
    overdueTasks: Int!
    dueTodayTasks: Int!
    dueWeekTasks: Int!
}
type DashboardTenancyWidgetResponse {
    upcomingVacatingTenancies: Int!
    tenancyEndDateNotificationDays: Int
    upcomingScheduledMoveIns: Int!
    tenancyStartDateNotificationDays: Int
    upcomingScheduledRenewals: Int!
    tenancyRenewalNotificationDays: Int
}

type DashboardRentWidgetResponse {
    rentDueToday: Int
    rentOverDueCounter: Int
    rentOverDueAmount: Float
    propertiesWithHighestOverDue: [OverDuePropertyResponse]
}

type OverDuePropertyResponse {
    property: String
    amount: Float
}

type ModelApplicantResponseItem {
    name: String!
    status: ApplicantStatus
    lettingNegotiator: String
    applicantEmail: String
    applicantPhone: String
    screeningStatus: InvestigationReferenceStatus
    id: ID!
    cognitoId: String
    cognitoEmail: AWSEmail
    createdDate: AWSDateTime
    invitationSend: Boolean!
    invitationVerified: Boolean!
    roles: [Role]
}

type ModelApplicantResponse {
    items: [ModelApplicantResponseItem]!
    pageCount: Int!
    limit: Int!
}

input ListApplicantsFilters {
    name: String
    statuses: [ApplicantStatus]
    lettingNegotiatorIds: [ID]
    screeningStatuses: [InvestigationReferenceStatus]
}

type DocumentCertificateWidgetItem {
    propertyAddres: String
    documentType: DocumentType!
    expires: AWSDateTime!
}

type DocumentCertificateWidgetResponse {
    expiredGasSafety: Int!
    expiredEPC: Int!
    expiredOther: Int!

    upcomingExpirations: [DocumentCertificateWidgetItem]!
}

type DashboardLeadsWidgetResponse {
    newLeads: Int!
    viewingsScheduledForToday: Int!
}

type ContractorWidgetResponse {
    contractorsToPay: Int!
    amountOwed: String!
}

type ScheduleTasksItem {
    id: String!
    property: String!
    taskType: String!
    frequency: FrequencyItem!
    active: Boolean!
}
type ScheduleTasksResponse {
    items: [ScheduleTasksItem]!
    active: Int!
    notActive: Int!
    total: Int!
}

type AutomatedTasksWrapper {
    propertyEvents: [AutomatedTasks]!
    tenancyEvents: [AutomatedTasks]!
    documentEvents: [AutomatedTasks]!
}

type PropertySummaryResponse {
    documents: [Document]
    expiredDocuments: Int
    expiringDocuments: Int
    validDocuments: Int
    tasks: [Task]
    openedTasks: Int
    primaryLandlord: User
    landlords: [User]
    PendingApproval: Int
    tenants: [User]
    tenancy: Tenancy
}

enum ModelPropertyTaskField {
    DEADLINE
    STATUS
    ASSIGNEE
}

input SortPropertyTasksInput {
    field: ModelPropertyTaskField!
    direction: ModelSortDirection!
}

input DeleteOrganisationTaskChecklistInput {
    id: ID!
}

input DeleteTaskLabelInput {
    id: ID!
}

type LandlordPMsWidgetResponseItem {
    userId: String
    userPhoto: UserPhoto
    name: String
    phone: String
    email: String
}

type LandlordOccupancyRateWidgetResponse {
  occupancyRate: Float
  occupiedCount: Int
  totalCount: Int
}

type LandlordPortfolioResponse {
   items: [LandlordPortfolioItem]
   pageCount: Int
   limit: Int
}

enum ModelSortByDate {
    START_DATE
    END_DATE
}

type LandlordPortfolioItem {
   tenancyId: String!
   tenancyStatus: TenancyStatus
   tenancyStartDate: AWSDateTime
   tenancyEndDate: AWSDateTime
   propertyCity: String
   propertyState: String
   propertyCountry: String
   propertyPostcode: String
   propertyId: String!
   propertyStatus: PropertyStatus!
   propertyAddressLine1: String
   propertyAddressLine2: String
   propertySettings: LandlordPropertySettings
}

input ListWorksOrdersForLandlordInput {
    organisationId: ID!
    userId: ID! # landlordId
    clientApprovalRequired: Boolean # default is true
    clientApprove: Boolean # default is false
    clientAcceptanceStatus: ClientWorksOrderAcceptanceStatus # POC
    not: ListWorksOrdersForLandlordFilters
    limit: Int
    page: Int
}

input ListWorksOrdersForLandlordFilters {
    clientApprove: Boolean
    clientAcceptanceStatus: ClientWorksOrderAcceptanceStatus
}

enum ClientWorksOrderAcceptanceStatus {
    PENDING # default
    APPROVED
    DECLINED
}


type ListWorksOrdersForLandlordResult
{
    limit: Int
    pageCount: Int
    total: Int
    success: Boolean
    data: [WorksOrder]
}

input LandlordPropertySettingsInput {
    landlordUserId: ID
    targetFloat: Float
}

input InsertPropertyInput {
    archived: Boolean
    addressLine1: String!
    addressLine2: String
    addressLine3: String
    reference: String
    type: PropertyType
    zipCode: String
    fullBathrooms: Int
    listingType: String
    councilTax: String
    epc: String
    internetConnection: String
    internalNotes: String
    keys: String
    alarm: String
    parking: String
    phone: AWSPhone
    city: String
    postcode: String
    state: String
    country: String
    status: PropertyStatus
    keyFeatures: [String]
    detailedDescription: String
    description: String
    summary: String
    coverImage: String
    images: [String]
    source: String
    minimumBalance: Float
    createdAt: AWSDateTime
    updatedAt: AWSDateTime
    primaryLandlordId: String
    latitude: Float
    longitude: Float
    marketedLatitude: Float
    marketedLongitude: Float
    splitOwnershipEnabled: Boolean
    certificateNumber: String
    certificateExpirationDate: AWSDateTime
    propertyParentPropertyEntityId: String
    transUnionPropertyId: String
    propertyOrganisationId: ID!
    landlordPropertySettings: LandlordPropertySettingsInput
    openingBalance: Int
    hasUnit: Boolean
    parentId: ID
    createdBy: ID
    updateBy: ID
    name: String
    bedRooms: Int
    bathRooms: Int
    fullBaths: Int
    halfBaths: Int
    sqft: Float
    sqmt: Int
    utilities: [UtilityInput]
    amenities: [String]
    floorPlans: [String]
    videos: [String]
    councilTaxBand: String
    taxEPCRating: String
    yearBuilt: Int
    doneSteps: [String]
    allowPets: [String]
    startDate: AWSDateTime
    leaseTerm: LeaseTerm
    furnishedStatus: FurnishedType
    duration: Int
    monthlyRent: Float
    rentPeriod: Period
    securityDeposit: Int
    insuranceRequired: String
    petsAllowed: String
    otherRules: String
    agentIds: [String]
    applicationFee: Float
    questions: [String]
    customAmenities: [String]
    zillowPublishStatus: Boolean
    hotpadsUrl: String
    hotpadsComments: String
    rightmovePublishStatus: Boolean
    zooplaPublishStatus: Boolean
    listingStatus: ListingStatus
    smokingAllowed: String
    activeUnitsCount: Int
    customType: String
    loftyMetadata: LoftyMetadataInput
    nonAutoSyncTrigger: Boolean
    propertyOwnerId: ID!
    propertyInvoiceAllocationId: ID
    propertyPropertyInvestigationReferenceId: ID
}

input LoftyMetadataInput {
    mlsOrgId: String
    videoLinks: [String]
    listingId: String
    syncDisabled: Boolean
}

input UtilityInput {
    name: String
    supplierId: String
    metric: Float
}

type GenerateLandlordReviewUrlResult {
    success: Boolean
    url: String
}

input PropertyAssetAttachedFileInput {
    key: String!
    name: String!
    type: String!
}

input CreatePropertyAssetInput {
    propertyAssetName: String!

    # only one of these - will be implemented in service
    propertyId: ID
    contractId: ID

    category: PropertyAssetCategory!
    categoryItem: PropertyAssetCategoryItem

    make: String
    model: String

    purchaseDate: AWSDateTime
    warrantyExpiryDate: AWSDate

    relationship: PropertyAssetRelationship!
    status: PropertyAssetStatus!

    room: String
    condition: String
    description: String
    attachedFiles: [PropertyAssetAttachedFileInput!]
}

# 'propertyAssetName', 'category', 'relationship', 'status' are not required
# but will not be updated if null provided - handler validation side
# if any field is set to null - handler will remove it from the record
input UpdatePropertyAssetInput {
    id: ID!

    propertyAssetName: String

    category: PropertyAssetCategory
    categoryItem: PropertyAssetCategoryItem

    make: String
    model: String

    purchaseDate: AWSDateTime
    warrantyExpiryDate: AWSDate

    status: PropertyAssetStatus
    relationship: PropertyAssetRelationship
    propertyId: ID
    contractId: ID

    room: String
    condition: String
    description: String
    attachedFiles: [PropertyAssetAttachedFileInput!]
}

input DeletePropertyAssetInput {
    id: ID!
}

input PropertyInventoryTabResponsePaginationInput {
    limit: Int!
    page: Int!
}

# that would be best solution to have input union type but it's not possible in current version of graphql
# union PropertyAssetFilterValue = PropertyAssetCategory | PropertyAssetStatus | [AWSDateTime]

input PropertyAssetFilterWarrantyExpiryDatesInput {
    from: AWSDate
    to: AWSDate
}

input PropertyAssetsFilterInput {
    category: PropertyAssetCategory
    status: PropertyAssetStatus
    warrantyExpiryDates: PropertyAssetFilterWarrantyExpiryDatesInput
}

enum PropertyAssetSortField {
    PURCHASE_DATE
    WARRANTY_EXPIRY_DATE
}

input PropertyAssetsSortInput {
    field: PropertyAssetSortField!
    direction: ModelSortDirection!
}

type PropertyInventoryTabResponsePagination {
    limit: Int!
    page: Int!
}


type PropertyInventoryTabResponse {
    pageCount: Int!
    total: Int!
    items: [PropertyAsset]
    page: PropertyInventoryTabResponsePagination!
    hasNextPage: Boolean!
    nextPage: PropertyInventoryTabResponsePagination
}

type ExpiringPropertyAssets {
    expired: Int!
    expiring: Int!
    upcoming: [PropertyAsset]
}

input UpdateOrganisationLedgerCodeInput {
    name: String
    displayName: String
    code: String
    accounts: [String]
    includedInPayout: Boolean
    income: Boolean
}

type UpdateOrganisationLedgerCodesResult {
    tenanciesUpdateInProgress: Boolean!
    organisationId: String!
    success: Boolean
}

type UpdateTenancySettingsFeeLedgerCodeResult   {
    success: Boolean!
    message: String!
    organisationId: String!
}

input UpdateTenancySettingsFeeLedgerCodeResultInput {
    organisationId: String!
    success: Boolean!
    message: String!
}

type Query {
    getPropertyPortfolio(userId: ID!, page: Int, limit: Int, filter: String, status: String): LandlordPortfolioResponse
    @function(name: "propertyService-${env}")
    getLandlordPortfolio(page: Int, limit: Int, filter: String, status: String, sortBy: ModelSortByDate, sortDirection: ModelSortDirection): LandlordPortfolioResponse
    @function(name: "propertyService-${env}")
    getPropertySummary(propertyId: String!): PropertySummaryResponse
    @function(name: "propertyService-${env}")
    getLandlordPMsWidget: [LandlordPMsWidgetResponseItem]
    @function(name: "integrationService-${env}")
    getLandlordOccupancyRateWidget: LandlordOccupancyRateWidgetResponse
    @function(name: "integrationService-${env}")
    getScheduleTasks(organisationId: String!, types: [String], propertyIds: [String], active: Boolean, pageSize: Int!, page: Int!): ScheduleTasksResponse
    @function(name: "boardService-${env}")
    getAutomatedTasks(organisationId: String!): AutomatedTasksWrapper
    @function(name: "boardService-${env}")
    getDocumentCertificationWidget(organisationId: String!): DocumentCertificateWidgetResponse
    @function(name: "reportService-${env}")
    getDashboardTaskWidget(organisationId: String!): DashboardTaskWidgetResponse
    @function(name: "boardService-${env}")
    getDashboardTenancyWidget(organisationId: String!): DashboardTenancyWidgetResponse
    @function(name: "reportService-${env}")
    getContractorWidget(organisationId: String!): ContractorWidgetResponse
    @function(name: "integrationService-${env}")
    getDashboardRentWidget(organisationId: String!): DashboardRentWidgetResponse
    @function(name: "integrationService-${env}")
    getDashboardLeadsWidget(organisationId: String!): DashboardLeadsWidgetResponse
    @function(name: "userService-${env}")
    getPropertySummaryPageFinanceDetails(propertyId: String!): PropertySummaryFinanceDetails
    @function(name: "integrationService-${env}")
    listApplicants(limit: Int!, page: Int!, filters: ListApplicantsFilters): ModelApplicantResponse
    @function(name: "userService-${env}")
    listAuditHistory(input: ListAuditHistoryInput!): auditHistoryResponse
    @function(name: "propertyService-${env}")
    listUsersWithAvatar(input: ListUsersWithAvatarInput!): [UserItem]
    @function(name: "userService-${env}")
    checkCreatingConversation(input: CheckCreatingConversationInput!): CheckCreatingConversationModel
    @function(name: "conversationService-${env}")
    queryConversations(input: QueryConversationsInput!): QueryConversationsModel
    @function(name: "conversationService-${env}")
    listWorksOrdersForLandlord(input: ListWorksOrdersForLandlordInput!): ListWorksOrdersForLandlordResult
    @function(name: "conversationService-${env}")
    queryTenanciesByTenant(tenantId: String!): [Tenancy]
    @function(name: "tenancyService-${env}")
    checkEmail(input: checkEmailInput!): CheckEmailResult
    @function(name: "userService-${env}")
    listTemplateLetters(organisationId: String!, contactType: UserType, category: String, letterName: String): [TemplateLetter]
    @function(name: "organisationService-${env}")
    searchAllInvestigationReferences(input: SearchInvestigationReferenceInput!): ModelInvestigationReferenceList
    @function(name: "tenancyService-${env}")
    listXeroAccounts(organisationId: String!): [XeroAccountsResponse] @function(name: "integrationService-${env}") 
    getBackendDashboard(input: BackendDashboardInput): BackendDashboardResponseWrapper @function(name: "reportService-${env}") 
    searchForTaskChecklist(search: String!): [OrganisationTaskChecklist] @function(name: "boardService-${env}") 
    getUser(id: ID!): User @function(name: "userService-${env}") 
    getUserByCognitoId(cognitoId: String!, email: String): User @function(name: "userService-${env}") 
    getOrganisationUserByCognitoId(cognitoId: String!): OrganisationUser @function(name: "userService-${env}") 
    getUserTypes: [UserType] @function(name: "userService-${env}") 
    #    getSessions(ids: [ID!]!): [Session] @function(name: "userService-${env}") 
    findUsers(searchInput: String): [User] @function(name: "userService-${env}") 
    listInvitations(filter: InvitationFilter): [Invitation] @function(name: "userService-${env}") 
    listContacts(filter: ContactsFilter): PaginatedUsers @function(name: "listContacts-${env}") 

    listEligibleOrganisations: [Organisation] @function(name: "organisationService-${env}") 
    listLandlordStatements(landlordId: String, organisationId: String, from: AWSDateTime, to: AWSDateTime): [Statement] @function(name: "userService-${env}") 

    getContractDashboard(organisationId: ID!, tenancyType: TenancyType, durationDays: Int, property: String, status: String, landlord: String, manager: String, dashboardType: ContractDashboardSubsection, sortBy: SortTenancyInput, startDateFrom: AWSDateTime, startDateTo: AWSDateTime, endDateFrom: AWSDateTime, endDateTo: AWSDateTime): ContractDashboardResponseWrapper @function(name: "reportService-${env}") 
    getOrganisationReportingHistory(organisationId: ID!, label: String!, dateFrom: String): [ReportingHistory] @function(name: "organisationService-${env}") 
    getDocuments(organisationId: ID!, documentType: DocumentType, dateRange: Int, propertyId: String, propertyManagerId: String, propertyLandlordId: String): DocumentResponseWrapper @function(name: "tenancyService-${env}") 
    listDocumentTemplatesTypes(organisationId: String!): [DocumentTemplateTypes] @function(name: "organisationService-${env}") 

    listRooms: [Room] @function(name: "listRooms-${env}") 
    getCategories(roomId: String): [Category] @function(name: "getCategories-${env}") 
    getSubCategories(roomId: String, categoryId: String): [SubCategory] @function(name: "getSubCategories-${env}") 
    getPublicVapidKey: String @function(name: "webPushSubscriptionService-${env}") 
    search(input: SearchInput!): SearchResult
    @function(name: "esService-${env}")
    searchForUsers(input: SearchUserInput!): SearchUsersResult
    @function(name: "esService-${env}")
    listDocuments: [Document]
    @function(name: "attachmentsService-${env}")
    listConversationAttachments(conversationId: String!): [Document]
    @function(name: "attachmentsService-${env}")
    listBoards(organisationId: String!): [Board] @function(name: "boardService-${env}")
    listBoardColumns(boardId: String!, propertyIds: [String]): [Column] @function(name: "boardService-${env}")
    listMessages(conversationId: String!, sortDirection: ModelSortDirection, limit: Int, nextToken: String): ModelMessageConnection
    @function(name: "attachmentsService-${env}")
    listSupplierOrganisations(organisationId: String!, limit: Int!, search: String, nextToken: String): ModelSupplierOrganisationConnection
    @function(name: "suppliersService-${env}")
    listTaskLabels(organisationId: String!): [TaskLabel] @function(name: "boardService-${env}")
    listArchiveTasks: [Task] @function(name: "boardService-${env}")
    listPropertyEvents(propertyId: String!, from: AWSDateTime, to: AWSDateTime, limit: Int): ModelPropertyEvents
    @function(name: "propertyService-${env}")
    listParentPropertyEvents(parentPropertyId: String!, from: AWSDateTime, to: AWSDateTime, limit: Int): ModelPropertyEvents
    @function(name: "propertyService-${env}")
    listParentPropertyLinkableProperties(organisationId: String!, owners: [PropertyOwnerInput]!, limit: Int!, page: Int! sortBy: SortPropertyInput, filter: String): ModelPropertyList
    @function(name: "propertyService-${env}")
     
    listOrganisationEvents(organisationId: String!, from: AWSDateTime, to: AWSDateTime, types: [CalendarEventType]): ModelPropertyEvents
    @function(name: "tenancyService-${env}")
    
    listPropertyStatuses(region: String): [PropertyStatus]
    @function(name: "tenancyService-${env}")
    
    generateReference(organisationId: String!, type: String!): String @function(name: "propertyService-${env}") 
    validReference(organisationId: String!, itemId: String, reference: String!, type: String!): Boolean @function(name: "tenancyService-${env}") 
    listTenancies(returnRentBalance: Boolean, filter: String, type: TenancyType, statuses: [TenancyStatus], startDateFrom: AWSDateTime, startDateTo: AWSDateTime, endDateFrom: AWSDateTime, endDateTo: AWSDateTime, renewalDateFrom: AWSDateTime, renewalDateTo: AWSDateTime, activeTenancies: Boolean, sortBy: SortTenancyInput, limit:Int, page: Int): ModelTenancyList
    @function(name: "tenancyService-${env}")
    listExpiringTenancies(filter: ExpiringTenanciesFilter, page: Int!, limit: Int!): [ExpiringTenanciesResponse]
    @function(name: "expiringTenanciesService-${env}") 
    listTenantReferences(organisationId: String!, tenantId: String!, status: String, filter: String, page: Int, limit: Int): ModelTenantReference
    @function(name: "tenancyService-${env}")
    listTenanciesForProperty(propertyId: String!): [Tenancy]
    @function(name: "tenancyService-${env}")
    listTenanciesForOrganisation(organisationId: String!, allTenancies: Boolean, limit: Int, page: Int): [Tenancy]
    @function(name: "tenancyService-${env}")
    listArchivedTenancies(organisationId: String!): [Tenancy]
    @function(name: "tenancyService-${env}")
    listProperties(input: ListPropertyInput!): ModelPropertyList
    @function(name: "tenancyService-${env}")
    matchInterestedProperties(input: MatchInterestedPropertyInput!): [Property]
    @function(name: "tenancyService-${env}")
    listScheduleViewingByApplicantId(applicantId: String!): [ApplicantScheduleViewingModel]
    @function(name: "propertyService-${env}")
    getApplicantById(applicantId: String!): ApplicantModel
    @function(name: "usersService-${env}")
    listParentProperties(input: ListParentPropertyInput!): ModelParentPropertyList
    @function(name: "tenancyService-${env}")
    listParentChildProperties(parentPropertyEntityId: ID!, filter: String, type: String, status: PropertyStatus, sortBy: ParentPropertyPropertiesSortField, sortDirection: ModelSortDirection, limit: Int!, page: Int!): ModelPropertyList
    @function(name: "propertyService-${env}")
    listBankAccounts(organisationId: String!): ModelBankAccounts
    @function(name: "integrationService-${env}")
    
    listBankTransactions(organisationId: String!, accountId: String!, limit: Int!, page: Int!, sortBy: String): ModelBankTransactions
    @function(name: "integrationService-${env}")
    
    listInvoices(organisationId: String!, type: InvoiceType, limit: Int!, page: Int!, sortBy: SortInvoiceInput, propertyId: String, status: String, from: String, filter: String, inArrears: Boolean, inWarning: Boolean, ignoreClientBalance: Boolean, startTime: AWSDateTime, endTime: AWSDateTime): ModelInvoices
    @function(name: "integrationService-${env}")
    
    getInvoicesSummary(organisationId: String!): ModelInvoicesSummary
    @function(name: "integrationService-${env}")
    
    getBillsSummary(organisationId: String!): ModelBillsSummary
    @function(name: "integrationService-${env}")
    
    listPropertyExpenses(propertyId: String!, limit: Int!, page: Int!): ModelPropertyInvoiceExpenses
    @function(name: "integrationService-${env}")
    
    listTenancyIncomes(tenancyId: String!, limit: Int!, page: Int!): ModelPropertyInvoiceExpenses
    @function(name: "integrationService-${env}")
    
    getInvoice(id: ID!): Invoice
    @function(name: "integrationService-${env}")
    
    getIntegrationStatus(organisationId: String!, type: IntegrationService): IntegrationStatusResponse
    @function(name: "integrationService-${env}")
    
    getAllIntegrationStatus(organisationId: String!, userId: String!): [IntegrationStatusResponse]
    @function(name: "integrationService-${env}")
    
    listLandlordBalances(organisationId: String!, limit: Int!, page: Int!, status: LandlordBillStatus, filter: String, inWarning: Boolean): ModelLandlordBill
    @function(name: "integrationService-${env}")
    
    getCommunicationWidget(orgId: String!, userId: String!): CommunicationWidgetResponse
    @function(name: "conversationService-${env}")
    getUserUnreadMessagesCount(orgId: String!, userId: String!): UserUnreadMessagesCountResponse
    @function(name: "conversationService-${env}")
    getDepositWidget: DepositWidgetResponse
    @function(name: "integrationService-${env}")
    listLandlordBalancesGroupedByLandlord(organisationId: String!, limit: Int!, page: Int!, from: String!, to: String!, landlordId: String, groupBy: String): ModelLandlordBill
    @function(name: "integrationService-${env}")
    getLandlordWidget(organisationId: String!, limit: Int!, page: Int!, from: String!, to: String!, landlordId: String, groupBy: String): LandlordWidgetResponse
    @function(name: "integrationService-${env}")
    listStatements(organisationId: String!, propertyId: String, limit: Int!, page: Int!, sortBy: ModelSortDirection): ModelStatements
    @function(name: "integrationService-${env}")
    
    listBoardTasks(boardId: String!, propertyIds: [String], status: TaskStatus, assignee: ID): [Task]
    @function(name: "boardService-${env}")
    listPropertyTasks(organisationId: String!, propertyId: String!, status: TaskStatus!, sortBy: SortPropertyTasksInput!, page: Int!, limit: Int!): [Task]
    @function(name: "boardService-${env}")
    
    listPropertyScheduledTasks(organisationId: String!, propertyId: String!, active: Boolean, type: String, limit: Int!, page: Int!): [TaskScheduler]
    @function(name: "boardService-${env}")
    

    getPropertiesByIds(input: GetPropertiesByIdsInput!): [Property]
    @function(name: "propertyService-${env}")
    getUnitsByParentId(input: GetUnitsByParentIdInput!): [Property]
    @function(name: "propertyService-${env}")
    multiFilesDownload(files: [String]!): String
    @function(name: "multiFilesDownload-${env}")
    getOrganisationPropertyApplications(input: listPropertyApplicationInput): [PropertyApplication]

    @function(name: "tenancyService-${env}")
    
    doesAddressAlreadyExist(input: CheckExistingAddressRequest!): Boolean!
    @function(name: "propertyService-${env}")
    reportApplicants(input: ReportApplicantInput!): [ReportApplicant]
    @function(name: "reportApplicants-${env}")
    getPropertyAsset(id: ID!): PropertyAsset @function(name: "inventoryService-${env}") 
    getPropertyAssets(propertyId: ID!, paginate: PropertyInventoryTabResponsePaginationInput!, sort: PropertyAssetsSortInput!, filters: PropertyAssetsFilterInput): PropertyInventoryTabResponse @function(name: "inventoryService-${env}") 
    expiringPropertyAssets: ExpiringPropertyAssets @function(name: "inventoryService-${env}") 

    listUserPreferences(filter: ModelUserPreferencesFilterInput, limit: Int): ModelUserPreferencesConnection @function(name: "userService-${env}") 
    listSupplierPropertiesBySupplierId(supplierId: String!, limit: Int!, nextToken: String): ModelSupplierPropertyConnection @function(name: "supplierPropertyService-${env}") 
    listSupplierPropertiesByPropertyId(propertyId: String!, limit: Int!, nextToken: String): ModelSupplierPropertyConnection @function(name: "supplierPropertyService-${env}") 
    getSupplierProperty(supplierId: String!, propertyId: String!): SupplierProperty @function(name: "supplierPropertyService-${env}") 
}

type Mutation {
    deleteOrganisationTaskChecklist(input: DeleteOrganisationTaskChecklistInput!): OrganisationTaskChecklist @function(name: "boardService-${env}") 
    deleteTaskLabel(input: DeleteTaskLabelInput!): TaskLabel @function(name: "boardService-${env}") 

    updateStripePaymentStatus(id: String!, status: String!): StripePaymentStatusResult
    @function(name: "rentancy-core-${env}-stripe-oauth")
    
    updateContact(input: UpdateContactInput!): User
    @function(name: "userService-${env}")
    updateContactType(input: UpdateContactTypeInput!): User
    @function(name: "userService-${env}")
    updateApplicantMetaData(input: UpdateApplicantMetaDataInput!): User
    @function(name: "userService-${env}")
    addMemberToConversation(input: AddMemberToConversationInput!): GeneralResponse
    @function(name: "conversationService-${env}")
    deleteMemberFromConversation(input: DeleteMemberFromConversationInput!): GeneralResponse
    @function(name: "conversationService-${env}")
    completeHistoryConversations(input: CompleteHistoryConversationsInput!): Boolean
    @function(name: "conversationService-${env}")
    updateConversationLink(input: UpdateConversationLinkInput!): Boolean
    @function(name: "conversationService-${env}")
    deleteConversationTag(input: DeleteConversationTagInput!): Boolean
    @function(name: "conversationService-${env}")
    updateSplitOwnership(propertyId: String!, owners: [PropertyOwnerInput]!, parentProperty: Boolean!): GenericOperationResponse
    @function(name: "propertyService-${env}")
    createParentPropertyEntity(input: CreateParentPropertyEntity!): GenericOperationResponse
    @function(name: "propertyService-${env}")
    updateParentPropertyEntity(input: UpdateParentPropertyEntity!): GenericOperationResponse
    @function(name: "propertyService-${env}")
    archiveParentPropertyEntity(id: ID!): GenericOperationResponse
    @function(name: "propertyService-${env}")
    shareReferenceDocumentToLandlord(input: ShareReferenceDocumentToLandlordInput!): Boolean
    @function(name: "rentancy-core-${env}-share-reference-document-landlord")
    shareQuestionnaireToApplicant(input: ShareQuestionnaireToApplicantInput!): Boolean
    @function(name: "rentancy-core-${env}-share-questionnaire-applicant")
    sharePropertyToApplicant(input: SharePropertyToApplicantInput!): Boolean
    @function(name: "rentancy-core-${env}-share-property-applicant")
    updateOrganisationExpiredFlag(input: UpdateOrganisationExpiredFlagInput!): Boolean
    @function(name: "organisationService-${env}")
    updateUserType(input: UpdateUserTypeInput!): Boolean
    @function(name: "userService-${env}")
    initRoom: Boolean
    @function(name: "reportService-${env}")
    insertProperty(input: InsertPropertyInput!): InsertPopertyResponse
    @function(name: "propertyService-${env}")
    addPropertyApplication(input: CreatePropertyApplicationInput): PropertyApplication
    @function(name: "propertyService-${env}")
    deleteDocumentTemplateType(documentTypeId: String!, organisationId: String!): Boolean
    @function(name: "tenancyService-${env}")
    addOrganisationMail(organisationId: String!, organisationMail: String!): Boolean
    @function(name: "organisationService-${env}")
    resolveConversation(convId: String!): Boolean
    @function(name: "activityService-${env}")
    uploadTenancyFile(propertyId: String, tenancyFileUrl: String!): Boolean
    @function(name: "tenancyService-${env}")
    
    uploadPropertyFile(organisationId: String!, propertyFileUrl: String!): Boolean
    @function(name: "tenancyService-${env}")
    uploadParentPropertyFile(organisationId: String!, parentPropertyFileUrl: String!): Boolean
    @function(name: "tenancyService-${env}")
    
    uploadContactFile(organisationId: String!, contactFileUrl: String!): Boolean
    @function(name: "tenancyService-${env}")
    
    updateAllPropertyUnitStatus(propertyId: String!, status: String!): Boolean
    @function(name: "updatePropertyUnitStatus-${env}")
    
    addMembersToProperty(id: String!, members: [String!]!): Boolean
    @function(name: "propertyService-${env}")
    
    addFloatsToProperty(input: AddFloatsToPropertyInput!): Boolean
    @function(name: "propertyService-${env}")
    
    setMembersToProperty(id: String!, members: [String!]!): Boolean
    @function(name: "propertyService-${env}")
    
    removeMemberFromProperty(id: String!, memberId: String!): Boolean
    @function(name: "propertyService-${env}")
    
    addManagersToProperty(id: String!, members: [String!]!): Boolean
    @function(name: "propertyService-${env}")
    
    setManagersToProperty(id: String!, members: [String!]!): Boolean
    @function(name: "propertyService-${env}")
    
    removeManagerFromProperty(id: String!, memberId: String!): Boolean
    @function(name: "propertyService-${env}")
    

    createTenancy(input: CreateTenancyInput!): Tenancy
    @function(name: "tenancyService-${env}")
    
    updateTenancy(input: UpdateTenancyInput!): Tenancy
    @function(name: "tenancyService-${env}")
    
    moveOutTenancy(input: MoveOutTenancyInput!): Boolean
    @function(name: "tenancyService-${env}")
    
    removeMemberFromTenancy(id: String!, memberId: String!): Boolean
    @function(name: "tenancyService-${env}")
    
    copyTenancy(originalTenancyId: ID!): Tenancy
    @function(name: "elasticService-${env}")
    
    deleteSystemUser(id: String!, deleteOrganisationIfAdmin: Boolean): Boolean

    @function(name: "userService-${env}")
    
    updateUserSession(userId: String): String @function(name: "updateUserSession-${env}") 
    disableUser(id: ID!): Boolean
    @function(name: "userService-${env}")
    
    enableUser(id: ID!): Boolean
    @function(name: "userService-${env}")
    
    changeUserRole(id: String, role: Role!): User
    @function(name: "userService-${env}")
    addRoleToUser(id: String, role: Role!): User
    @function(name: "userService-${env}")
    removeRoleFromUser(id: String, role: Role!): User
    @function(name: "userService-${env}")
    createInvitation(input: CreateInvitationInput!): String
    @function(name: "createInvitation-${env}")
    
    createInvitations(input: [CreateInvitationInput!]!): String
    @function(name: "createInvitation-${env}")
    

    verifyInvitation(userId: String!, organisationId: String!): Boolean
    @function(name: "createInvitation-${env}")
    createGroupChat(parentId: String!, parentType: ParentType!, members: [String!]!): String
    @function(name: "mutateGroupChat-${env}")
    #    @auth(rules: [{allow: groups, groups: ["AGENT", "ADMIN_AGENT", "HELPDESK"]}])
    createWhatsAppConversation(whatsAppAddress: String!, whatsAppUserName: String!): String
    @function(name: "mutateGroupChat-${env}")
    #    @auth(rules: [{allow: groups, groups: ["AGENT", "ADMIN_AGENT", "HELPDESK"]}])
    createWhatsAppConversationV2(whatsAppAddress: String!, whatsAppUserName: String!, subject: String, contactType: ContactType, conversationCategory: ConversationCategory): String
    @function(name: "mutateGroupChat-${env}")
    #    @auth(rules: [{allow: groups, groups: ["AGENT", "ADMIN_AGENT", "HELPDESK"]}])
    createSmsConversation(phoneNumber: String!, smsUserName: String!, subject: String, contactType: ContactType, conversationCategory: ConversationCategory): String
    @function(name: "mutateGroupChat-${env}")
    #    @auth(rules: [{allow: groups, groups: ["AGENT", "ADMIN_AGENT", "HELPDESK"]}])
    createEmailConversation(emailUserName: String, subject: String, contactType: ContactType, conversationCategory: ConversationCategory): String
    @function(name: "mutateGroupChat-${env}")
    #    @auth(rules: [{allow: groups, groups: ["AGENT", "ADMIN_AGENT", "HELPDESK"]}])
    createConversation(associatedUserId: String!): String
    @function(name: "mutateGroupChat-${env}")
    createConversationV2(associatedUserId: String!, subject: String): String
    @function(name: "mutateGroupChat-${env}")
    createGroupChatV2(parentId: String!, parentType: ParentType!, members: [String!]!, subject: String, contactType: ContactType, conversationCategory: ConversationCategory, conversationType: ConversationType): String
    @function(name: "mutateGroupChat-${env}")
    #    @auth(rules: [{allow: groups, groups: ["AGENT", "ADMIN_AGENT", "HELPDESK"]}])
    removeUsersFromGroupChat(convoId: String!, membersToRemove: [String!]!): String
    @function(name: "mutateGroupChat-${env}")
    #    @auth(rules: [{allow: groups, groups: ["AGENT", "ADMIN_AGENT", "HELPDESK"]}])
    registerSubscription(userId: String!, subscription: WebPushSubscriptionInput!): String
    @function(name: "webPushSubscriptionService-${env}")
    addUsersToGroupChat(convoId: String!, membersToAdd: [String!]!): String
    @function(name: "mutateGroupChat-${env}")
    #    @auth(rules: [{allow: groups, groups: ["AGENT", "ADMIN_AGENT", "HELPDESK"]}])

    createWorkspace(organisationName: String!, payoutVersion: PayoutVersion, utmCustomerAttributionSource: String): String
    @function(name: "createOrganisation-${env}")
    createConversationAttachment(conversationId: String!, document: AttachmentInput!, cognitoUserId: String): String
    @function(name: "attachmentsService-${env}")
    
    deleteConversationAttachment(conversationId: String!, document: DeleteAttachmentInput!): Boolean
    @function(name: "attachmentsService-${env}")
    registerUserDevice(authToken: String!, deviceId: String!): Boolean
    @function(name: "webPushSubscriptionService-${env}")
    deleteColumn(id: ID!): Column @function(name: "boardService-${env}")
    deleteTask(id: ID!): Task @function(name: "boardService-${env}")
    archiveTask(id: ID!): Task @function(name: "boardService-${env}")
    unarchiveTask(id: ID!): Task @function(name: "boardService-${env}")
    updateColumn(input: UpdateColumnInput!): Column! @function(name: "boardService-${env}")
    updateTask(input: UpdateTaskInput!): Task! @function(name: "boardService-${env}")
    createTask(input: CreateTaskInput!, authorId: ID): Task @function(name: "boardService-${env}")  
    createColumn(input: CreateColumnInput!): Column @function(name: "boardService-${env}")

    updateIntegrationStatus(organisationId: String!, type: IntegrationService!, status: IntegrationStatus!): Integration
    @function(name: "integrationService-${env}")
    
    updateIntegrationStatusById(id: String!, status: IntegrationStatus!): Integration
    @function(name: "integrationService-${env}")
    
    updateIntegrationDate(organisationId: String!, type: IntegrationService!, startDate: AWSDateTime!): Integration
    @function(name: "integrationService-${env}")
    
    updateAllowedXeroContactGroups(organisationId: String!, groups: [String]): Integration
    @function(name: "integrationService-${env}")
    
    approveInvoices(invoiceIds: [String]!): Boolean
    @function(name: "integrationService-${env}")
    

    generateLandlordReviewUrl(tenancyId: ID!, organisationId: ID!): GenerateLandlordReviewUrlResult
    @function(name: "expiringTenanciesService-${env}")
    #    updateUserMetadata(id: String!, metadata: UserMetadataInput!): User
    #    @function(name: "userService-${env}")
    #    
    #    

    switchOrganisation(newOrganisationId: String!): String @function(name: "organisationService-${env}") 
    addUserToOrganisation(userId: String!, newOrganisationId: String!): [Organisation] @function(name: "organisationService-${env}") 
    removeUserFromOrganisation(userId: String!, organisationId: String!): [Organisation] @function(name: "organisationService-${env}") 

    createPropertyAsset(input: CreatePropertyAssetInput!): PropertyAsset @function(name: "inventoryService-${env}") 
    updatePropertyAsset(input: UpdatePropertyAssetInput!): PropertyAsset @function(name: "inventoryService-${env}") 
    deletePropertyAsset(input: DeletePropertyAssetInput!): Boolean @function(name: "inventoryService-${env}") 
    updateOrganisationLedgerCodes(input: [UpdateOrganisationLedgerCodeInput!]!): UpdateOrganisationLedgerCodesResult! @function(name: "organisationService-${env}") 
    notifyOfTenancySettingsFeeLedgerCodeUpdateResult(input: UpdateTenancySettingsFeeLedgerCodeResultInput!): UpdateTenancySettingsFeeLedgerCodeResult @function(name: "organisationService-${env}")  

    createUserPreferences(input: CreateUserPreferencesInput!): UserPreferences @function(name: "userService-${env}") 
    updateUserPreferences(input: UpdateUserPreferencesInput!): UserPreferences @function(name: "userService-${env}") 

    syncSupplierPropertiesWithSuppliers(input: SyncSupplierPropertiesWithSuppliersInput!): ModelSupplierPropertyConnection @function(name: "supplierPropertyService-${env}") 
    syncSupplierPropertiesWithProperties(input: SyncSupplierPropertiesWithPropertiesInput!): ModelSupplierPropertyConnection @function(name: "supplierPropertyService-${env}") 
}

type Subscription {
    onStripePaymentChanged(organisationId: ID!): StripePaymentStatusResult
    @aws_subscribe(mutations: ["updateStripePaymentStatus"])
    
    onOrganisationUpdate(id: ID!): Organisation
    @aws_subscribe(mutations: ["updateOrganisation"])
    onOrganisationStripeChargeEvent(organisationId: ID!): OrganisationStripeCharges
    @aws_subscribe(mutations: ["createOrganisationStripeCharge"])
    onRemoveUserFromOrganisation(userId: ID!): [Organisation]
    @aws_subscribe(mutations: ["removeUserFromOrganisation"])
    onTenancyChanged(id: ID!): Tenancy
    @aws_subscribe(mutations: ["updateTenancy", "createTenancy"])

    onTenancyMemberChanged(id: ID!): Boolean
    @aws_subscribe(mutations: ["removeMemberFromTenancy"])
    onUserCreation: User
    @aws_subscribe(mutations: ["createUser"])
    onUpdateConvo(id: ID!): Conversation
    @aws_subscribe(mutations: ["updateConvo"])
    onCreateConvoLink(convoLinkUserId: ID!): ConvoLink
    @aws_subscribe(mutations: ["createConvoLink"])
    onDeleteConvoLink(convoLinkUserId: ID!): ConvoLink
    @aws_subscribe(mutations: ["deleteConvoLink"])
    onCreateMessage(messageConversationId: ID!): Message
    @aws_subscribe(mutations: ["createMessage"])
    onCreateTypingStatusMessage(messageConversationId: ID!): TypingStatusMessage
    @aws_subscribe(mutations: ["createTypingStatusMessage"])
    onUpdateMessage(messageConversationId: ID!): Message
    @aws_subscribe(mutations: ["updateMessage"])
    onUpdateUserSession(userId: String): String
    @aws_subscribe(mutations: ["updateUserSession"])
    onDocumentChanged(documentPropertyId: String!): Document
    @aws_subscribe(mutations: ["createDocument", "updateDocument", "deleteDocument"])
    onTenancyDocumentChanged(documentTenancyId: String!): Document
    @aws_subscribe(mutations: ["createDocument", "updateDocument", "deleteDocument"])
    onSupplierDocumentChanged(documentSupplierOrganisationId: String!): Document
    @aws_subscribe(mutations: ["createDocument", "updateDocument", "deleteDocument"])
    onUserDocumentChanged(documentOrganisationId: String!, documentUserId: String!): Document
    @aws_subscribe(mutations: ["createDocument", "updateDocument", "deleteDocument"])
    onUpdateBoard(id: ID!): Board
    @aws_subscribe(mutations: ["updateBoard"])
    onChangeColumn(columnBoardId: ID!): Column
    @aws_subscribe(mutations: ["createColumn", "updateColumn", "deleteColumn"])
    onChangeTask(taskBoardId: ID!): Task
    @aws_subscribe(mutations: ["createTask", "updateTask", "deleteTask", "archiveTask", "unarchiveTask"])
    onChangeUser(id: ID!): User
    @aws_subscribe(mutations: ["updateUser", "changeUserRole"])
    onIntegrationStatusChanged(organisationId: ID!): Integration
    @aws_subscribe(mutations: ["updateIntegrationStatus", "updateIntegrationStatusById"])
    onIntegrationDateChanged(organisationId: ID!): Integration
    @aws_subscribe(mutations: ["updateIntegrationDate"])
    #    onUpdateUserMetadata(id: String!, currentOrganisation: String!): User
    #    @aws_subscribe(mutations: ["updateUserMetadata"])
    onChangeProperty(propertyOrganisationId: ID!): Property
    @aws_subscribe(mutations: ["updateProperty"])
    onUpdateProperty(id: ID!): Property @aws_subscribe(mutations: ["updateProperty"])
    onProblemCardChanged(problemCardPropertyId: String!): ProblemCard
    @aws_subscribe(mutations: ["createProblemCard", "updateProblemCard"])
    onOrganisationSupplierChanged(supplierOrganisationOrganisationId: ID!): SupplierOrganisation
    @aws_subscribe(mutations: ["createSupplierOrganisation", "updateSupplierOrganisation", "deleteSupplierOrganisation"])
    onSupplierChanged(id: ID!): SupplierOrganisation
    @aws_subscribe(mutations: ["createSupplierOrganisation", "updateSupplierOrganisation", "deleteSupplierOrganisation"])
    onUpdateOrganisationLedgerCodes(organisationId: String!): UpdateTenancySettingsFeeLedgerCodeResult
    @aws_subscribe(mutations: ["notifyOfTenancySettingsFeeLedgerCodeUpdateResult"])
}