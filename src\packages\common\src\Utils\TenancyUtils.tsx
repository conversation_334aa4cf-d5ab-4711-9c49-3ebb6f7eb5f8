import { CSSProperties } from "react";
import { User } from "@rentancy/common";

import { statusColours } from "@rentancy/ui/src/Layout/theme";

export interface InternalTenancyStatus {
  [status: string]: {
    name: string;
    color: CSSProperties;
    mainChip: CSSProperties;
    chip: CSSProperties;
    priority: number;
  };
}

export const TENANCY_STATUS: InternalTenancyStatus = {
  UPCOMING: {
    name: "Upcoming",
    color: {
      color: statusColours.canceled,
    },
    mainChip: {
      color: statusColours.canceled,
      borderColor: statusColours.canceled,
    },
    chip: {
      color: statusColours.canceled,
      borderColor: statusColours.canceled,
    },
    priority: 110,
  },
  CANCELLED: {
    name: "Cancelled",
    color: {
      color: statusColours.canceled,
    },
    mainChip: {
      color: statusColours.canceled,
      borderColor: statusColours.canceled,
    },
    chip: {
      color: statusColours.canceled,
      borderColor: statusColours.canceled,
    },
    priority: 110,
  },
  VACATED: {
    name: "Vacated",
    color: {
      color: statusColours.draft,
    },
    mainChip: {
      color: statusColours.draft,
      borderColor: statusColours.draft,
    },
    chip: {
      color: statusColours.draft,
      borderColor: statusColours.draft,
    },
    priority: 110,
  },
  NOT_INSTRUCTED: {
    name: "Not Instructed",
    color: {
      color: statusColours.draft,
    },
    mainChip: {
      color: statusColours.draft,
      borderColor: statusColours.draft,
    },
    chip: {
      color: statusColours.draft,
      borderColor: statusColours.draft,
    },
    priority: 110,
  },
  LET_EXTERNALLY: {
    name: "Let Externally",
    color: {
      color: statusColours.draft,
    },
    mainChip: {
      color: statusColours.draft,
      borderColor: statusColours.draft,
    },
    chip: {
      color: statusColours.draft,
      borderColor: statusColours.draft,
    },
    priority: 110,
  },
  VACATING: {
    name: "Vacating",
    color: {
      color: statusColours.checkout,
    },
    mainChip: {
      color: statusColours.checkout,
      borderColor: statusColours.checkout,
    },
    chip: {
      color: statusColours.checkout,
      borderColor: statusColours.checkout,
    },
    priority: 110,
  },
  PERIODIC: {
    name: "Periodic",
    color: {
      color: statusColours.checkin,
    },
    mainChip: {
      color: statusColours.checkin,
      borderColor: statusColours.checkin,
    },
    chip: {
      color: statusColours.checkin,
      borderColor: statusColours.checkin,
    },
    priority: 110,
  },
  EXPIRING: {
    name: "Expiring",
    color: {
      color: statusColours.checkin,
    },
    mainChip: {
      color: statusColours.checkin,
      borderColor: statusColours.checkin,
    },
    chip: {
      color: statusColours.checkin,
      borderColor: statusColours.checkin,
    },
    priority: 110,
  },
  EXPIRED: {
    name: "Expired",
    color: {
      color: statusColours.checkin,
    },
    mainChip: {
      color: statusColours.checkin,
      borderColor: statusColours.checkin,
    },
    chip: {
      color: statusColours.checkin,
      borderColor: statusColours.checkin,
    },
    priority: 110,
  },
  NOTICE_GIVEN: {
    name: "Notice Given",
    color: {
      color: statusColours.checkout,
    },
    mainChip: {
      color: statusColours.checkout,
      borderColor: statusColours.checkout,
    },
    chip: {
      color: statusColours.checkout,
      borderColor: statusColours.checkout,
    },
    priority: 110,
  },
  ASSURED: {
    name: "Assured",
    color: {
      color: statusColours.draft,
    },
    mainChip: {
      color: statusColours.draft,
      borderColor: statusColours.draft,
    },
    chip: {
      color: statusColours.draft,
      borderColor: statusColours.draft,
    },
    priority: 110,
  },
  ARCHIVED: {
    name: "Archived",
    color: {
      color: statusColours.draft,
    },
    mainChip: {
      color: statusColours.draft,
      borderColor: statusColours.draft,
    },
    chip: {
      color: statusColours.draft,
      borderColor: statusColours.draft,
    },
    priority: 110,
  },
  DRAFT: {
    name: "Draft",
    color: {
      color: statusColours.draft,
    },
    mainChip: {
      color: statusColours.draft,
      borderColor: statusColours.draft,
    },
    chip: {
      color: statusColours.draft,
      borderColor: statusColours.draft,
    },
    priority: 110,
  },
  APPLICATION: {
    name: "Application",
    color: {
      color: statusColours.application,
    },
    mainChip: {
      color: statusColours.application,
      borderColor: statusColours.application,
    },
    chip: {
      color: statusColours.application,
      borderColor: statusColours.application,
    },
    priority: 80,
  },
  REFERENCING: {
    name: "Referencing",
    color: {
      color: statusColours.referencing,
    },
    mainChip: {
      color: statusColours.referencing,
      borderColor: statusColours.application,
    },
    chip: {
      color: statusColours.referencing,
      borderColor: statusColours.application,
    },
    priority: 120,
  },
  RTR_CHECK: {
    name: "RtR Check",
    color: {
      color: statusColours.rtrCheck,
    },
    mainChip: {
      color: statusColours.rtrCheck,
      borderColor: statusColours.application,
    },
    chip: {
      color: statusColours.rtrCheck,
      borderColor: statusColours.application,
    },
    priority: 90,
  },
  CONTRACT: {
    name: "Contract",
    color: {
      color: statusColours.contract,
    },
    mainChip: {
      color: statusColours.contract,
      borderColor: statusColours.application,
    },
    chip: {
      color: statusColours.contract,
      borderColor: statusColours.application,
    },
    priority: 60,
  },
  TRANSFER_MONEY: {
    name: "Transfer £",
    color: {
      color: statusColours.transferMoney,
    },
    mainChip: {
      color: statusColours.transferMoney,
      borderColor: statusColours.transferMoney,
    },
    chip: {
      color: statusColours.transferMoney,
      borderColor: statusColours.transferMoney,
    },
    priority: 130,
  },
  CHECK_IN: {
    name: "Check-In",
    color: {
      color: statusColours.checkin,
    },
    mainChip: {
      color: statusColours.checkin,
      borderColor: statusColours.checkin,
    },
    chip: {
      color: statusColours.checkin,
      borderColor: statusColours.checkin,
    },
    priority: 100,
  },
  HOLDING_OVER: {
    name: "Holding Over",
    color: {
      color: statusColours.checkin,
    },
    mainChip: {
      color: statusColours.checkin,
      borderColor: statusColours.checkin,
    },
    chip: {
      color: statusColours.checkin,
      borderColor: statusColours.checkin,
    },
    priority: 100,
  },
  ACTIVE: {
    name: "Active",
    color: {
      color: statusColours.active,
    },
    mainChip: {
      color: statusColours.active,
      borderColor: statusColours.active,
    },
    chip: {
      color: statusColours.active,
      borderColor: statusColours.active,
    },
    priority: 150,
  },
  RENEWING: {
    name: "Renewing",
    color: {
      color: statusColours.active,
    },
    mainChip: {
      color: statusColours.active,
      borderColor: statusColours.active,
    },
    chip: {
      color: statusColours.active,
      borderColor: statusColours.active,
    },
    priority: 150,
  },
  RENEWED: {
    name: "Renewed",
    color: {
      color: statusColours.active,
    },
    mainChip: {
      color: statusColours.active,
      borderColor: statusColours.active,
    },
    chip: {
      color: statusColours.active,
      borderColor: statusColours.active,
    },
    priority: 150,
  },
  HEAD_OF_TERMS: {
    name: "Head Of Terms",
    color: {
      color: statusColours.HeadOfTerms,
    },
    mainChip: {
      color: statusColours.HeadOfTerms,
      borderColor: statusColours.HeadOfTerms,
    },
    chip: {
      color: statusColours.HeadOfTerms,
      borderColor: statusColours.HeadOfTerms,
    },
    priority: 30,
  },
  UNDER_OFFER: {
    name: "Under Offer",
    color: {
      color: statusColours.UnderOffer,
    },
    mainChip: {
      color: statusColours.UnderOffer,
      borderColor: statusColours.UnderOffer,
    },
    chip: {
      color: statusColours.UnderOffer,
      borderColor: statusColours.UnderOffer,
    },
    priority: 30,
  },
  OFFER_ACCEPTED: {
    name: "Offer Accepted",
    color: {
      color: statusColours.OfferAccepted,
    },
    mainChip: {
      color: statusColours.OfferAccepted,
      borderColor: statusColours.OfferAccepted,
    },
    chip: {
      color: statusColours.OfferAccepted,
      borderColor: statusColours.OfferAccepted,
    },
    priority: 30,
  },
  CONVEYANCING: {
    name: "Conveyancing",
    color: {
      color: statusColours.Conveyancing,
    },
    mainChip: {
      color: statusColours.Conveyancing,
      borderColor: statusColours.Conveyancing,
    },
    chip: {
      color: statusColours.Conveyancing,
      borderColor: statusColours.Conveyancing,
    },
    priority: 30,
  },
  EXCHANGE: {
    name: "Exchange",
    color: {
      color: statusColours.Exchange,
    },
    mainChip: {
      color: statusColours.Exchange,
      borderColor: statusColours.Exchange,
    },
    chip: {
      color: statusColours.Exchange,
      borderColor: statusColours.Exchange,
    },
    priority: 30,
  },
  SOLD_STC: {
    name: "Sold STC",
    color: {
      color: statusColours.SoldSTC,
    },
    mainChip: {
      color: statusColours.SoldSTC,
      borderColor: statusColours.SoldSTC,
    },
    chip: {
      color: statusColours.SoldSTC,
      borderColor: statusColours.SoldSTC,
    },
    priority: 30,
  },
  DPS_CUSTODIAL: {
    name: "DPS Custodial",
    color: {
      color: statusColours.SoldSTC,
    },
    mainChip: {
      color: statusColours.SoldSTC,
      borderColor: statusColours.SoldSTC,
    },
    chip: {
      color: statusColours.SoldSTC,
      borderColor: statusColours.SoldSTC,
    },
    priority: 30,
  },
};

export const TENANCY_STATUS_LIST: { value: string; text: string }[] = [
  { text: "Active", value: "ACTIVE" },
  { text: "Application", value: "APPLICATION" },
  { text: "Archived", value: "ARCHIVED" },
  { text: "Cancelled", value: "CANCELLED" },
  { text: "Check-In", value: "CHECK_IN" },
  { text: "Contract", value: "CONTRACT" },
  { text: "Conveyancing", value: "CONVEYANCING" },
  { text: "Draft", value: "DRAFT" },
  { text: "Exchange", value: "EXCHANGE" },
  { text: "Heads Of Terms", value: "HEAD_OF_TERMS" },
  { text: "Holding Over", value: "HOLDING_OVER" },
  { text: "Notice Given", value: "NOTICE_GIVEN" },
  { text: "Offer Accepted", value: "OFFER_ACCEPTED" },
  { text: "Periodic", value: "PERIODIC" },
  { text: "Referencing", value: "REFERENCING" },
  { text: "Renewed", value: "RENEWED" },
  { text: "RtR Check", value: "RTR_CHECK" },
  { text: "Sold STC", value: "SOLD_STC" },
  { text: "Transfer £", value: "TRANSFER_MONEY" },
  { text: "Under Offer", value: "UNDER_OFFER" },
  { text: "Vacated", value: "VACATED" },
  { text: "Vacating", value: "VACATING" },
  { text: "Renewing", value: "RENEWING" },
  { text: "Expiring", value: "EXPIRING" },
  { text: "Upcoming", value: "UPCOMING" },
];

export class TenancyUtils {
  static getColor = (status: string): CSSProperties => {
    return TENANCY_STATUS[status] ? TENANCY_STATUS[status].color : TENANCY_STATUS.DRAFT.color;
  };

  static getChip = (status: string): CSSProperties => {
    return TENANCY_STATUS[status] ? TENANCY_STATUS[status].chip : TENANCY_STATUS.DRAFT.chip;
  };

  static getMainChip = (status: string): CSSProperties => {
    return TENANCY_STATUS[status] ? TENANCY_STATUS[status].mainChip : TENANCY_STATUS.DRAFT.mainChip;
  };

  static getName = (status: string): string => {
    return TENANCY_STATUS[status] ? TENANCY_STATUS[status].name : TENANCY_STATUS.DRAFT.name;
  };

  static getTenantDisplayName = (primaryTenant: User): string => {
    return primaryTenant?.fname || primaryTenant?.fname || primaryTenant?.companyName
      ? primaryTenant.companyName
      : primaryTenant?.fname || "";
  };
}
