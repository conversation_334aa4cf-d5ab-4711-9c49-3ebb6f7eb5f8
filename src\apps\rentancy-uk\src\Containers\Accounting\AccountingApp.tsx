import "./init-amplify";
import { BrowserRouter, Route, Routes } from "react-router-dom";
import { FC, useEffect } from "react";
import {
  GlobalProvider,
  NetworkGlobal,
  NotificationProvider,
  UserGlobal,
  ErrorBoundary,
  DictionariesProvider,
  WebSocketProvider,
} from "@rentancy/common";
import { LazyLoad } from "@rentancy/ui";
import { fetchWSToken } from "Api/User";
import { makeStyles } from "@mui/styles";
import { Authorization } from "Components/Authorization";
import { AccountingLayout } from "./layout/AccountingLayout";
import { initAdTrack } from "../App/ad";
import mixpanel from "mixpanel-browser";
import { IdleDetector } from "Components/IdleDetector";
import { HTTP_ENDPOINT, REALTIME_ENDPOINT } from "Constants/index";

// 获取微应用的基础路径
function getBasename() {
  // 如果是qiankun微应用模式，使用空字符串作为basename
  if ((window as any).__POWERED_BY_QIANKUN__) {
    return "";
  }
  // 独立运行时使用默认路径
  return "";
}

initAdTrack();

export const AccountingApp: FC = () => {
  const classes = useStyles();

  // todo: remove, what does this for ?
  useEffect(() => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (window as any).dataLayer = (window as any).dataLayer || [];
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (window as any).dataLayer.push({
      event: "pageview",
    });
  }, []);

  //load walkme and Mixpanel
  useEffect(() => {
    const mixpanelToken = process.env.MIXPANEL_TOKEN as string;
    const isDevelopmentMode = process.env.NODE_ENV === "development";

    mixpanel.init(mixpanelToken || " ", {
      track_pageview: "url-with-path-and-query-string",
      persistence: "localStorage",
      opt_out_tracking_by_default: !mixpanelToken,
    });

    if (isDevelopmentMode) {
      return;
    }
    const walkme = document.createElement("script");
    walkme.type = "text/javascript";
    walkme.async = true;
    walkme.src = process.env.WALKME_SCRIPT_URL || "";
    const scriptElement = document.getElementsByTagName("script")[0];
    scriptElement.parentNode && scriptElement.parentNode.insertBefore(walkme, scriptElement);
    window._walkmeConfig = { smartLoad: true };
  }, []);

  //redirect to lofty-works
  useEffect(() => {
    if (document.location.host === "app.rentancy.com") {
      document.location.replace(
        `https://app.loftyworks.com${document.location.pathname}${document.location.search}`,
      );
    }
  }, []);

  return (
    <div data-testid="accounting-app" className={classes.root}>
      <div className={classes.container}>
        <NetworkGlobal>
          <BrowserRouter basename={getBasename()}>
            <GlobalProvider>
              <UserGlobal>
                <NotificationProvider>
                  <DictionariesProvider>
                    <ErrorBoundary>
                      <WebSocketProvider
                        httpDomain={HTTP_ENDPOINT}
                        realtimeDomain={REALTIME_ENDPOINT}
                        tokenFetcher={fetchWSToken}
                      >
                        <Authorization isAgent>
                          <LazyLoad>
                            <Routes>
                              <Route path="*" element={<AccountingLayout />} />
                            </Routes>
                          </LazyLoad>
                        </Authorization>
                        <IdleDetector />
                      </WebSocketProvider>
                    </ErrorBoundary>
                  </DictionariesProvider>
                </NotificationProvider>
              </UserGlobal>
            </GlobalProvider>
          </BrowserRouter>
        </NetworkGlobal>
      </div>
    </div>
  );
};

const useStyles = makeStyles(theme => ({
  root: {
    minHeight: "100vh",
    display: "flex",
    justifyContent: "space-around",
    [theme.breakpoints.down("lg")]: {
      maxHeight: "100dvh",
      minHeight: "100dvh",
      overflow: "hidden",
    },
  },
  container: {
    backgroundColor: "#ffffff",
    width: "100%",
  },
}));
