import { FC, useEffect, useState } from "react";
import { Box, Theme, But<PERSON>, Stack } from "@mui/material";
import { useTranslation } from "react-i18next";
import { makeStyles } from "@mui/styles";
import { Add } from "@mui/icons-material";
import { Tabs, TabOption } from "@rentancy/ui";
import { useQueryString, useApi } from "@rentancy/common";
import { Helmet } from "react-helmet";
import { ApplicationDesktop } from "./ApplicationDesktop";
import { getAllApplication, getApplicationStatics } from "Api/Application/index";
import { CreateApplicationModal } from "../components/CreateApplicationModal";

export const ApplicationList: FC = () => {
  const { t } = useTranslation();
  const classes = useStyles({ withTabs: true });
  const { apiCall: getApplicationApi, loading, data, refetch } = useApi(getAllApplication);
  const { apiCall: getApplicationStaticsApi, data: dataStatics } = useApi(getApplicationStatics);

  const { data: statics = {} } = dataStatics ?? {};

  const { cancelled = 0, completed = 0, pending = 0 } = statics;

  const tabOptions: TabOption[] = [
    { label: "All", count: cancelled + completed + pending, value: "All" },
    { label: "Pending", count: pending, value: "Pending" },
    { label: "Cancelled", count: cancelled, value: "Cancelled" },
    { label: "Completed", count: completed, value: "Completed" },
  ];

  // TODO: need api for different type totals
  const [show, setShow] = useState<boolean>(false);

  const handleTabChange = (value: string) => {
    setPureQuery({
      type: value === "All" ? undefined : value,
    });
  };

  const { query, setPureQuery } = useQueryString<{
    type?: string;
    page: string;
    limit: string;
    orderBy: string;
    orderDirection: string;
    q: string;
  }>();

  useEffect(() => {
    const input = {
      type: query?.type ?? "",
      limit: isNaN(+query.limit) ? 30 : +query.limit,
      page: isNaN(+query.page) ? 1 : +query.page,
      keyword: query.q,
      orderBy: query.orderBy,
      orderDirection: query.orderDirection,
    };
    getApplicationApi(input as any);
    getApplicationStaticsApi();
  }, [query.type, query.limit, query.orderBy, query.orderDirection, query.page, query.q]);

  return (
    <Box className={classes.container}>
      <>
        <Helmet>
          {t("applications")} - {t("loftyWorks")}
        </Helmet>
        <Stack className={classes.root}>
          <Box display="flex" justifyContent="space-between" alignItems="center" height={40}>
            <Tabs tabs={tabOptions} onTabChange={handleTabChange} initialTab={query.type ?? ""} />
            <Box pt={1}>
              <Button
                variant="contained"
                color="primary"
                startIcon={<Add />}
                data-testid="add-application-btn"
                onClick={() => {
                  setShow(true);
                }}
              >
                {t("addApplication")}
              </Button>
            </Box>
          </Box>
        </Stack>
      </>
      <ApplicationDesktop data={data?.data} loading={loading} refetch={refetch} />
      {show && (
        <CreateApplicationModal
          onClose={() => {
            refetch();
            setShow(false);
          }}
          onSave={() => {}}
        />
      )}
    </Box>
  );
};

type StypeProps = {
  withTabs: boolean;
};

const useStyles = makeStyles<Theme, StypeProps>((theme: Theme) => ({
  container: {
    background: theme.palette.specialColors.pageBackground,
    width: "100%",
    flex: 1,
    display: "flex",
    flexDirection: "column",
    [theme.breakpoints.down("lg")]: {
      overflow: "scroll",
      position: "absolute",
    },
  },
  mobileHeader: {
    position: "relative",
    marginTop: "0 !important",
  },
  root: {
    minHeight: 80,
    justifyContent: "space-between",
    boxSizing: "border-box",
    padding: ({ withTabs }) =>
      withTabs ? theme.spacing(2.75, 2.5, 0, 2.5) : theme.spacing(2.5, 3.75),
    background: "#FFF",
    border: `1px solid ${theme.palette.specialColors.themeBorder}`,
    borderTopWidth: 0,
    flexShrink: 0,
  },
  title: {
    fontSize: "24px",
    margin: 0,
    color: theme.palette.text.primary,
    fontWeight: 700,
  },
}));
