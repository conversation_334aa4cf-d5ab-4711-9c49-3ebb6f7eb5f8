import {
  ContactDetailsForSelect,
  InvoiceItem,
  PROPERTY_ROUTES,
  PropertyDetails,
  Tenancy,
  UserTypes,
  getImage,
  getPropertyContracts,
  getPropertyDocuments,
  getPropertyRest,
  listContactsForSelect,
  onChangeDocumentSubscription,
  onChangeProblemReportSubscription,
  onChangePropertySubscription,
  setPropertyViewed,
  useApi,
  useUser,
  getContact,
} from "@rentancy/common";
import {
  Dispatch,
  FC,
  ReactNode,
  SetStateAction,
  createContext,
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
} from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";

import { isRegionUS } from "../../Utils";
import { usePropertyInvoice } from "./hooks/usePropertyInvoice";

export const PropertyDetailsContext = createContext<ContextType | null>(null);

export const PropertyDetailsProvider: FC<{ children?: ReactNode }> = ({ children }) => {
  const params = useParams<{ id?: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(false);
  const { user } = useUser();
  const usersSet = useRef(new Map());
  const [suppliers, setSuppliers] = useState<ContactDetailsForSelect[]>([]);

  const [info, setInfo] = useState<PropertyDetails>({
    addressLine1: "",
    addressLine2: "",
    addressLine3: "",
    id: "",
    parentId: "",
    listingType: "",
    councilTax: "",
    epc: "",
    internetConnection: "",
    type: "",
    city: "",
    postcode: "",
    state: "",
    reference: "",
    country: isRegionUS ? "" : "GB",
    description: "",
    internalNotes: "",
    landlords: [],
    managers: [],
    keyFeatures: [],
    applications: { items: [] },
    primaryLandlordId: null,
    inventory: [],
  });
  const [propertyContracts, setPropertyContracts] = useState<Tenancy[]>([]);

  const { apiCall: getPropertyContractsApi } = useApi(getPropertyContracts);
  const { apiCall: getPropertyDetailsApi } = useApi(getPropertyRest);
  const { apiCall: getContactApi } = useApi(getContact);
  const { apiCall: getPropertyDocumentsApi, loading: documentsLoading } =
    useApi(getPropertyDocuments);
  const { apiCall: getContactListApi, loading: suppliersLoading } = useApi(listContactsForSelect);

  const { getInvoices, propertyInvoices } = usePropertyInvoice({
    propertyId: params.id || "",
  });

  const sortContracts = useCallback((list: Tenancy[]) => {
    const sortedContractsByDate: Tenancy[] = list.sort((a, b) => {
      const aDate = a.startDate?.toString() || "";
      const bDate = b.startDate?.toString() || "";

      return bDate.localeCompare(aDate);
    });

    const activeContracts = sortedContractsByDate.filter(c => c.status === "ACTIVE");
    const notActiveContracts = sortedContractsByDate.filter(c => c.status !== "ACTIVE");
    return [...activeContracts, ...notActiveContracts];
  }, []);

  const getContractList = useCallback(async () => {
    if (!params.id) {
      navigate("/404");
      return;
    }

    const { data } = await getPropertyContractsApi(params.id);
    setPropertyContracts(sortContracts(data?.data.listTenanciesForProperty || []));
  }, [params.id, getPropertyContractsApi, sortContracts, navigate]);

  const getProperty = useCallback(
    async (loading: boolean = true) => {
      loading && setLoading(true);
      if (!params.id) {
        navigate("/404");
        return;
      }

      const response = await getPropertyDetailsApi({
        organisationId: user.organisationId,
        propertyId: params.id,
      });

      if (!response.data) {
        navigate("/404");
        return;
      }

      const data = response.data;

      let url = data.coverImage;
      if (data.coverImage) {
        url = await getImage("original", data.coverImage);
      }

      setInfo(pre => ({
        ...pre,
        ...data,
        id: data.id || "",
        addressLine1: data.addressLine1 || "",
        addressLine2: data.addressLine2 || "",
        addressLine3: data.addressLine3 || "",
        listingType: data.listingType || "",
        minimumBalance: data.minimumBalance || 0,
        councilTax: data.councilTax || "",
        type: data.type || "",
        status: data.status,
        city: data.city || "",
        postcode: data.postcode || "",
        state: data.state || "",
        reference: data.reference || "",
        description: data.description || "",
        epc: data.epc || "",
        internetConnection: data.internetConnection || "",
        internalNotes: data.internalNotes || "",
        keyFeatures: data.keyFeatures || [],
        landlords: data.landlords || [],
        problemCards: data.problemCards,
        managers: data.managers || [],
        country: data.country,
        primaryLandlord: data.primaryLandlord,
        latitude: data.latitude || 0,
        longitude: data.longitude || 0,
        marketedLatitude: data.marketedLatitude || 0,
        marketedLongitude: data.marketedLongitude || 0,
        applications: data.applications,
        parentPropertyEntity: data.parentPropertyEntity,
        image: pre.image !== url ? url : pre.image,
        splitOwnershipEnabled: data.splitOwnershipEnabled,
      }));

      loading && setLoading(false);
    },
    [params.id, getPropertyDetailsApi, user.organisationId, navigate],
  );

  const { apiCall: setPropertyViewedApi } = useApi(setPropertyViewed, {
    disableErrorMessage: true,
  });

  const getUserObject = useCallback(
    (id: string) => {
      if (usersSet.current.has(id)) {
        return usersSet.current.get(id);
      }
      getContactApi({ id })
        .then(res => {
          usersSet.current.set(id, res.data);
        })
        .catch(() => {
          usersSet.current.set(id, null);
        });
      return null;
    },
    [getContactApi],
  );

  const getSuppliers = useCallback(async () => {
    const { data } = await getContactListApi({
      types: [UserTypes.SUPPLIER],
    });

    setSuppliers(data?.users || []);
  }, [getContactListApi]);

  useEffect(() => {
    if (user.username && user.organisationId && params.id && process.env.RENTANCY_CORE_ENDPOINT) {
      setPropertyViewedApi({
        organisationId: user.organisationId,
        userId: user.username,
        propertyId: params.id,
        urlPrefix: process.env.RENTANCY_CORE_ENDPOINT,
      });
    }
  }, [setPropertyViewedApi, user.organisationId, user.username, params.id]);

  const getDocuments = useCallback(async () => {
    if (!params.id) {
      return;
    }

    const { data } = await getPropertyDocumentsApi(params.id);

    setInfo(prev => ({
      ...prev,
      documents: { items: data || [] },
    }));
  }, [params.id, getPropertyDocumentsApi]);

  useEffect(() => {
    getContractList();
    getProperty();
    getDocuments();
    getInvoices();

    if (!suppliers.length) {
      getSuppliers();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location.state?.reload, params.id]);

  useEffect(() => {
    const managers = info.managerIds?.map(id => getUserObject(id)).filter(Boolean);
    const landlords = info.landlordIds?.map(id => getUserObject(id)).filter(Boolean);

    setInfo(prev => ({
      ...prev,
      managers,
      landlords,
    }));
  }, [getUserObject, info.landlordIds, info.managerIds, usersSet.current.size]);

  useEffect(() => {
    const onChangeProperty = onChangePropertySubscription(params.id!).subscribe({
      next: (data: {
        value: {
          data: {
            onUpdateProperty: {
              archived: boolean;
            };
          };
        };
      }) => {
        if (data.value.data.onUpdateProperty.archived) {
          navigate("/");
        } else {
          getProperty(false);
        }
      },
    });
    const onChangeProblemReport = onChangeProblemReportSubscription(params.id!).subscribe({
      next: (data: {
        value: {
          data: {
            onProblemCardChanged: {
              archived: boolean;
            };
          };
        };
      }) => {
        if (data.value.data.onProblemCardChanged.archived) {
          navigate(PROPERTY_ROUTES.problems(params.id!));
        } else {
          setTimeout(() => {
            getProperty(false);
          }, 300);
        }
      },
    });

    return () => {
      onChangeProperty.unsubscribe();
      onChangeProblemReport.unsubscribe();
    };
  }, [
    params.id,
    getProperty,
    navigate,
    location,
    user.organisationId,
    user.username,
    getContractList,
  ]);

  useEffect(() => {
    const onUpdateDocument = onChangeDocumentSubscription(params.id!).subscribe({
      next: (data: {
        value: {
          data: {
            onDocumentChanged: {
              documentPropertyId: string;
            };
          };
        };
      }) => {
        if (
          data.value.data.onDocumentChanged &&
          data.value.data.onDocumentChanged.documentPropertyId === params.id
        ) {
          getDocuments();
        }
      },
      error: (error: object) => {
        console.log("error", error);
      },
    });

    return () => {
      onUpdateDocument.unsubscribe();
    };

    //eslint-disable-next-line
  }, [params.id, getProperty, history, user.organisationId, user.username]);

  return (
    <PropertyDetailsContext.Provider
      value={{
        details: info!,
        setDetails: setInfo,
        getProperty,
        loading,
        setLoading,
        propertyContracts,
        getDocuments,
        documentsLoading,
        getContractList,
        suppliers: suppliers || [],
        suppliersLoading,
        setSuppliers,
        propertyInvoices,
        getInvoices,
      }}
    >
      {children}
    </PropertyDetailsContext.Provider>
  );
};

export const usePropertyDetails = () => {
  const propertyDetails = useContext(PropertyDetailsContext);

  if (!propertyDetails) {
    throw new Error("usePropertyDetails must be inside in PropertyDetailsContext");
  }
  return propertyDetails;
};

interface ContextType {
  details: PropertyDetails;
  getProperty: (loading?: boolean) => Promise<void>;
  loading: boolean;
  getDocuments: () => Promise<void>;
  documentsLoading: boolean;
  setDetails: Dispatch<SetStateAction<PropertyDetails>>;
  setLoading: Dispatch<SetStateAction<boolean>>;
  propertyContracts: Tenancy[];
  getContractList: () => Promise<void>;
  suppliersLoading: boolean;
  suppliers: ContactDetailsForSelect[];
  setSuppliers: Dispatch<SetStateAction<ContactDetailsForSelect[]>>;
  getInvoices: () => Promise<void>;
  propertyInvoices: InvoiceItem[];
}
