import {
  Box,
  Dialog,
  DialogActions,
  DialogContent,
  Divider,
  IconButton,
  Typography,
} from "@mui/material";
import { <PERSON><PERSON>, ConfirmPopup, DialogHeader } from "@rentancy/ui";
import {
  ContractSettings,
  SubscriptionType,
  Tenancy,
  TenancySettingsResponse,
  UserTypes,
  getContactsList,
  getManagementFeeConfig,
  hasSubscription,
  updateTenancy,
  updateTenancySettings,
  useApi,
  useGlobal,
  useUser,
} from "@rentancy/common";
import { FC, useCallback, useEffect, useMemo, useState } from "react";
import { Icon } from "@rentancy/icon";
import { useTranslation } from "react-i18next";

import { DepositPanel } from "./Deposit/DepositPanel";
import { DetailsPanel } from "./DetailsPanel/DetailsPanel";
import { EditContractTab, EditContractTabEnum, Errors } from "./EditContractTab";
import { FinancialPanel } from "./Financial/FinancialPanel";
import { MembersPanel } from "./Members/MemberPanel";
import {
  TenancyFormik,
  getInitialValues,
  transformSubmitSettings,
  transformSubmitTenancy,
  useEditContractForm,
} from "./hooks/useEditContractForm";
import { deepCompareFormValues } from "./utils";
import { TenancyStatus } from "../consts";

const EditPanels = {
  [EditContractTabEnum.DETAILS]: (props: TenancyFormik) => <DetailsPanel {...props} />,
  [EditContractTabEnum.FINANCIAL]: (props: TenancyFormik) => <FinancialPanel {...props} />,
  [EditContractTabEnum.DEPOSIT]: (props: TenancyFormik) => <DepositPanel {...props} />,
  [EditContractTabEnum.MEMBERS]: (props: TenancyFormik) => <MembersPanel {...props} />,
};
type EditContractDetailPopupProps = {
  contract: Tenancy;
  tenancySettings: TenancySettingsResponse;
  onSuccess: () => void;
};
export const EditContractDetailPopup: FC<EditContractDetailPopupProps> = ({
  contract,
  tenancySettings,
  onSuccess,
}) => {
  const [open, setOpen] = useState(false);
  const [openUnsavedConfirm, setOpenUnsavedConfirm] = useState(false);
  const [openDateChangeConfirm, setDateChangeConfirm] = useState(false);
  const [isLoading, setLoading] = useState(false);
  const [tabValue, setTabValue] = useState<EditContractTabEnum>(EditContractTabEnum.DETAILS);
  const { t } = useTranslation();
  const { user } = useUser();

  const hasLoftyPayOrFullSubscription = hasSubscription(user, [
    SubscriptionType.LOFTYPAY,
    SubscriptionType.FULL,
  ]);

  const { emitter } = useGlobal();

  const { apiCall: updateTenancyApi } = useApi(updateTenancy);
  const { apiCall: updateTenancySettingApi } = useApi(updateTenancySettings);
  const { apiCall: getManagementFeeConfigApi, data: managerFeeConfig } =
    useApi(getManagementFeeConfig);
  const { apiCall: listContactsApi, data: contacts } = useApi(getContactsList);

  const initialContract = useMemo(() => {
    return getInitialValues(
      contract,
      tenancySettings,
      managerFeeConfig,
      contacts?.items,
      hasLoftyPayOrFullSubscription,
    );
  }, [contacts?.items, contract, hasLoftyPayOrFullSubscription, managerFeeConfig, tenancySettings]);

  const handleSuccess = () => {
    onSuccess();
    setOpen(false);
  };

  const formik = useEditContractForm({ initialContract });

  const isValid = formik.isValid;
  const [
    isTenancyChanged,
    isSettingsChanged,
    isTenancySettingsChanged,
    isStartDateChanged,
    isEndDateChanged,
  ] = useMemo(() => {
    return [
      !deepCompareFormValues(initialContract, formik.values),
      !deepCompareFormValues(initialContract.settings!, formik.values.settings!),
      !deepCompareFormValues(initialContract.tenancySettings, formik.values.tenancySettings),
      initialContract.startDate !== formik.values.startDate,
      initialContract.endDate !== formik.values.endDate,
    ];
  }, [initialContract, formik.values]);

  const handleClose = () => {
    if (isTenancyChanged || isSettingsChanged) {
      setOpenUnsavedConfirm(true);
    } else {
      setOpen(false);
    }
  };

  const handleSave = async () => {
    try {
      setLoading(true);
      const updatePromises = [];
      if (isSettingsChanged) {
        updatePromises.push(
          updateTenancySettingApi(
            transformSubmitSettings(
              formik.values.settings! as ContractSettings,
              hasLoftyPayOrFullSubscription,
            ),
          ),
        );
      }
      if (isTenancyChanged) {
        updatePromises.push(
          updateTenancyApi(transformSubmitTenancy(formik.values, hasLoftyPayOrFullSubscription)),
        );
      }

      if (updatePromises.length > 0) {
        await Promise.all(updatePromises);
        handleSuccess();
      }
    } catch (error) {
      console.error("Error updating tenancy:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (open) {
      formik.setValues(initialContract);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initialContract, open]);

  useEffect(() => {
    if (!open) {
      setTabValue(EditContractTabEnum.DETAILS);
    }
  }, [open]);

  const showTenancyDetail = useCallback(() => {
    // fixme: force formik to re-render in next tick
    setTimeout(() => {
      formik.setFieldValue("status", TenancyStatus.PERIODIC);
    }, 0);

    setOpen(true);
  }, [formik]);

  useEffect(() => {
    emitter?.on("openEditContractDetails", showTenancyDetail);
    return () => {
      emitter?.off("openEditContractDetails", showTenancyDetail);
    };
  }, [emitter, showTenancyDetail]);

  useEffect(() => {
    if (hasLoftyPayOrFullSubscription && contract.id) {
      getManagementFeeConfigApi({ tenancyId: contract.id });
    }
  }, [contract, getManagementFeeConfigApi, hasLoftyPayOrFullSubscription]);

  useEffect(() => {
    listContactsApi({ type: UserTypes.SUPPLIER, organisationId: user.organisationId });
  }, [listContactsApi, user.organisationId]);

  return (
    <>
      <ConfirmPopup
        open={openUnsavedConfirm}
        onClose={() => setOpenUnsavedConfirm(false)}
        title={t("unsavedChanges")}
        onConfirm={() => {
          setOpenUnsavedConfirm(false);
          setOpen(false);
        }}
        note={
          <>
            <Typography color="text.secondary2">{t("unsavedChangesMsg1")}</Typography>
            <Typography color="text.secondary2">{t("unsavedChangesMsg2")}</Typography>
          </>
        }
        confirmButtonTitle={t("leave")}
      />
      <ConfirmPopup
        open={openDateChangeConfirm}
        onClose={() => setDateChangeConfirm(false)}
        title={t("changeDateWarning")}
        onConfirm={() => {
          setDateChangeConfirm(false);
          handleSave();
        }}
        note={
          <>
            <Typography color="text.secondary2">{t("changeDateWarningMsg")}</Typography>
          </>
        }
        confirmButtonTitle={t("confirm")}
      />
      <IconButton onClick={() => setOpen(true)} data-testid="edit-tenancy-btn">
        <Icon name="edit" />
      </IconButton>
      <Dialog open={open} onClose={handleClose} fullWidth maxWidth="md">
        <DialogHeader onClose={handleClose}>
          <Typography variant="h4">{t("editTenancy")}</Typography>
        </DialogHeader>
        <DialogContent sx={{ padding: "0px !important" }}>
          <EditContractTab
            tabValue={tabValue}
            setTabValue={setTabValue}
            errors={formik.errors as Errors}
          />
          <Divider />
          <Box sx={{ height: "800px" }} padding={theme => theme.spacing(2.5, 3.75)}>
            {EditPanels[tabValue]({ formik, initialContract })}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button data-testid="edit-cancel-btn" variant="text" color="info" onClick={handleClose}>
            {t("cancel")}
          </Button>
          <Button
            data-testid="edit-save-btn"
            loading={isLoading}
            disabled={isLoading || !isValid || (!isTenancyChanged && !isTenancySettingsChanged)}
            variant="contained"
            color="primary"
            onClick={() => {
              if (isStartDateChanged || isEndDateChanged) {
                setDateChangeConfirm(true);
              } else {
                handleSave();
              }
            }}
          >
            {t("save")}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};
