{"reselect": "Reselect", "selectATenantToCreateInvoice": "Select a Tenant to Create Invoice", "searchByContactName": "Search by contact name", "vendor": "<PERSON><PERSON><PERSON>", "expired": "Expired", "pastDue": "Past Due", "receivePayment": "Receive Payment", "newAccount": "New Account", "accountCode": "Account Code", "accountName": "Account Name", "enterRoutingNumber": "Enter routing number", "enterAccountNumber": "Enter account number", "enterAccountName": "Enter account name", "enterAccountCode": "Enter account code", "enterDescription": "Enter description", "accountInfo": "Account Info", "revenue": "Revenue", "expense": "Expense", "youWouldMakeadjustments": "if you would like to make any adjustments", "alreadyEnteredOpeningBalances": "You already entered your opening balances for this account.", "whenStartTrackingFinances": "When do you want to start tracking your finances for this account?", "whenStartTrackingFinancesDescription": "Your accounting start date is very important, because this is the day you begin tracking transactions for this bank account in LoftyWorks. Everything before this date is summarized in the opening balance you set for this bank account.", "accountingStartDate": "Accounting Start Date", "WhatTotalAccountBalanceOn": "What was your total account balance on ", "accountBalance": "Account <PERSON><PERSON>", "enterBalance": "Enter balance", "knowTheBalanceByProperty": "We also need to know the balance by property", "knowTheBalanceByPropertyDescription": "Please add the balance per property below. The total per property must be equal to the total Account Balance. This should include the total amount for each property,including deposits and reserves.", "addAnotherProperty": "Add Another Property", "remainingBalance": "Remaining Balance", "remainingBalanceMustBeZero": "Remaining balance must be zero", "thisFieldIsRequired": "This field is required", "outstandingTransaction": "Outstanding Transaction", "charges": "Charges", "paymentAmount": "Payment Amount", "receivedFrom": "Received from", "depositAccount": "Deposit account", "amountReceived": "Amount Received", "paymentMethod": "Payment method", "amountToApply": "Amount to Apply", "amountToCredit": " Amount to Credit", "zeroAmount": "$0.00", "nameExists": "This name already exists", "codeExists": "This code already exists", "systemAccountsCannotBeChanged": "System accounts cannot be changed", "selectDifferentAccountOrCreateNewOne": "Please select a different account or create a new one.", "accountCannotExceed1000": "The number of account can’t exceed 1000", "selectProperty": "Select property", "newExpense": "New Expense", "expenseDate": "Expense date", "payFromAccountId": "Pay from account", "accountbalance": "Account balance", "oneTimeExpense": "One time Expense", "recurringExpense": "Recurring Expense", "expenseSelection": "Expense Selection", "memo": "Memo", "addLineItems": "+ Add Line Item", "startDate": "Start Date", "endDate": "End Date", "frequency": "Frequency", "repeatForever": "Repeat Forever", "selectMethod": "Select method", "enterAmount": "Enter Amount", "noDepositsHeldByTheAccount": "There are no deposits held by the account", "withholdDeposit": "With<PERSON>", "amountWithhold": "Amount withhold", "leaseOutstandingBalance": "Lease Outstanding Balance", "totalDepositsWithheld": "Total Deposits Withheld", "amountHeld": "Amount Held", "invoiceSelection": "Invoice Selection", "oneTimeInvoice": "One time Invoice", "recurringInvoice": "Recurring Invoice", "recurringInvoices": "Recurring Invoices", "invoiceData": "Invoice Date", "dueDate": "Due Date", "daily": "Daily", "weekly": "Weekly", "every2Weeks": "Every 2 Weeks", "biWeekly": "Biweekly", "every2Months": "Every 2 Months", "quarterly": " Quarterly", "every6Months": "Binnual (Every 6 Months)", "annually": "Annually", "numberOnDaysUntilDue": "Number on days until due", "account": "Account", "selectAccount": "Select account", "description": "Description", "addLineItem": "Add Line Item", "note": "Note", "bankDeposit": "Bank Deposit", "depositDate": "Deposit Date", "SelectThePaymentIncludedInThisDeposit": "Select the payment included in this deposit", "item": "<PERSON><PERSON>", "payment": "Payment", "amountWithholdCantBeGreaterThanAmountHeld": "Amount Withhold can’t be greater than Amount Held", "createBill": "New Bill", "createCredit": "Issue Credit", "createInvoice": "Create Invoice", "selectAContactToCreateBill": "Select a Contact to Create Bill", "selectAContactToIssueCredit": "Select a Contact to issue credit", "billSelection": "<PERSON>", "oneTimeBill": "One Time Bill", "recurringBill": "Recurring Bill", "billDate": "<PERSON>", "success": "Success", "DragFilesOrClickHereToUpload": " Drag files or click here to upload", "DragFileOrClickHereToUpload": "Drag file or click here to upload", "selectATenantToWithholdDeposit": "Select a Tenant to withhold deposit", "selectAContactToCreateExpense": "Select a Contact to create expense", "issueCredit": "Issue Credit", "selectATenantToRecordPayment": "Select a Tenant to record payment", "balance": "Balance", "invoice": "Invoice", "credit": "Credit", "amountMustBeSmallerThanBalance": "Amount must be smaller than balance", "maximumAmountCanBeAppliedToBill": "Maximum amount can be applied to bill of {{propertyName}} is {{balance}}", "paymentErrorFromCheckbookFlow": "Contact {{companyName}} does not have the following date stored: {{missingFields}}. Please visit the Contact and ensure that it is populated correctly.", "theAmountReceivedcantBeLess": "The Amount Received (plus credits) can't be less than the selected charges.", "alsoTheSelectedInvoicesCantBeLess": "Also, the selected invoices can't be less than the selected credits.", "noOutstandingInvocie": "There is no outstanding {{type}}", "addADepositLineItemToContinue": "Add a deposit line item to continue", "property": "Property", "transferDate": "Transfer Date", "bankTransfer": "Bank Transfer", "transferTo": "Transfer To", "transferFrom": "Transfer From", "amountToTransfer": "Amount To Transfer", "selectAPropertyToTransfer": "Select a property to transfer", "searchByPropertyAddress": "Search by property address", "accountMustDifferent": "Transfer from account must be different than the transfer to account", "thisTransactionHasAlreadyBeenReconciled": "This transaction has already been reconciled.", "someChangesToThisTransactionWillNotBeAllowed": "Some changes to this transaction will not be allowed.", "click": "Click", "here": "here", "invoices": "Invoices", "min": "Min", "max": "Max", "payee": "Payee", "payer": "Payer", "lookingFor": "Looking For", "subType": "Sub Type", "pleaseEnterCodeInRange": "Please enter code in range", "receivepayment": "Receive payment", "Unpaid": "Unpaid", "Paid": "Paid", "Arrears": "Arrears", "outStanding": "Outstanding", "newInvoice": "New Invoice", "makePayment": "Make Payment", "tipsOfUnpaidble": "The Workspace you've been invited to hasn't connected with <PERSON><PERSON>. Please reach out to your property manager and connect first.", "logPayment": "Log Payment", "selectASupplierToMakePayment": "Select a Supplier to Make Payment", "paidFrom": "<PERSON><PERSON>", "amountPaid": "Amount <PERSON>", "totalAmountDistributedShouldtBeGreaterThanTheAmountToApply": "Total Amount distributed should’t be greater than the amount to apply.", "bills": "Bills", "createBill2": "Create Bill", "Overdue": "Overdue", "amountDue": "Amount Due", "multipleLineItem": "multiple line item", "type": "Type", "billStatus": "Bill Status", "createPayment": "Create Payment", "selected": "Selected", "PartiallyPaid": "Partially Paid", "Processing": "Processing", "billSelectWarning": "Only support selecting one supplier's multiple bills to make payment, can search supplier in the upper 'From' Box first, then select to pay for convenience", "popupTitle": "Some bills are already fully paid", "popupNote": "Bills are already full paid, you can't make payment to these bills anymore.", "dismiss": "<PERSON><PERSON><PERSON>", "includeInPayOut": "Include in pay out", "reconciliation": "Reconciliation", "newBankReconciliation": "New Bank Reconciliation", "selectBankAccount": "Select Bank Account", "uploadBankStatement": "Upload Bank Statement", "importTransactions": "Import Bank Statement", "matchTransactions": "Match Transactions", "selectABankAccountToMakePaymentTo": "Select a bank account to make payment to", "prepareFileForUpload": "Prepare file for upload", "downloadYourBankTransactionsAndUploadFiles": "Download your bank transactions and upload the .CSV, .XLS, or .XLSX here to manually reconcile your finances. A CSV template is available for you to download below.", "downloadTemplate": "Download Template", "supportedFormatsSpreadsheet": "Supported formats: Spreadsheet (.CSV, .XLS, .XLSX)", "MaxFileSize100Mb": "Max file size: 100Mb.", "summaryOfTransactions": "Summary of transactions", "transactionAreReadyToImport": "transaction(s) are ready to import", "transactionAreUnableToRecognized": "transaction(s) are unable to  recognized", "transactionType": "Transaction Type", "transactionAmount": "Transaction Amount", "thereIsNo": "There is no {{type}}", "bankAccountSummary": "Bank Account Summary", "statementBalance": "Statement Balance", "balanceInRentancy": "Balance in LoftyWorks", "reconciliationReport": "Reconciliation Report", "bankAccountSummaryHover": "Bank reconciliation is a process to confirm all transactions with our bank are recorded in LoftyWorks. It is done by matching the bank statement lines with our transactions in LoftyWorks. Left side is your bank statement’s transaction and balances, right side is all the transactions and balance you recorded in LoftyWorks system. Match to see if you have finish recording every single transaction in LoftyWorks. Once two balances are the same, it means you have done reconciliation for this period. We recommend you do the reconciliation regularly to keep every transaction on track.", "submissionSuccessful": "Submission Successful", "totalItemsSelected": "Total items selected", "statementImportRuleHoverLine1": "Please follow the template’s rule (on the header), There are limitation and some rules in characters inputting in each cell.", "statementDateRule": "Date: only MM/DD/YYY format.", "statementPayeeRule": "Payee: 40 characters Max.", "statementDescriptionRule": "Description: 200 characters Max.", "statementReferenceRule": "Reference: 30 characters Max.", "matchedTransactions": "Matched transactions", "reconcile": "Reconcile", "matchedTransactionsNote": "Search for your transactions. The sum of the selected transactions must match the amount received.", "inputAmount": "Input Amount", "clear": "Clear", "filters": "Filters", "showReceivedItems": "Show Received Items", "showPaidItems": "Show Paid Items", "showItemsOnly1": "Show", "showItemsOnly2": "items only", "mustMatchMoneyPaid": "Must match money paid", "selectedTransactionsSum": "Selected transactions sum:", "amountToReconcile": "Amount to <PERSON><PERSON><PERSON><PERSON>:", "totalIsOutBy": "Total is out by:", "whatsThis": "What's this", "transactionMatchingSectionText": "Review your bank statement lines,then match with your transactions in LoftyWorks", "moreDetail": "More details", "findMore": "Find & Match", "go": "Go", "inputName": "Input Name", "statementDetails": "Statement Details", "transactionDate": "Transaction Date", "reference": "Reference", "totalsMatch": "Totals match", "statementEndingDate": "Statement Ending Date", "reconciledAt": "Reconciled At", "endingBalance": "Ending Balance", "viewReport": "View Report", "disconnectYourBank": "Disconnect your bank", "connectYourBank": "Connect your bank", "areYouSureToDisconnectIt": "We've leveraged our integration with Plaid to streamline your reconciliation on LoftyWorks. Are you sure you want to disconnect your account from Plaid?", "undoReconciliation": "Undo reconciliation", "Draft": "Draft", "continueReconciliation": "Continue Reconciliation", "undoAlertMsg": "Do you want to undo reconciliation of the transaction? When you undo, this transaction will not be deleted, but its status will become unreconciled.", "whatIsThisHover": "Once you think your bank statement on the left side totally match LoftyWorks's transaction, click OK. This transaction will be gone, and marked as reconciled.", "saveForLater": "Save For Later", "reconciliationUndoStatementPopupMsg": "Do you want to undo reconciliation of the transaction? When you undo, this transaction will not be deleted, but its status will become unreconciled", "undoReconcile": "Undo reconcile", "bankStatement": "Bank Statement", "rentancyTransactions": "LoftyWorks Transactions", "deleteBankStatementConfirmMsg": "Do you want to delete the transaction? You only delete the transaction when it is a repetitive bank transaction that has been imported to system.", "goSetting": "Go to Settings", "setBalanceFirst": "The account you select doesn't have an opening balance, please set an open balance first to continue.", "undoBankStatementConfirmMsg": "Do you want to undo reconciliation of the transaction? When you undo, this transaction will not be deleted, but its status will become unreconciled.", "undo": "Undo", "matchPageActionTipsLine1": "The bank statement line you had reconciled is kept. But you'll need to reconcile it again.", "matchPageActionTipsLine2": "The bank statement line is deleted from system, you have to import the bank statement line again.", "matchPageActionTipsLine3": "Both undo and delete actions make linked LoftyWorks transaction unreconciled.", "matchPageActionTipsLine4": "When you undo reconciliation for an unreconciled bank statement line, nothing will happen.", "amountMustMatch": "*Total amount must match bank statement's amount: {{amount}}.", "rentancyBalance": "LoftyWorks Balance", "startReconciliation": "Start Reconciliation", "viewBankStatement": "View Bank Statement", "viewRentancyTransaction": "View LoftyWorks Transaction", "viewHistory": "View History", "unfinishedReconciliationRecord": "The account you select has an unfinished reconciliation record, you can click “Resume” to continue reconciling.", "endingDateAt": "Ending date at", "resume": "Resume", "reconciliationHistory": "Reconciliation History", "bankReconciliationSummary": "Bank Reconciliation Summary", "date": "Date", "plusOutstandingPayment": "Plus Outstanding Payments", "totalOutstandingPayments": "Total Outstanding Payments", "lessOutstandingReceipts": "Less Outstanding Receipts", "totalOutstandingReceipts": "Total Outstanding Receipts", "plusUnreconciledStatementsLines": "Plus Unreconciled Statements Lines", "totalUnreconciledStatementsLines": "Total Unreconciled Statements Lines", "statementBalances": "Statement Balances", "statementBalancesDescription": "Statement balance(calculated)", "autoCreatePaymentMsg1": "System has create a reconciled status payment {{innerMsg}} for you.", "autoCreatePaymentMsg2": "({{paymentType}}, {{amount}}, from {{accountName}} {{accountCode}})", "uploadTime": "Upload {{time}} time", "funded": "Funded", "markAsFunded": "Mark as funded", "unfunded": "Unfunded", "areYouSureYouWantToManuallyMarkThisBillAsFunded": "Are you sure you want to manually mark this bill as ‘Funded’?", "summaryReportText": "The bank statement balance and LoftyWorks Balance must match. If they don't, adjust the imported bank statement or create invoice/bill/payment.", "rentancyTransactionText1": "the LoftyWorks transaction you had reconciled is kept. But you'll need to reconcile it again.", "rentancyTransactionText2": "Undo reconciliation will make the linked bank statement line transaction turn status to unreconciled.", "rentancyTransactionText3": "When you undo reconciliation for an unreconciled LoftyWorks transaction, nothing will happen.", "previewStatement": "Preview Statement", "incomes": "Incomes", "expenses": "Expenses", "bill": "Bill", "transactionReport": "Transaction Report", "chartofAccounts": "Chart of Accounts", "depositManagement": "Deposit Management", "updateBill": "Update Bill", "updateInvoice": "Update Invoice", "landlordPayout": "Landlord Payout", "multiLine": "Multi-Line", "multiLineBill": "Multi-Line Bill", "multiLineInvoice": "Multi-Line Invoice", "multipleProperties": "Multiple Properties", "toChangeInfoClickEditOrClickSaveToContinue": "To change information click edit, or click “Save” to continue.", "proceed": "Proceed", "processPayment": "Process Payment", "processPayments": "Process Payments", "selectProceed": "Select Proceed", "confirmation": "Confirmation", "poweredByCheckbook": "Powered by Checkbook.io", "checkbookProcessPaymentDescTitle": "We have partnered with Checkbook.io in order to facilitate outgoing Payments through LoftyWorks.", "checkbookProcessPaymentDescLine1": "Payments through Checkbook.io take 2-3 business days to process and reach your Supplier", "checkbookProcessPaymentDescLine2": "Payments through Checkbook.io are charged at $1 per payment. This will be taken from the same Debit Card that is used to pay for your LoftyWorks subscription", "checkbookProcessPaymentDescLine3": "We have also partnered with Plaid who will be utilised to ensure you have suitable funds within your account to process payments", "checkbookProcessPaymentConfirmationTitle": "Select Confirm if satisfied with amounts below, or Back to amend transactions for this payment run.", "paymentPending": "Payment Pending", "paymentSuccessful": "Payment Successful", "checkbookProcessPaymentPendingDesc": "The payment you processed to Checkbook has been successfully submitted. We'll notify you once the result is returned.", "checkbookProcessPaymentSuccessfulDesc": "Your payment in Checkbook are successful, the 'Make Payment' will be generated in LoftyWorks automatically.", "subTotal": "Sub-Total", "bankFees": "Bank Fees", "deleteBillConfirmText": "Are you sure you want to delete this Bill?", "deleteInvoiceConfirmText": "Are you sure you want to delete this Invoice?", "billDeletedSuccessfully": "Bill deleted successfully!", "invoiceDeletedSuccessfully": "Invoice deleted successfully!", "someContactsMissingRequiredFields": "Some contacts are missing required fields, please complete them and try again.", "supplier": "Supplier", "checkbookLogoText": "checkbook", "poweredBy": "Powered by", "pleaseIntegrateWithCheckbookInSettings": "Please integrate with Checkbook in Settings > Integrations to enable payment initiation. Once integrated, you can proceed with payments.", "theBankStatementWillAutoSync": "The bank statement will be synchronized automatically.", "successfullyConnected": "Successfully Connected!", "payNow": "Pay Now", "youHaveOutstandingBankFees": "You have outstanding bank fees from previous transactions. Please click Pay Now to settle these fees before proceeding with your current payment.", "outstandingBankFeeSettled": "Outstanding bank fee settled.", "outstandingBankFeeNotSettled": "Outstanding bank fee not settled. Please try again.", "noLinkedCardNotice": "No linked card found in Lofty's Payment Processing System. Please add one before proceeding.", "transUnionPaidSuccess": "Your payment of ${{amount}} for the Tenant Reference has been successful.", "transUnionPaidFailed": "Your payment of ${{amount}} for the Tenant Reference has failed. Please try again or contact {{email}} for assistance.", "transUnionUnpaidNotice": "You have unsuccessful TranUnion payments. Please pay now before creating a new Tenant Reference.", "bankFeeChargedConfirmationEmailSent": "Bank fee charged. Confirmation email sent.", "bankFeeChargeUnsuccessful": "Bank fee charge unsuccessful. Please try again.", "connected": "Connected", "connectionError": "Connection Error", "actions": "Actions", "pin": "<PERSON>n", "recent": "Recent", "unpin": "Unpin", "tag": "Tag", "tagName": "Tag Name", "markAsUnread": "<PERSON> as Unread", "markAsRead": "<PERSON> <PERSON>", "doNotDisturb": "Do Not Disturb", "enableNotifications": "Enable Notifications", "deleteChat": "Delete Chat", "personal": "Personal", "noPinnedMessages": "No pinned messages...", "noRecentMessages": "No recent messages...", "areYouSureYouWantToDeleteThisChat": "Are you sure you want to delete this chat?", "addTag": "Add Tag", "addNewTag": "Add new Tag", "plusAddTag": "+ Add Tag", "manageTags": "Manage Tags", "noMessageATM": "No messages at the moment.", "noResults": "No results", "startingNewConversation": "You are starting a new conversation", "pleaseEnterFirstMessageBelow": "Please enter your first message below.", "noTagsToDisplay": "No tags to display.", "tagNameAlreadyExist": "Tag name already exist.", "afterDeletingATagTheConversationsWithinTheTagAreNotDeleted": "After deleting a tag, the conversations within the tag are not deleted.", "conversationHistory": "Conversation History", "textMessage": "Text Message", "linkedConversation": "Linked Conversation", "linkedConversations": "Linked Conversations", "unread": "Unread", "recipient": "Recipient", "writeANote": "Write a note...", "pressEnterToSave": "Press \"Enter\" to save", "editAssignedTo": "Edit Assigned to", "addAdministrator": "Add administrator", "viewAll": "View All", "areYouSureYouWantToDeleteThisInternalNote": "Are you sure you want to delete this Internal Note?", "editInternalNote": "Edit Internal Note", "internalNote": "Internal Note", "linkForMoreInformation": "Link for more information", "editLinks": "Edit Links", "doYouWantToActionTheConversation": "Do you want to {{action}} the conversation?", "scheduledEmailCreated": "Scheduled email created", "whatsApp": "WhatsApp", "leaveGroupChat": "Leave Group Chat", "saveToProperty": "Save to Property", "saveToTask": "Save to Task", "saveToParentProperty": "Save to Parent Property", "preScheduled": "Pre-scheduled", "chats": "Chats", "createCommunication": "Create Communication", "hereYouCanInitiateConversations": "Here, you can initiate conversations.", "clickHereToEditPersonalInfo": "Click here to edit personal information.", "auditHistory": "Audit History", "communication": "Communication", "emailSentSuccessfully": "<PERSON>ail sent successfully", "scheduleViewing": "Schedule Showing", "applicantName": "Applicant Name", "leadName": "Lead Name", "phoneName": "Phone Number", "assignPropertyManager": "Assign Property Manager", "placeholderSelectManager": "Select Property Manager", "titleProperty": "Property", "placeholderSelectProperty": "Select Property", "location": "Location", "budget": "Budget", "numberOfRooms": "Number of Rooms", "numberOfBathRooms": "Number of Bathrooms", "desiredMoveInDate": "Desired Move-In Date", "incomePerMonth": "Income (Per Month)", "numberOfOccupants": "Number of Occupants", "extraNotes": "Extra Notes", "qualifyLead": "Qualify Lead", "createScheduleViewSuccess": "Create schedule viewing successfully", "createScheduleViewFailed": "Create schedule viewing failed", "prospectApplicationInfo": "Completed results will be emailed to the property manager.", "applicaitonQuestionnaire": "Questionnaire", "personalDetails": "Personal Details", "searchCriteria": "Search Criteria", "priceRangeIncorrect": "The price range you entered is incorrect. Please enter a valid range.", "enterEllipses": "Enter...", "numberSelect": "Number Select", "updateSuccessful": "Update successful", "lettingsNegotiator": "<PERSON><PERSON>s Negotiator", "addLettingsNegotiator": "Add Lettings Negotiator", "priceRangeIsIncorrect": "The price range you entered is incorrect. Please enter a valid range.", "viewing": "Viewing", "checkCrmWrapperForError": "Please check CRM context provider for errors.", "applicationQuestionnaireSharedSuccess": "Questionnaire shared successfully!", "shareApplicationQuestionnaire": "Share Questionnaire", "confirmToDeleteInterestedProperty": "Are you sure you want to delete this Interested Property?", "confirmToSendPropertyToApplicant": "Are you sure you want to share this property?", "dateOfInterest": "Date of Interest", "showing": "Showing", "addPropertyManager": "Add Property Manager", "leadManagement": "Lead Management", "leadStatus": "Lead Status", "lastUpdatedDate": "Last Update Date", "viewingsArranged": "Viewings Arranged", "nextViewingDate": "Next Viewing Date", "showingsArranged": "Showings Arranged", "nextShowingDate": "Next Showing Date", "createDate": "Create Date", "profitAndLoss": "Profit and Loss", "debitAmount": "Debit Amount", "creditAmount": "Credit Amount", "upcomingViewings": "Upcoming Showings", "allViewings": "All Showings", "deleteViewingConfirmText": "Are you sure you want to delete this Showings?", "lookingForNoDataText": "Complete the lead's preference for better listing recommendation and engagement", "addLookingFor": "Add Looking For", "addQualifyLead": "Add Qualify Lead", "interested": "Interested", "suggested": "Suggested", "contactPropertyManager": "Property Manager", "contactPropertyManagers": "Property Managers", "addContactPropertyManager": "Add Property Manager", "leadEmail": "Lead Email", "sendFrom": "Send From", "leadNumber": "Send Number", "numberMaxinum100": "Maximum 100", "dateRangeIsIncorrect": "The date range you entered is incorrect. Please enter a valid range.", "electricity": "Electricity", "water": "Water", "gas": "Gas", "tv": "TV", "internetAndPhone": "Internet and Phone", "internet": "Internet", "phone": "Phone", "contractor": "Contractor", "handyman": "<PERSON><PERSON>", "electrician": "Electrician", "cleaner": "Cleaner", "plumber": "Plumber", "inventoryClerk": "Inventory Clerk", "supplierType": "Supplier Type", "preferred": "Preferred", "addPreferredSupplier": "Add Preferred Supplier", "whatsNewAtLoftyWorks": "What's new at LoftyWorks", "whatsNewAtLoftyWorksDescirption": "Click the link below to see our latest releases. Then bookmark the page so you always know what’s coming up.", "integrationsDescirption": "We’ve integrated with market leading software including Xero, Google, Outlook and WhatsApp to provide you with the best all in one solution. If you want to connect your existing accounts to LoftyWorks, follow our guides or contact us via", "addFirstPropertyTitle": "Add Your First Property", "addFirstPropertyDescription": "In LoftyWorks, the Property represents the rentable space. Just fill in the details then market your properties on Zillow, HotPads, Trulia and social media to generate new leads.", "addFirstTenancy": "Add Your First Tenancy", "addFirstTenancyDescription": "Within LoftyWorks, you use Contracts to add your Leases. Then manage the end-to-end journey by following the steps", "needHelpGettingStarted": "Need Help Getting Started?", "needHelpGettingStartedDescription": "Need help setting up your account? Use our Getting Started guide or reach out to our Customer Success team at ", "hide": "<PERSON>de", "show": "Show", "tasksDueNextDays": "Tasks Due Next {{days}} Days", "tasksDueToday": "Tasks Due Today", "overdueTasks": "Overdue Tasks", "unassignedTasks": "Unassigned Tasks", "certificates": "Certificates", "expiredGasSafety": "Expired Gas Safety", "expiredEPC": "Expired EPC", "expiredOther": "Expired Other", "upcoming": "Upcoming", "recentlyViewed": "Recently Viewed", "overDueAmount": "Overdue Amount", "rentDueToday": "Rent Due Today", "rentOverdue": "Rent Overdue", "upcomingMoveIns": "Upcoming Move Ins", "upcomingRenewals": "Upcoming Renewals", "upcomingVacancies": "Upcoming Vacancies", "editDashboard": "Edit Dashboard", "contractors": "Vend<PERSON>", "contractorsToPay": "Vendors to Pay", "newLeads": "New Leads", "viewingScheduledToday": "Viewing Scheduled Today", "occupancyRate": "Occupancy Rate", "propertyOwnerPayout": "Property Owner Payout", "rentCollection": "Rent Collection", "workOrderRequiringApproval": "Workorder Requiring Approval", "viewWorkers": "View Workers", "monthToDate": "Month to Date", "yearToDate": "Year to Date", "addNewRow": "Add New Row", "allocateBy": "Allocate by", "allocation": "Allocation", "amountToJournal": "Amount to Journal", "autoJournal": "Auto Journal", "autoJournalDescription": "Auto journal feature automates the creation of financial entries by automatically recording transactions based on predefined rules and criteria.", "billsToBeCarriedForward": "{{ amount }} bills to be carried forward", "clientIsRequired": "Client is required", "contractNumber": "Contract Number", "count": "Count", "currentMonth": "Current Month", "dateReceived": "Date Received", "dayOfMonth": "Day of Month", "exclusiveOfTax": "Exclusive of Tax", "featureIsInBetaModeWarning": "This feature is currently in Beta mode. Are you sure you want to continue", "finaliseRaise": "Finalize & Raise", "finalisingStatementForDates": "Finalizing statement for dates", "firmTerm": "Firm Term", "firstProcess": "First Process", "grossAmount": "Gross Amount", "grossAmountAndTotalValueAreDifferentPleaseCheck": "Gross amount and total value are different please check", "groupByParentProperty": "Group By Parent Property", "groupByProperty": "Group By Property", "groupByType": "Group By Type", "inclusiveOfTax": "Inclusive of Tax", "itemsBroughtForward": "Items Brought Forward", "joint": "Joint", "lastProcess": "Last Process", "ledgerCodeSetup": "Ledger Code Setup", "manual": "Manual", "monthToMonth": "Month-to-Month", "nextJournalDay": "Next Journal Day", "nextProcess": "Next Process", "noArrears": "No Arrears", "noTax": "No Tax", "noVat": "No VAT", "notSetUp": "Not set up", "payoutMethod": "Pay-Out Method", "payoutMethodByLandlord": "Pay-Out By Landlord", "payoutSuccessful": "Payout Successful", "periodLengthInMonths": "Period Length (Months)", "periodToJournal": "Period to Journal", "previousJournalDay": "Previous Journal Day", "priorMonth": "Prior Month", "quantity": "Quantity", "raiseClientBillPayoutFromAmount": "Raise client bill payout from amount", "renewal": "Renewal", "rentPaymentReport": "Rent Payment Report", "rentToOwn": "Rent-to-Own", "residential": "Residential", "returnToPayouts": "Return to Payouts", "searchClientName": "Search client name", "selectClient": "Select Client", "selectSupplier": "Select supplier:", "selectVendor": "Select Vendor", "showSelect": "Show & Select", "statementConfirmation": "Statement Confirmation", "sublet": "Sublet (Sublease)", "subtotal": "Subtotal", "unfundedItems": "Unfunded Items", "upTo": "Up to {{amount}}", "vacationStay": "Vacation Stay", "vatOnOperation": "{{ vat }} (VAT on {{ operation }})", "yourPayoutWasSuccessful": "Your payout was successful.", "zeroRatedOperation": "Zero Rated {{ operation }}", "theMinimumCostForTheSubscription": "The minimum cost for the subscription is $30 ($25+VAT) per month", "upcomingPayments": "Upcoming Payments", "overdueInvoices": "Overdue Invoices", "payWithStripe": "Pay with Stripe", "payTo": "Pay to", "paymentFailed": "Payment Failed", "payAll": "Pay All", "paymentDate": "Payment Date", "errorInGeneratingYourInvoicePleaseTryAgain": "Error generating PDF for Invoice. Please try again.", "paymentFail": "Payment Fail", "AutomaticallyMarkBillsAsFunded": "Auto Mark Bills as Funded", "recurringCharges": "Recurring Charges", "oneOffCharges": "One Off Charges", "charge": "Charge", "contactType": "Contact Type", "addOneOffCharge": "Add One Off Charge", "generateNow": "Generate Now", "saveAndClose": "Save and Close", "infiniceptSetup": "Infinicept Setup", "businessInfo": "Business Info", "businessEmailAddress": "Business Email Address", "enterEmailAddress": "Enter email address", "ownerInfo": "Owner Info", "processingInfo": "Processing Info", "businessInformation": "Business Information", "physicalAddressNoPOBox": "Physical Address (No PO Box)", "formSuccessToastMessage": "Application form saved successfully", "formErrorToastMessage": "There was an error with form submission. Please try again", "whatPercentageOnAverageOfYourMerchantsServicesAreDelivered": "What percentage (on average of your merchants services are delivered? (Must sum to 100%)", "ownerInformation": "{{number}} Owner Information", "first": "First", "second": "Second", "third": "Third", "fourth": "Fourth", "ownersHomeAddress": "{{number}} Owner's Home Address", "processingInformation": "Processing Information", "dBAOrRegisteredTradeName": "DBA or Registered Trade Name", "taxpayerIDOrTaxIdentificationNumberTIN": "Taxpayer ID or Tax Identification Number (TIN)", "legalBusinessEntitiesMailingAddressDifferentFromItsPhysicalAddress": "Legal Business Entity's mailing address different from its physical address", "accountNumber": "Account Number", "bankAccountInformationProvider": "Bank account information provided must be for an account held in the name of the legal entity that is the applicant", "controlpronginformation": "Control Prong Information", "controlprongsaddress": "Control Prong's Address", "areyousureyouwanttocanelonboarding": "Changes will be not saved. Are you sure you want to close onboarding?", "noticeClosingOnboardingHeader": "Notice", "validationHelperIsRequired": "This field is required.", "mustBeValid": "{{fieldName}} must be valid.", "validationHelperMustBeNumber": "Field must be a number.", "mustMatch": "{{fieldName}} must match.", "mustBeValidDateFormat": "Date must be a valid date format (MMM DD YYYY)", "provideEmailToSaveDraft": "Please provide Business Email Address to save draft", "setupInformationSent": "Setup Information Sent", "congratulationsOnCompletingYourInfiniceptSetupForm": "Congratulations on completing your Infinicept setup form! You are one step closer to payments powered by LoftyWorks. Your application will be verified by Infinicept and they will reach out if they have any questions.", "noSpecialCharactersAllowed": "No special characters allowed", "mustBeExactly10DigitsAndCannotStartWith1Or0": "Must be exactly 10 digits and cannot start with 1 or 0", "b2bPlusB2cMustSumTo100": "B2B + B2C must sum to 100", "deliveredMustSumTo100": "Delivered must sum to 100", "assetName": "Asset Name", "enterAssetName": "Enter Asset Name", "editAsset": "Edit Asset", "addNewAsset": "Add New Asset", "deletedAsset": "Deleted asset", "lockboxCode": "Lockbox Code", "inputMake": "Input Make", "inputModel": "Input Model", "assetCreatedSuccessfully": "Asset Created Successfully", "assetUpdatedSuccessfully": "Asset Updated Successfully", "selectInput": "Select", "deleteAsset": "Delete Asset?", "deletedAssetSuccessFully": "Asset Deleted Successfully", "leads": "Leads", "addLead": "Add Lead", "goToLofty": "Go to Lofty", "allLeads": "All Leads", "tenantScreeningStatus": "Tenant Screening Status", "sendToLofty": "Send to Lofty", "shareQuestionnaire": "Share Questionnaire", "completeTheLeadsPreferenceForBetterListingRecommendationAndEngagement": "Complete the Lead's Preference for better listing recommendation and engagement", "upcomingShowings": "Upcoming Showings", "monthlyIncome": "Monthly Income", "occupants": "Occupants", "allShowings": "All Showings", "editBasicInfo": "Edit Basic Info", "basicInfoUpdatedSuccessfully": "Basic info updated successfully", "basicInfoUpdateFailed": "Basic info update failed. Please try again", "theSelectedLeadsHaveBeenSuccessfullySynchronizedToLofty": "The selected leads have been successfully synchronized to Lofty.", "theSelectedPropertiesHaveBeenSuccessfullySynchronizedToLofty": "The selected properties have been successfully synchronized to Lofty.", "viewInLofty": "View in Lofty", "noReassign": "No, reassign", "yesReplace": "Yes, replace", "thePropertyManagersForTheFollowingLeadsAreEitherBlankOrInvalid": "The property managers for the following leads are either blank or lack identity info in Lofty: ", "wouldYouLikeToReplaceThemYourselfAndProceed": "Would you like to replace them with yourself and proceed?", "theLeadsBelowAreAssignedToAnotherAgentInLofty": "The leads below are assigned to another agent in Lofty, you cannot sync or update them to Lofty:", "allTheSelectedLeadsAreAssignedToAnotherAgentInLofty": "All the selected leads are assigned to another agent in Lofty, you cannot sync or update them to Lofty.", "maximumOfTwentyLeadsCanBeSelected": "Maximum of 20 leads can be selected", "maximumOfTwentyPropertiesCanBeSelected": "Maximum of 20 properties can be selected", "partialSuccessSomeLeadsSentOthersIncompleteAndUnsyncedWithLofty": "Partial success! Some leads have been sent, others are incomplete and unsynced with Lofty.", "weAreUnableToSyncTheFollowingLeadsWithLoftyDueToSomeErrors": "We are unable to sync the following leads with Lo<PERSON> due to some errors:", "theLeadsYouHaveSelectedIncludeOnesNotAssignedToYou": "The leads you have selected include ones not assigned to you. You can choose to skip these and proceed with the sync, or cancel the operation altogether.", "noSelectedItemIsAssignedToYouSyncCantProceed": "No selected item is assigned to you. Sync can't proceed.", "thisLeadIsNotAssignedToYou": "This lead is not assigned to you.", "thisItemIsFromLofty": "This item is from Lofty.", "1Day": "1 day", "1SelectTemplate": "1. Select template", "25March_24June_29September_25December": "25 March, 24 June, 29 September, 25 December", "2UploadTheDocumentYouWishToSign": "2. Upload the document you wish to sign", "2ndImage": "2nd Image", "3AddTheContactsYouWouldLikeToSign": "3. Add the contacts you would like to sign", "3rdImage": "3rd Image", "4thImage": "4th Image", "5thImage": "5th Image", "AddNewContact": "+ Add “{{value}}” as new contact", "AddNewLease": "Add New Lease", "AddNewTenant": "Add New Tenant", "CheckOutThis": "Check out this", "ContactInformation": "Contact Information", "LetsTalk": "Let's talk", "SMSSentSuccessfully": "SMS sent successfully", "aCompilationOfAllActiveTasksWithinTheWorkspace": "A compilation of all active tasks within the workspace", "aUserWithThatEmailAddressAlreadyExists": "A user with that email address already exists.", "about": "About", "accessFullLogOfTheChangesMadeAcrossYourWholeWorkspace": "Access full log of the changes made across your whole workspace, performed by all your colleagues and contacts.", "accessFullLogOfTheSummaryOfClientBalancesByProperty": "Access full log of the summary of client balances by property", "accessFullLogOfTheSummaryOfPropertyBalancesByProperty": "Access full log of the summary of property balances by property", "accountingSideBar": "Accounting", "achievedPrice": "Achieved Price", "achivedPrice": "Achived Price", "activateNow": "Activate Now", "active": "Active", "activeContracts": "Active Contracts", "activeLeaseAleardyExists": "An active lease already exists for the selected unit and dates. If the dates for the new lease are correct, fix the start or end dates of the existing lease for this unit", "activity": "Activity", "add1": "+ Add", "add": "Add", "addActivity": "Add Activity", "addAddress": "Add address…", "addAllYourUnitsBelow": "Add all your units below", "addAnEmailAddressThatYouWouldLikeYourWorkspaceToSend": "Add an email address that you would like your workspace to send emails from. E.g. relaying messages or notifications.", "addAnother": "+ Add another", "addAnotherUnit": "Add another unit", "addApplication": "Add Application", "addAsNewContact": "+ Add as new Contact", "addAttachment": "+ Add Attachment", "addBill1": "+ Add Bill", "addBill": "Add Bill", "addBudget": "Add Budget", "addChecklist": "+ Add Checklist", "addContact": "Add Contact", "addContacts": "Add Contacts", "addContract": "Add Contract", "addDeposit": "<PERSON><PERSON>", "addDescription": "Add description…", "addDetails": "Add details…", "addDetailsWithPlus": "+ Add Details", "addDocument": "Add document", "addEachFeatureOnASeparateLine": "* Add each feature on a separate line", "addExpiry": "+ Add Expiry", "addFile1": "Add File", "addFile": "Add File", "addHyperlink": "Add hyperlink", "addInputValueAsNewContact": "+ Add “{{inputValue}}” as new contact", "addInternalNote": "Add internal note…", "addKeyFeatures": "Add key features…", "addLandlord": "+ Add <PERSON>lord", "addManager": "+ Add Manager", "addMore": "Add More", "addNew": "+ Add New", "addNewLandlord": "Add New Landlord", "addNewNoPlus": "Add New", "addNewProperty1": "+ Add New Property", "addNewProperty": "+ Add {{value}} as new property", "addNewPropertyWhenBlank": "Add details of your property to start marketing your property on top listing site", "addNote1": "Add note...", "addNote": "Add note…", "addNotes": "Add notes...", "addNumber": "Add number...", "addPeople1": "Add people", "addPeople": "Add People", "addPhoto": "Add Photo", "addPhotos1": "+ Add Photos", "addPhotos": "Add Photos", "addProperty": "Add Property", "addRent": "Add Rent", "addReservation": "Add Reservation", "addRole": "+ Add {{role}}", "addRow1": "Add row", "addRow": "+Add row", "addTags": "+ Add tags", "addTask1": "Add task", "addTask": "Add Task", "addType1": "+ Add type", "addType": "Add Type", "addVat": "Add VAT", "addWorkspace": "Add Workspace", "addedContract": "Added contract:", "addedTask": "Added task:", "addedToContacts": "Added to contacts", "additionalIncome": "Additional Income", "additionalInformation": "Additional Information", "additionalMessage": "Additional Message", "address": "Address", "addressPostCodeReference": "Address, post code, reference…", "address_1": "Address 1", "address_2": "Address 2", "address_3": "Address 3", "admin": "Admin", "adminsCanClickInviteToAddTeamMembersToTheWorkspace": "Admins can click In<PERSON>te to add team members to the workspace.", "adminsOnlyExportDataInCsvFileFormat": "Admins only, export data in csv file format", "afterSendingCheckInYourDocusignAccountForSigningStatus": "After sending, check in your Docusign account for signing status", "age": "Age", "agent": "Agent", "agentIdTitleFnameSnameEmailsEmailTypePhonesPhoneTypeI": "agent { id title fname sname emails{email type} phones{phone type} image{key} }", "ago": "ago", "all": "All", "allBills": "All Bills", "allStatuses": "All Statuses", "allTypes": "All Types", "allAssignee": "All Assignees", "allAddress": "All Addresses", "allocateTo": "Allocate to:", "allocated": "Allocated", "allocationOfBill": "Allocation of Bill", "alreadyUsingLessThanBrGreaterThanRentancy": "Already using <br /> LoftyWorks?", "amenities": "Amenities", "alreadyUsingLofty": "Already using <br /> LoftyWorks?", "amount": "Amount", "annualIncome": "Annual Income", "apartment": "Apartment", "applicantInfo": "Applicant Info", "applicants": "Applicants", "applicationAssignedTo": "Assigned To", "applicationDeletedSuccessfully": "Application deleted successfully", "applicationPayment": "Online payment needs to be activated in order to charge applications a fee and request credit, criminal and eviction reports from Transunsion.", "applicationRequestCredit": "Request Credit, Criminal and Eviction History reports from Transunion (Cost: ${{cost}})", "applicationSpecifyFees": "Specify fees for this application", "applications": "Applications", "applictionTenantScreening": "Who pays for the tenant screening?", "applyAs": "Apply As", "applyAsCosigner": "Apply as a co-signer", "applyAsTenant": "Apply as a tenant", "approve": "Approve", "approveNegativeBalance": "Approve negative balance", "archive": "Archive", "archiveTask": "Archive Task", "archived": "Archived", "archivedTasks": "Archived Tasks", "areYouAwareThatYouAreAboutToPayABillWithoutHavingFu": "Are you aware, that you are about to pay a bill without having funds in the account?", "areYouSureChangeTheStatusProperty": "Are you sure to change the status of this property?", "areYouSureToChangeTheApplicantStatus": "Are you sure to change the applicant status?", "areYouSureYouWantToDeleteThisChecklist": "Are you sure, you want to delete this checklist?", "areYouSureYouWantToDeleteThisContact": "Are you sure you want to delete this contact?", "areYouSureYouWantToDeleteThisTask": "Are you sure, you want to delete this task?", "areYouSureYouWantToLeaveThisGroup": "Are you sure, you want to leave this group?", "areYouSureYouWantToRemoveChecklistnameChecklist": "Are you sure, you want to remove checklist?", "areYouSureYouWantToRemoveTheWholePhotosSectionThisAc": "Are you sure you want to remove the whole photos section? This action remove all photos", "areYouSureYouWantToRemoveThisDocument": "Are you sure, you want to remove this document?", "areYouSureYouWantToRemoveThisUnit": "Are you sure, you want to remove this unit?", "areYouSureYouWantToRemoveThisUserFromGroup": "Are you sure, you want to remove this user from group?", "areYouSureYoudLikeToRemoveThisMapping": "Are you sure you’d like to remove this mapping?", "areYouTenantCosigner": "Are you applying as a tenant or a co-signer?", "area": "Area", "arrearsAmount": "Arrears Amount", "arrearsFinanceRentancy": "Arrears - Finance - LoftyWorks", "askTheApplicantAboutAdditionalIncome": "Ask the applicant about additional income", "askTheApplicantAboutDependants": "Ask the applicant about dependants", "askTheApplicantAboutEmergencyContacts": "Ask the applicant about emergency contacts", "askTheApplicantAboutEmployment": "Ask the applicant about employment", "askTheApplicantAboutPets": "Ask the applicant about pets", "askTheApplicantAboutResidentialHistory": "Ask the applicant about residential history", "askTheApplicantAboutVehicles": "Ask the applicant about vehicles", "askTheApplicantToUploadFiles": "Ask the applicant to upload files", "askingPrice": "Asking Price", "asset": "<PERSON><PERSON>", "assignGuarantor": "Assign <PERSON>", "assignTenant": "Assign Tenant", "assignedTo": "Assigned to", "assignee": "Assignee", "atLeastOneLandlordHasBeenAssignedToTheProperty": "At least one landlord has been assigned to the property", "atLeastOneTenantHasBeenAssignedToTheContract": "At least one tenant has been assigned to the contract", "attach": "Attach", "attachDocument": "+ Attach Document", "attachFloorplanForProperty": "Attach floorplans for property.", "attachPhotos": "+ Attach Photos", "attachPhotosForProperty": "Attach photos for property. Minimum 1 photo required to advertise your property.", "attachVideosForProperty": "Attach videos for property.", "attached": "Attached", "attachmentHasBeenDeleted": "Attachment has been deleted", "attachments": "Attachments", "authorisingThisBillWillCreateANegativeBalance": "Authorising this bill will create a negative balance", "authorisingThisBillWillCreateANegativeClientBalance": "Authorising this bill will create a negative client balance", "autoInvoice": "Auto Invoice", "availableStartDate": "Available Start Date", "atLeastOneAssigneeIsRequired": "At least one assignee is required", "back1": "BACK", "back": "Back", "backToPreviousStep": "Back to previous step", "backToTenancy": "Back to tenancy", "backgroundImages": "Background images", "backgroundImagesLoadRandomlyEachTimeThePageIsOpenedRem": "Background images load randomly each time the page is opened. Remove or add images as desired.", "balances": "Balances", "bank": "Bank", "bankAddress": "Bank Address", "bankDetails": "Bank Details", "bankName": "Bank Name", "bankingFinanceRentancy": "Banking - Finance - LoftyWorks", "basicInformation": "Basic Information", "bathroom": "Bathroom", "bathrooms": "Bathrooms", "baths": "Baths", "bedroom": "Bedroom", "beds": "Beds", "beingPartOfTheTeamYouCanInviteMoreTeamMembersAllInfo": "Being part of the team you can invite more team members. All information within your workspace is shared with your team.", "beneficiary": "Beneficiary", "billAmount": "<PERSON>", "billGeneratedSuccessfully": "Bill generated successfully!", "billsFinanceRentancy": "Bills - Finance - LoftyWorks", "billsOutstanding": "Bills Outstanding", "billsSelectedValue": "bills selected, value", "blog": "Blog", "board": "Board", "boardView": "Board View", "breakClause": "Break Clause", "breed": "Breed", "budgetSuccessfullyRemoved": "Budget successfully removed", "bulkApproval": "Bulk Approval", "bulkApprove": "Bulk Approve", "bulkPayout": "Bulk Payout", "bulkUpload1": "Bulk upload", "bulkUpload": "Bulk Upload", "businessDetails": "Business Details", "buyers": "Buyers", "byAssignee": "By Assignee", "byContinuingYoureAgreeingToOur": "By continuing, you’re agreeing to our", "byContinuingYoureAgreeingToOurIAccept": "By continuing, you’re agreeing to our I accept", "byDefaultTheyWillBeNamedPropertyAndContractHoweverIfY": "By default they will be named Property and Contract. However if you are already using Tracking Categories in your Xero instance, you can specify their names here.", "byDefaultThisIsUnpublishedSetToPublishedIfYouWantThe": "By default this is unpublished. Set to Published if you want the page to be visible on the internet and able to be connected to your own website.", "byDueDate": "By Due Date", "byProperty": "By Property", "calculationError": "Calculation Error", "calendar": "Calendar", "calendarRentancy": "Calendar - LoftyWorks", "cancel": "Cancel", "cashBalanceReport": "Cash Balance Report", "category": "Category", "allCategories": "All categories", "change": "Change", "changeName": "Change Name", "changeStatus": "Change status ?", "changesHaveBeenSavedSuccessfully": "Changes have been saved successfully!", "chargeAppFees": "Charge Application Fees", "chargeRentFrequency": "Charge Rent Frequency", "chartOfAccount": "Chart Of Account", "chat": "Cha<PERSON>", "chatMembers": "Chat Members", "chatRentancy": "Chat - LoftyWorks", "checkYourEmail1": "Check your email!", "checkYourEmail": "Check your Email", "checklistItems": "Checklist items", "checklistSuccessfullyAddedToTheTask": "Checklist successfully added to the task!", "checklistSuccessfullyCreated": "Checklist successfully created", "checklistSuccessfullyRemoved": "Checklist successfully removed", "checklistSuccessfullyUpdated": "Checklist successfully updated", "checklistTitle": "Checklist title", "city": "City", "clearAllFilters": "Clear All Filters", "clickHere": "click here", "clickTheButtonAboveToAddAProblemReport": "Click the button above to add a Problem Report", "client": "Client", "landlordApprovalRequired": "Landlord A<PERSON> required", "landlordApproved": "Landlord Approved", "landlordAcceptanceStatus": "Landlord Acceptance Status", "pending": "Pending", "approved": "Approved", "declined": "Declined", "clientBalance": "Client Balance", "clientBalancesFinanceRentancy": "Client Balances - Finance - LoftyWorks", "clientBalancesReport": "Client Balances Report", "clientFundsInsufficient": "Client funds insufficient", "clients": "Clients", "clipboardNotSupported": "Clipboard not supported", "closing": "Closing", "closingBalance": "Closing Balance", "closingBalances": "Closing Balances", "code": "Code", "codeIsRequired1": "code is required", "codeIsRequired": "Code is required", "collectRent": "Collect Rent", "color": "Color", "companyAddress": "Company Address", "companyId": "Company ID", "companyName": "Company Name", "companyWebsite": "Company Website", "complete": "Complete", "condo": "<PERSON><PERSON>", "configureForAgentWorkflowsLessThanBrGreaterThanAddClie": "Configure for agent workflows <br />Add Client Account Balances tab <br />Add Client Statement Automations <br />Default management fee 5% <br />Appends landlord details to demands/ invoices", "configureForLandlordWorkflowsLessThanBrGreaterThanDefau": "Configure for landlord workflows <br /> Default No Management Fee <br />Workspace name used for landlord default", "confirm": "Confirm", "connect": "Connect", "connectToXero": "Connect to Xero", "connectToYourFinancialGeneralLedgerByMappingTheCodesIn": "Connect to your financial general ledger by mapping the codes in LoftyWorks.", "connectWithPeopleUsingWhatsapp": "Connect with people using Whatsapp", "connectWithYourDocusignAccount": "Connect with your Docusign Account", "connectWithYourXeroAccount": "Connect with your Xero Account", "connectedPropertiesAndContracts": "Connected Properties and Contracts", "connectedProperties": "Connected Properties", "connectionEstablished": "Connection Established", "connectionFailed": "Connection Failed", "contact": "Contact", "contactEmail": "Contact Email", "contactInformation": "Contact Information", "contactName": "Contact Name", "salutation": "Salutation", "contactNameExistsError": "This contact name already exists as a contact.", "contactPhone": "Contact Phone", "contacts": "Contacts", "contactsLofty": "Contacts - LoftyWorks", "containedInTheSpreadsheetIsATabListingTheFieldDefiniti": "Contained in the spreadsheet is a tab listing the field definitions.", "continue": "Continue", "contract": "Contract", "contractAddedSuccessfully": "Contract added successfully", "contractBreak": "Contract Break", "contractBreakDate": "Contract Break Date", "contractDates": "Contract Dates", "contractDetails": "Contract Details", "contractDeletedSuccessfully": "Contract deleted successfully", "contractEndDate": "Contract End Date", "contractRenewalDate": "Contract Renewal Date", "contractNotesInternalOnly": "Contract Notes (Internal Only)", "contractReference": "Tenancy Reference", "contractRemindersSuccessfullyUpdated": "Contract reminders successfully updated", "contractReviewDate": "Contract Review Date", "contractStartDate": "Contract Start Date", "contractStatus": "Contract Status", "contractStatusIsActiveOrPeriodic": "Contract status is “Active” or Periodic", "contractTags": "Contract Tags", "contractTitle": "Contract Title", "contractType": "Contract Type", "contractTypes": "Contract Types", "contractValue": "Contract Value", "contracts": "Contracts", "controlTheNotificationsYouReceiveToYourEmail": "Control the notifications you receive to your email.", "conversationAssignedTo": "Conversation Assigned to", "copied": "<PERSON>pied", "copyFailed": "Co<PERSON> failed", "copyLink": "Copy Link", "councilTax": "Council Tax", "create": "Create", "createABill": "Create a bill", "tryLoftyLessThanBrGreaterThanWithYourTeamLessThan": "Try LoftyWorks <br /> with your team <br /> for free", "createABrandNewWorkspaceLessThanBrGreaterThanForYouY": "Create a brand-new workspace <br /> for you, your team and your <br /> properties", "createADocusignAccount": "Create a DocuSign account", "createAXeroAccount": "Create a Xero account", "createAndNext": "Create & Next", "createChecklistsForProceduresUsedByYourTeamForExample": "Create checklists for procedures used by your team. For example:", "createChecklistsForProceduresUsedByYourTeamForExampleP": "Create checklists for procedures used by your team. For example: Property Inspection\\n\" + \"Viewing\\n\" + \"Tenant Reference\\n", "createGroupChat": "Create Group Chat", "createNew": "+ Create New", "createPassword": "Create password", "createReport": "Create Report", "createTask": "Create Task", "createNewTaskType": "Create New Task Type", "editTaskType": "Edit Task Type", "taskTypeSuccessfullyCreated": "Task Type successfully created", "tastTypeSuccessfullyUpdated": "Task Type successfully updated", "createWorkspace": "CREATE WORKSPACE", "currency": "<PERSON><PERSON><PERSON><PERSON>", "currentAddress": "Current Address", "currentStatus": "Current Status", "dailyUpdates1": "Daily Updates", "dailyUpdates": "Daily Updates:", "date-0": "Date ...", "dateAdded": "Date added", "dateAvailable": "Date Available", "dateIsRequired": "Date is required", "dateOfBirth": "Date of Birth", "dateOfBirthday": "Date of Birthday", "dateRegistered": "Date Registered", "dateValidateMessage": "The date must be in the past", "dates": "Dates", "days": "Days", "daysAfterRent": "Days after rent is due", "daysInArrears": "Days in Arrears", "deadline": "Deadline", "debit": "Debit", "defaultInvoiceSettings": "Default Invoice Settings", "defaultManagementFee": "Default Management Fee", "defineYourLease": "Define Your Lease", "delete": "Delete", "deleteApplication": "Are you sure to delete this application?", "deleteApplicationYes": "Yes, Delete it", "deleteAttachment": "Delete attachment", "deleteChecklist": "Delete Checklist", "deleteColumn": "Delete column", "deleteContact-0": "Delete Contact", "deleteContact1": "Delete contact?", "deleteContact": "Delete contact", "deleteContract-0": "Delete Contract", "deleteContract1": "Delete contract", "deleteContract": "Delete Contract?", "deleteDocument": "Delete Document?", "deleteDocumentfordeleteName": "Delete \"{{documentForDelete}}\" ?", "deleteFilename": "Delete \"{{fileName}}\" ?", "deletePhoto": "Delete photo", "deletePhotosSection": "Delete photos section", "deleteProperty1": "Delete Property", "deleteProperty": "Delete Property?", "deleteTask1": "Delete Task", "deleteTask": "Delete task", "deleteTeamLandlord": "Delete Team Landlord?", "deleteTenant": "Delete Tenant?", "deleteThisManager": "Delete this Manager?", "deleteTitle": "Delete {{title}}?", "demoAndCo": "Demo & Co", "dependantInformation": "Dependant Information", "dependants": "Dependants", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "depositAmount": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "deposits": "Deposits", "detailedListOfAllActiveTenancyContractsByProperty": "Detailed list of all active tenancy contracts by property", "details": "Details", "direct": "Direct", "directChat": "Direct chat", "directMessage": "Direct Message", "disable": "Disable", "disabled": "Disabled", "disconnect": "Disconnect", "divorced": "Divorced", "doYouChargeLateFees": "Do you charge late fees for unpaid rent?", "doYouHaveAnyDependants": "Do you have any Dependants?", "doYouHaveAnyVehicles": "Do you have any vehicles?", "doYouHaveSnyPets": "Do you have any pets?", "doYouWantSetupRecurringInvoice": "Do you want to set up a recurring rent reminder?", "document": "Document", "documentDetails": "Document Details", "documentExpiryDate": "Document Expiry Date", "documentSentSuccessfully": "Document sent successfully!", "documentTemplates": "Document templates", "documentUpdatedSuccessfully": "Document updated successfully!", "documents": "Documents", "documentsRemindersSuccessfullyUpdated": "Documents reminders successfully updated", "documentsReport": "Documents Report", "docusign": "DocuSign", "done": "Done", "download": "Download", "downloadComplete": "Download Complete", "downloadCsvFile": "Download CSV File", "downloadDraftVersion": "Download draft version", "downloadPdfVersion": "Download PDF version", "downloadType": "Download {{type}}", "downloadWasCanceled": "Download was canceled", "downloading": "Downloading", "driversLicenseNumber": "Drivers License Number", "driversLicenseState": "Drivers License State", "due": "Due", "dueDate1": "Due date...", "dueDateIsRequired": "Due date is required", "dueDateTime": "Due Date/ Time", "dueDay": "Due Day", "dueToClient": "Due to Client", "duration": "Duration", "edit": "Edit", "editClientBillAmount": "Edit Client <PERSON>", "editContact": "Edit Contact", "editDetails": "Edit Details", "editFile": "Edit File", "editList1": "Edit List", "editList": "Edit list", "editProblemReport": "Edit Problem Report", "editProfile": "Edit profile", "editTheAmount": "Edit the amount", "editToDate": "Edit to date", "email": "Email", "emailAddress": "Email Address", "emailExistingError": "Marked email address already exists as a contact.", "emailInvoices": "Email Invoices", "emailIsMissing": "<PERSON><PERSON> is missing", "emailIsRequired": "Email is required", "emailIsInvalid": "<PERSON><PERSON> is invalid", "emailMessage": "Email message", "emailNotifications": "Email Notifications", "emailStatements": "Email Statements", "emergencyContacts": "Emergency Contacts", "emergencyContactsInformation": "Emergency Contacts Information", "emergencyLine": "Emergency line", "employed": "Employed", "employment": "Employment", "employmentInfo": "Employment Info", "employmentInformation": "Employment Information", "enable": "Enable", "end": "End", "endDateMustBeAfterStartDate": "End Date must be after Start Date", "ends": "Ends", "enter": "Enter", "enterAnAddressToBeIncludedOnStatementsAndInvoices": "Enter an address to be included on statements and invoices.", "enterDetailsHere": "Enter details here", "enterMonthNumber": "Enter Months Number", "enterName": "Enter name", "enterEmail": "Enter email", "enterNumbers": "Enter Numbers", "enterPhoneNumber": "Enter phone number", "enterYearNumber": "Enter Years Number", "enterYourContactDetails": "Enter your contact details, and we'll let the rental manager know you want to submit an application. If they're interested, they'll contact you with next steps.", "enteraValidEmail": "Enter a valid email", "enteraValidPhoneNumber": "Enter a valid phone number", "enteringATextStringWillDisplayAsTextStringEnteredStart": "Entering a text string will display as: [Text string entered], [start period] - [end period].", "epcAndTheEnvironment": "EPC & The Environment", "epcRating": "EPC Rating", "error": "Error", "errorOnUploadingItemNameFile": "Error on uploading {{name}} file!", "exchangeDate": "Exchange Date", "exclusiveVat": "Exclusive VAT", "exemptionCert": "Exemption Cert", "exemptionCertificateNo": "Exemption Certificate No", "exemptionDate": "Exemption Date", "expectedMoveIn": "Expected Move-in", "expensesOutstanding": "Expenses Outstanding", "expensesPropertyFinance": "Expenses - Property - Finance", "expires": "Expires", "export": "Export", "every": "Every", "failedToConnect": "Failed to Connect", "fee": "Fee", "female": "Female", "fields": "Fields", "file": "File", "fileName0": "File Name...", "fileName1": "File name...", "fileName": "File Name", "fileTitle": "File Title", "fileUpdatedSuccessfully": "File updated successfully!", "files": "Files", "fillOutAllInformationOrContactUs": "i.e. Please fill out all the information, if you have any questions, contact us at 555-123-4556", "filter": "Filter", "filterByProperty": "Filter by Property", "filterByAssignee": "Filter by <PERSON><PERSON><PERSON>", "filterByDueDate": "Filter by Due Date", "filterInArrears": "Filter in Arrears", "finalizedBy": "Finalized by", "finance": "Finance", "financialInformation": "Financial Information", "finish": "Finish", "firstDigitShouldNotBeZero": "First digit should not be 0", "firstName": "First Name", "firstNameIsRequired": "First Name is required", "firstReminder": "First Reminder", "firstTakeAPhotoOfTheProblemYouWantToReport": "First, take a photo of the problem you want to report", "fixedFee": "Fixed {{currency}}", "float": "Float", "floorplans": "Floorplans", "forAdminsOnlyClickTemplateForEachDataSetToDownloadAS": "For Admins only. Click ‘template’ for each data set to download a spreadsheet which can be imported.", "forAdminsOnlyExportPropertiesContractsAndContactsAsThe": "For Admins only. Export Properties, Contracts and Contacts as the core data from LoftyWorks.", "forClientAccountingGenerateACashBalanceAndAllocationRep": "For client accounting: generate a cash balance and allocation report", "forInternalUseOnly": "For Internal Use Only", "forThePeriod": "For the Period", "forTheTemplateSpreadsheet": "for the template spreadsheet.", "forgotMyPassword": "Forgot my password", "forgotPassword": "Forgot Password", "forgotPasswordUserNotFoundErr": "We do not recognize this email address. Please try again.", "forgottenPasswordRentancy": "Forgotten Password - LoftyWorks", "forgottenPassword": "Forgotten Password", "forwardingInbox": "Forwarding inbox", "from": "From", "fromDate": "From Date", "fromIsRequired": "From is required", "fromTo": "From/To", "fullName": "Full Name", "fullProfile": "Full Profile", "fundClient": "Fund Client", "fundSuppliers": "Fund suppliers", "furnitureFurnished": "Furniture Furnished", "furtherDataCanBeExportedDirectlyFromYourFinancialGenera": "Further data can be exported directly from your financial general ledger.", "gender": "Gender", "generalInfo": "General Info", "generalInstructions": "General Instructions", "generate": "Generate", "generatePdf": "Generate PDF", "generateReport": "Generate Report", "generateXLS": "Generate XLS", "giveYourNewLessThanBrGreaterThanWorkspaceAName": "Give your new <br /> workspace a name", "global": "Global", "globalCurrency": "Global Currency", "goTo": "Go to", "group": "Group", "groupInfo": "Group Info", "guarantor1": "GUARANTOR", "guarantor": "Guarant<PERSON>", "guarantors": "Guarantors", "healthAndSafety": "Health & Safety", "held": "Held", "home": "Home", "homeAddress": "Home Address", "howMuchCharge": "How much would you like to charge", "howMuchShouldCharge": "How much should we charge?", "howOftenChargeRent": "How often do you charge rent?", "howToUse": "How to use:", "hyperlink": "Hyperlink", "iConfirmThatIHaveReadTheRelevantAdviceProvidedInTheP": "I confirm that I have read the relevant advice provided in the PDF below", "iCurrentlyWorkHere": "I currently work here", "iCurrentlyWorkStudyHere": "I currently work/study here", "iHaveCoApplicants": "I have co-applicants", "iOwnThisProperty": "I own this property", "iRentThisProperty": "I rent this property", "iban": "IBAN", "identification": "Identification", "ifYouAreVatRegisteredEnterYourVatCodeHere": "If you are VAT  registered, enter your VAT code here.", "ifYourWorkspaceIsSetToAgentSpecifyYourManagementFeeYo": "If your workspace is set to Agent specify your management fee you charge for contracts.", "imApplyingAlone": "I’m applying alone", "import": "Import", "importExport": "Import / Export", "importingDataWillAppendNewRecordsToThisWorkspaceAndCan": "Importing data will append new records to this workspace and cannot be undone.", "inArrears": "In Arrears", "inCaseOfEmergency": "In case of emergency", "inProgress": "In Progress", "inactive": "Inactive", "inbox": "Inbox", "inboxEmail": "Inbox Email", "includeTheWorkspaceWebsiteIncludingHttpsAndTheLogoInTh": "Include the workspace website including https:// and the logo in the top left corner will hyperlink to the address", "includes": "Includes", "inclusiveVat": "Inclusive VAT", "income": "Income", "incomeOutstanding": "Income Outstanding", "incomeRaisedNotPaid": "Income Raised Not Paid", "instructions": "Instructions", "insurance": "Insurance", "integrations": "Integrations", "internal": "Internal", "internalNotes": "Internal Notes", "internalOnly": "Internal Only", "invalidData": "Invalid data!", "invalidDate": "Invalid date", "invalidEmailAddress": "Invalid email address", "invalidPhoneNumberLength": "Invalid phone number length", "invitationAccepted": "Invitation Accepted", "invitationPending": "Invitation pending", "invite": "Invite", "inviteTeamMember": "Invite Team Member", "inviteTo": "Invite to", "inviteToWorkspace": "Invite to Workspace", "invoiceCount": "Invoice Count", "invoiceDate": "Invoice Date", "invoiceGeneratedSuccessfully": "Invoice generated successfully!", "invoiceInAdvance": "Invoice in Advance", "defaultAccountCode": "De<PERSON>ult Account Code", "invoiceLineItem": "Invoice Line Item", "invoiceReference": "Invoice Reference", "invoiceRentInAdvanceDaysErrorMessage": "Please note that the invoice due date and send date need to be in the future. This is to prevent an invoice period being missed", "invoiceTemplates": "Invoice Templates", "invoicesCount": "Invoices Count", "invoicesFinanceRentancy": "Invoices - Finance - LoftyWorks", "invoicing": "Invoicing", "isServicePet": "Is Service Pet", "itIsNotPossibleToEditYourEmailAddressPleaseContactRen": "It is not possible to edit your email address. Please contact <NAME_EMAIL>", "itemNameUploadedSuccessfully": "{{name}} uploaded successfully!", "jobDescription": "Job Description", "jobType": "Job Type", "joined": "Joined", "jumpHereIfYouAlreadyHaveAnLessThanBrGreaterThanActiv": "Jump here if you already have an <br /> active account or invite to an <br /> existing workspace.", "keyFeatures": "Key Features", "label": "Label", "landingPage": "<PERSON>", "landingPageUrl": "Landing page URL", "landlord1": "LANDLORD", "landlord": "Landlord", "landlordBillSkipped": "Landlord bill skipped!", "landlordBillUpdatedSuccessfully": "Landlord bill updated successfully!", "landlordInformation": "Landlord Information", "landlordItemCompanyName": "Landlord - {{companyName}}", "landlordName": "Landlord Name", "landlordReport": "Landlord Report", "landlords": "Landlords", "landlordsIdTitleFnameSnameEmailsEmailTypePhonesPhoneTy": "landlords { id title fname sname emails{email type} phones{phone type} image{key} }", "lastName": "Last Name", "lastNameIsRequired": "Last Name is required", "lastSynchronizationWasOn": "Last synchronization was on", "lastUpdate": "Last Update:", "lateFeeFrequency": "Late Fee Frequency", "lateFees": "Late Fees", "learnMore": "Learn More", "leaseDetailsFee": "Lease Details & Fee", "leaseTerms": "Lease Terms", "leave": "Leave", "leaveFieldsBlankToUseSystemDefaultsOfContractTypeConta": "Leave fields blank to use system defaults of [contract type], [contact title], [start period] - [end period]. Entering values will display as [Text entered], [start period] - [end period].", "leaveFieldsBlankToUseTheDefaultContractTypeContactTitl": "Leave fields blank to use the default: [contract type], [contact title], [start period] - [end period].", "leaveGroup": "Leave group", "ledgerCode": "Ledger Code", "ledgerCodes": "Ledger Codes", "ledgers": "Ledgers", "less": "Less", "lessThanPGreaterThanThisIsWhereYouCanManageAllOfYou": "<p> This is where you can manage all of your properties, <br /> conversations, tenancies and securely store your related documents. </p> <p>You can also invite team members, tenants, and landlords.</p>", "letUsKnowAndWellContactYou": "Let the property know when you're available, and the property will contact you to arrange a tour.", "letterGeneratedSuccessfully": "Letter generated successfully", "letterPreview": "Letter preview", "letterTemplate": "Letter template", "licensePlate": "License Plate", "lineItems": "Line Items", "link": "Link", "unLink": "Unlink", "linkPropertyOrContract": "Link Property or Contract…", "linkToClipboard": "Link copied to clipboard.", "linkToProblemReport": "Link to Problem Report...", "links": "Links", "list": "List", "listView": "List View", "listingStatus": "Listing Status", "listingSyndication": "Listing Syndication", "loading": "loading...", "localCurrencySign": "$", "localDateFormat": "MMM DD YYYY", "loftyWorks": "LoftyWorks", "logInLofty": "Log In - Lofty Works", "loginZillowAccount": "Login Zillow account, go to settings, click Connect My CRM and enter the code below.", "logIn": "Log in", "logInRentancy": "Log In - Lofty Works", "logo": "Logo", "longTerm": "Long Term", "mainContact": "Main contact", "mainImage": "Main Image", "make": "Make", "makeSureItHasAtLeast_8CharactersWeSuggestLessThanBrG": "Make sure it has at least 8 characters, we suggest using upper and lower case, as well as numbers and special symbols.", "male": "Male", "manageDocumentTemplatesHere": "Manage document templates here", "manageWorkspaces": "Manage Workspaces", "deleteWorkspace": "Delete Workspace", "manageYourInvoicesExpensesAndLessThanBrGreaterThanChec": "Manage your invoices, expenses and <br /> check bank transactions here.", "managementFee": "Management Fee", "manager": "Manager", "managers": "Managers", "mapLedgerCode": "Map Ledger Code", "maritalStatus": "Marital Status", "markAllAsRead": "Mark all as read", "markAsMandatory": "Mark as mandatory", "markAsResolved": "<PERSON> as Resolved", "markedAlreadyExistsError": "Marked {{type}} already exists as a contact.", "marketingDetails": "Marketing Details", "marketingInformation": "Marketing Information", "married": "Married", "maxCharactersAreAllowed": "Maximum {{number}} characters are allowed", "maxFileCount": "Max file count: {{maxFiles}}.", "maxFileSize": "Max file size: {{maxSize}}.", "maxFileSizeMaxsize_1000000Mb": "Max file size: {{maxSize}}Mb.", "maxInputRangeIs": "Maximum input range is {{length}}", "menu": "<PERSON><PERSON>", "message": "Message", "method": "Method", "middleName": "Middle Name", "minInputRangeIs": "Minimum input range is {{length}}", "mine": "Mine", "minimumBalance": "Minimum Balance", "mobileHome": "Mobile Home", "model": "Model", "month": "Month", "monthly": "Monthly", "monthlyPayment": "Monthly Payment", "monthlyRent": "Monthly Rent", "months": "Months", "monthsRemaining": "Months Remaining", "more1": "+ More", "more": "More", "moveInDate": "Move-in Date", "moveInMoveOut": "Move-in/Move-out", "multiFamily": "Multi Family", "mute": "Mute", "myPetIsSpayedNeutered": "My pet is spayed/neutered ", "myWorkspaces": "My Workspaces", "name": "Name", "nameEmailAddressOrNumber": "Name, email address or number…", "nameIsRequired": "Name is required", "nameOrEmail": "Name or email", "nameYourWorkspaceWithAUniqueNameThatRepresentsYourComp": "Name your workspace with a unique name that represents your company or organization.", "loftyworksGeneratesYourWorkspaceIDByRemovingSpacesFromYourWorkspaceName": "LoftyWorks generates your Workspace ID by removing spaces from your Workspace name from the first time you enter. The Workspace ID is unique for each workspace in LoftyWorks, it remains fixed and cannot be changed.", "netIncomeInPeriod": "Net Income in Period", "neverMarried": "Never married", "new": "New", "newApplication": "New Application", "newApplications": "New Applications", "newName": "New Name", "newPassword": "New Password", "newProspect": "Add New Prospect", "newWorkspace": "New Workspace", "newest": "Newest", "next1": "NEXT", "next": "Next", "nextDays": "Next {{item}} days", "nextRentInvoiceAmount": "Next Rent Invoice Amount", "nextRentInvoiceDueDate": "Next Rent Invoice Due Date", "nextRentInvoiceSendDate": "Next Rent Invoice Send Date", "no": "No", "noActivity": "No activity…", "noApplications2": "This application hasn’t been filled out yet.", "noApplicationsSub": "You can send the link below or send a reminder to the applicant to fill it out.", "noAttachmentsYet": "No attachments yet…", "noContractYet": "No Contract yet…", "noDataListed": "No data listed", "noDocumentsYet": "No documents yet…", "noFee": "No Fee", "noInvoiceFoundForThisSupplier": "No invoice found for this supplier", "noNotificationsRightNow": "No notifications right now", "noOptionClickHereToCreateYourFirstChecklistInSettings": "No option, Click here to create your first Checklist in Settings!", "noRecords": "No records…", "noResultsFor": "No results for", "nonResident": "Non Resident", "none": "None", "notAllowed": "Not Allowed", "notSet": "Not Set", "noteClientsAndTenantsCanOnlyAccessALimitedSubsetOfThe": "Note: clients and tenants can only access a limited subset of the data contained in LoftyWorks related to the properties and contracts they are connected.", "notes": "Notes", "nothingFound": "Nothing Found", "noticePeriod": "Notice Period", "notificationSuccessfullyUpdated": "Notification successfully updated", "notifications": "Notifications", "nowLetsInviteLessThanBrGreaterThanYourColleagues": "Now let’s invite <br /> your colleagues", "nrlSummaryReport": "NRL Summary Report", "nrlTax": "NRL Tax", "number": "Number", "numberRefOrTo": "Number, Ref or To...", "off": "Off", "ok": "OK", "oldest": "Oldest", "on": "On", "once": "Once", "onceSavedTheSystemWillAutomaticallySendAnEmailToThisA": "Once saved, the system will automatically send an email to this address (from Amazon Web Services). Click the link in the email and verify usage.", "onceSetThisWillApplyAsDefaultToNewInvoices": "Once set this will apply as default to new invoices.", "onceSetWeDoNotRecommendChanging": "Once set, we do not recommend changing.", "onceTheyHaveAcceptedTheInviteTheirStatusWillChangeToA": "Once they have accepted the invite, their status will change to a green tick.", "onceVerifiedTheSystemWillStartSendingEmailUsingThisAdd": "Once verified the system will start sending email using this address as the from address.", "onlyThoseTeamMembersAssignedTheSupportUserRoleWillBeI": "Only those team members assigned the Support user role will be included on new emails sent inbound.", "openPdf": "Open PDF", "openTasks": "Open Tasks", "opening": "Opening", "openingBalance": "Opening Balance", "openingBalances": "Opening Balances", "orUseTheLinkInTheInvitationEmail": "or use the link in the invitation email.", "orderBy": "Order by", "orderChangedSuccessfully": "Order changed successfully!", "orderType": "Order type", "organisation": "Organisation", "other": "Other", "otherLandlords": "Other Landlords", "otherNotes": "Other Notes", "overallocated": "Overallocated", "owedDueToLandlord": "Owed/Due to <PERSON>lord", "pageNotFound": "Page Not Found", "paid": "Paid", "paidDeposit": "<PERSON><PERSON>", "paidIncome": "Paid <PERSON>", "paidThisMonth": "Paid This Month", "paidTotal": "Paid Total", "password": "Password", "passwordIsRequired": "Password is required", "passwordMustBeAtLeast_8Characters": "Password must be at least 8 characters", "passwordUpdated": "Password Updated", "payOut": "Pay out", "payments": "Payments", "payout": "Payout", "payoutsCanBeProcessed": "Payouts can be processed", "pendingSince": "Pending since", "people": "People", "peopleRentancy": "People - LoftyWorks", "percentOfFullAmount": "{{amount}}% of Full Amount", "percentOfReceived": "{{amount}}% of Received", "percentage": "Percentage (%)", "percentageOfAmountReceived": "Percentage of Amount Received (%)", "percentageOfFullAmount": "Percentage of Full Amount (%)", "period": "Period", "petInformation": "Pet Information", "petName": "Pet Name", "petPolicy": "Pet Policy", "pets": "Pets", "pgHeaderLogin": "Log in", "phoneNumber": "Phone number", "phoneNumberIsRequired": "Phone number is required", "photos": "Photos", "photosAndVideos": "Photos And Videos", "photosVideo": "Photos & Videos", "pickYourEmoji": "Pick your emoji…", "planned-0": "Planned", "planned": "Planned", "pleaseAddEmailToInvite": "Please add email to invite", "pleaseAddWhatsappDetailsToContact": "Please add Whatsapp details to contact", "pleaseAvailableDates": "Please select your available dates", "pleaseCheckYourEmail": "Please check your email.", "pleaseContactYourWorkspaceAdminToSetupXeroConnection": "Please contact your workspace admin to setup Xero connection.", "pleaseDoubleCheckYourEmail": "Please double check your email.", "pleaseEnterCorrectAmount": "Please enter correct Amount", "pleaseEnterCorrectEmail": "Please enter correct email!", "pleaseEnterCorrectPhone": "Please enter correct phone!", "pleaseEnterCorrectUrl": "Please enter correct url!", "pleaseEnterEmailAddress": "Please enter email address", "pleaseEnterName": "Please enter name", "pleaseEnterTheCorrectEmailAddress": "please enter the correct email address", "pleaseEnterValidNumber": "Please Enter Valid Number", "pleaseSelectAChatToGetStartedLessThanBrGreaterThanOr": "Please select a chat to get started, <br /> or create a new one.", "pleaseSelectAFile": "Please select a file!", "pleaseSelectASupplier": "Please select a supplier", "pleaseSelectAccount": "Please select account!", "pleaseSelectPropertyOrContract": "Please select property or contract", "pleaseSelectTheColumn": "Please select the column", "pleaseSelectTheRole": "Please select the role:", "pleaseSetupYourXeroConnectionToBegin": "Please setup your Xero connection to begin.", "pleaseSignThisDocument": "Please sign this document", "pleaseTryDifferentSearchTerms": "Please try different search terms", "pleaseUploadTheSignedLease": "Please upload the signed lease", "policies": "Policies", "portal": "Portal(Landlord or Tenant)", "portalHomepage": "Portal Homepage", "portfolioDataReportXls": "Portfolio Data Report XLS", "portfolioDeletedSuccessfully": "portfolio deleted successfully", "post": "Post", "postalAddress": "Postal Address", "postcode": "Post code", "prefStartDate": "Pref Start Date", "preferredStartDate": "Preferred start date:", "preferredTemplate": "Preferred Template", "preferredTemplateUpdatedSuccessfully": "Preferred Template updated successfully!", "previewPublish": "Preview & Publish", "previewQuestionnaire": "Preview Questionnaire", "price": "Price", "primaryContact": "Primary contact", "primaryLandlord": "Primary Landlord", "primaryLandlordSuccessfullyChangedToLandlordCompanyName": "Primary landlord successfully changed to {{companyName}}", "primaryManager": "Primary Manager", "primaryPhone": "Primary Phone", "primaryTenant1": "Primary tenant", "primaryTenant": "Primary Tenant", "primaryTenantChangedSuccessfully": "Primary tenant changed successfully!", "primarytenantIdTitleFnameSnameEmailsEmailTypePhonesPhon": "primaryTenant { id title fname sname emails{email type} phones{phone type} image{key} }", "print": "Print", "problemReportSuccessfullyUpdated": "Problem Report successfully updated", "problemReports": "Problem Reports", "profile": "Profile", "profileImageSuccessfullyChanges": "Profile image successfully changes", "profileImageSuccessfullyUpdated": "Profile image successfully updated", "profit": "Profit", "properties": "Properties", "property1": "Property ...", "propertyAddress": "Property Address", "propertyAddressline1CityPostcode": "property { addressLine1 city postcode }", "propertyBalancesReport": "Property Balances Report", "propertyContractOrClientName": "Property, Contract or Client Name…", "propertyContracts": "Property & Contracts ({{length}})", "propertyCount": "Property Count", "propertyCreatedSuccessfully": "Property created successfully!", "propertyDeletedSuccessfully": "Property deleted successfully", "propertyDetails": "Property Details", "propertyFloat": "Property Float", "propertyIsRequired": "Property is required!", "propertyLease": "Property & Lease", "propertyLeaseTerm": "Property & Lease Term", "propertyManager": "Property Manager", "connectedSuppliers": "Connected Suppliers", "connectAsupplier": "Connect a supplier", "ConnectASupplier": "Connect a Supplier", "propertyManagers": "Property Managers", "propertyProfit": "Property Profit", "propertyReference": "Property Reference", "propertyType": "Property Type", "propsTitleImportedSuccessfully": "{{title}} imported successfully.", "protectionScheme": "Protection scheme", "providesConsolidatedListOfAllDocumentsLinkedToYourPrope": "Provides consolidated list of all documents linked to your properties along with their expiration dates.", "published": "Published", "qty": "Qty", "qtyIsRequired": "Qty is required", "rate": "Rate", "rateIsRequired": "Rate is required", "reachOutToZillowAndSendCompanyId": "Please reach out to ZIllow at the email address below and send them your Company ID", "readyToApply": "Ready To Apply", "reasonForLeaving": "Reason for leaving", "receiveThePdfVersionOfTheStatementInYourMailbox": "Receive the PDF version of the statement in your mailbox", "received": "Received", "receivers": "Receivers", "recently": "Recently", "reconciled": "RECONCILED", "reconnect": "Reconnect", "ref": "Ref", "referenceShouldNotContainSlash": "Reference shouldn't contain slash", "refreshTable": "Refresh table", "relationship": "Relationship", "remainingApiCallsAre": "Remaining API calls are", "reminders": "Reminders", "remittedAmount": "Remitted Amount", "remove": "Remove", "removeAttachment": "Remove attachment", "removeChecklist1": "Remove checklist", "removeChecklist": "Remove Checklist", "removeCodeMapping": "Remove Code Mapping", "removeExpiry": "Remove Expiry", "removeFromGroup": "Remove from group", "removeUnit": "Remove Unit", "removeUser": "Remove user", "removeWholeSection": "Remove whole section", "rename": "<PERSON><PERSON>", "renameColumn": "Rename column", "renameDocument": "Rename Document", "renameGroup": "Rename group", "renewalDate": "Renewal Date", "rent": "Rent", "rentAmount": "Rent Amount", "rentAmountIsSpecified": "Rent amount is specified", "rentDay": "Rent day", "rentIncludes": "Rent Includes", "rentInvoiceDay": "Rent Invoice Day", "rentReview": "Rent Review", "rentalApplications": "Rental Applications", "rentalTeams": "Rental Teams", "rentancyLetsYouApplyDifferentInvoiceTemplatesToDifferent": "LoftyWorks lets you apply different invoice templates to different contract types.", "rentancyPeriodicallyIncreasesTheFormatsOfStatementsOffere": "LoftyWorks periodically increases the formats of statements offered. Select a type of statement and press save.", "rentancyProvidesAPublicHomepageWhichYouCanIntegrateWith": "LoftyWorks provides a public homepage which you can integrate with your website.", "rentancyStandard": "LoftyWorks Standard", "rentancyUsesXeroTrackingCategoriesToTrackInvoicesAndBil": "LoftyWorks uses Xero Tracking Categories to track invoices and bills by property and contract.", "rentancyWillReadEmailsSentToThisAddressAndIncludeThem": "LoftyWorks will read emails sent to this address and include them in the Inbox.", "rentancyWillSendAnyUnreadMessages_10MinutesAfterTheyHav": "LoftyWorks will send any unread messages 10 minutes after they have been sent.", "reportAProblem": "Report A Problem", "reportProblem": "Report problem", "request": "Request", "campaign": "Campaign", "brand": "Brand", "register": "Register", "registerNow": "Register Now", "requestATour": "Request a tour", "requestHasBeenSentToYourEmail": "Request has been sent to your email", "requestSetupByOurAdmins": "Request setup by our admins", "requestToApply": "Request to Apply", "requestToTour": "Request a tour", "required": "Required", "requiredField": "Required field", "resendApplicationDoubleRequest": "This request has already been sent, and you cannot send a double request.", "resendApplicationRequest": "Are you sure to send this application request again?", "resendApplicationRequestYes": "Yes, re-send it", "resendCode": "Resend code", "resendInvite1": "Resend invite", "resendInvite": "Resend Invite", "sendInvitation": "Send Invitation", "resendRequest": "Re-send request", "reservation": "Reservation", "reset": "Reset", "residence": "Residence", "residenceMonthlyPayment": "Residence Monthly Payment", "residentialHistory": "Residential History", "resolve": "Resolve", "resolved": "Resolved", "unresolved": "Unresolved", "restore": "Rest<PERSON>", "resultsFor": "results for", "retrievingDataForNewConnectionItCanTakeSeveralMinutes": "Retrieving data for new connection, it can take several minutes…", "returnToListingDetail": "Return to listing detail", "reviewDate": "Review Date", "reviewNotice": "Review Notice", "roleIsRequired": "Role is required", "rolesCanBeAssignedToTeamMembersOnlyAfterTheyHaveCreat": "Roles can be assigned to team members only after they have created their user account.", "room": "Room", "routingNumber": "Routing Number", "salesFee": "Sales Fee", "save": "Save", "saveAndNext": "Save and Next", "savedSuccessfully": "Saved successfully!", "scheme": "Scheme", "school": "School", "screeningReports": "Screening Reports", "search": "Search", "searchAddressCityZipcodeNeibourhood": "Search by Address, City, Zipcode, Neighborhood ...", "searchByNameEmail": "Search by name, email", "secondReminder": "Second Reminder", "secrityDeposit": "Secrity Deposit", "securityDeposit": "Security Deposit", "select-0": "Select", "select1": "Select", "select": "Select ...", "selectATemplateThenContactsForSigningWillAppearHere": "Select a template, then contacts for signing will appear here.", "selectAfewDatesForYourAvailability": "Select a few dates you're available", "selectApplicant": "Please select applicant that you want to send the application to", "selectCategory": "Select category...", "selectDate": "Select Date", "selectFile": "Select file…", "selectProspect": "Please select prospect that you want to send the application to", "selectThisOnlyTheFirstTimeYouUseRentancyWeDoNotRecom": "Select this only the first time you use LoftyWorks. We do not recommend changing this setting after you have started using LoftyWorks.", "selectType1": "Select type", "selectType": "Select Type", "selectTypeAndTimeframe": "Select type, user and timeframe to create a ladger", "selectedTenantsDontHaveEmail": "Please select tenants with email specified", "selectedTenantsDontHavePhone": "Please select tenants with phone number specified", "selfEmployed": "Self Employed", "send": "Send", "sendEmail": "Send Email", "sendInvite": "Send Invite", "sendLetter": "Send Letter", "sendReportByEmail": "Send Report By Email", "sendRequest": "Send Request", "sendSMS": "Send SMS", "sendToDocusign": "Send to Docusign", "sentTourRequest": "Send Tour Request", "separated": "Separated", "service": "Service", "services": "Services", "setTheCurrencyFlagToMatchYourFinancialLedgerThatRentan": "Set the currency flag to match your financial ledger that LoftyWorks connects to.", "setThisSectionAsARequirementToSubmitTheApplication": "Set this section as a requirement to submit the application", "settingsSuccessfullyUpdated": "Settings successfully updated", "setupUploadedDocuments": "Setup uploaded documents:", "share": "Share", "shareLink": "Share Link", "sharePropertyTo": "Share Property to", "shareToExt": "Share to External Calendar", "shared": "Shared", "sharedWith": "Shared With", "sharing": "Sharing", "shortTerm": "Short Term", "eitherTerm": "Either", "showBillsWithWarning": "Show bills with warning", "showOnMap": "Show on Map", "showPast": "+ Show Past", "showingApplication": "Showing & Application", "signOut": "Sign out", "signUp1": "Sign up", "signUp": "Sign Up", "simpleTable": "simple table", "since": "since", "singleFamily": "Single Family", "sizeSqft": "Size (Sqft)", "skip": "<PERSON><PERSON>", "skipForNow": "Skip for now", "skipIntro": "SKIP INTRO", "skipThisStep": "Skip this step", "smoking": "Smoking", "sms": "SMS", "software": "Software", "somethingIsWrongWithYourDocusignIntegrationStatus": "Something is wrong with your DocuSign integration status", "somethingIsWrongWithYourWhatsappIntegrationStatus": "Something is wrong with your whatsapp integration status", "somethingIsWrongWithYourXeroIntegrationStatus": "Something is wrong with your Xero integration status", "somethingWentWrong": "Something went wrong", "somethingWentWrongPleaseTryAgainLater": "Something went wrong. Please try again later", "somethingsLessThanBrGreaterThanMissing": "Something’s <br /> missing…", "sorrySomethingWentWrong": "Sorry, something went wrong!", "sort": "Sort", "sortCode": "Sort Code", "source": "Source", "specificContract": "Specific Contract", "specifyIfYouWouldLikeAnInvoiceToBeGeneratedANumberOf": "Specify if you would like an invoice to be generated a number of days before it becomes due.", "spent": "Spent", "squareFeet": "Square Feet", "squareMeters": "Square Meters", "ssnOrItiN": "SSN or ITIN", "start": "Start", "startAConversation": "Start a Conversation", "startDateIsCompleted": "Start date is completed", "startFrom-0": "Start From", "startFrom": "Start from", "starts": "Starts", "state": "State", "statement": "Statement", "statementSentToYourEmailPleaseCheck": "Statement sent to your email, please check", "statementToDate": "Statement To Date :", "statements": "Statements", "status": "Status", "step_1CreateAWordDocumentIncludingFieldsYouWantToMerge": "Step 1: create a word document including fields you want to merge from LoftyWorks.", "step_2UploadHereMarkApprovedIfFinalVersion": "Step 2: upload here. <PERSON> approved if final version.", "step_3ForManyDocumentsClickEditListAndAddTypesToGive": "Step 3: for many documents, click Edit List and add types to give groupings to your", "step_4ClickOnTheCogIconToAssignTheDocumentToAPropert": "Step 4: click on the Cog icon to assign the document to a property, contract or contact.", "step_5VisitDocumentsSectionsUnderPropertyContractOrConta": "Step 5: visit documents sections under property, contract or contact and click Generate to create a merge document that uses the templates and relevant fields.", "student": "Student", "subject": "Subject", "submit": "Submit", "submittedAt": "Submitted At", "submitting": "Submitting..", "succesfullyRemoved": "Succesfully removed!", "successfully": "successfully", "successfullyDisconnected": "Successfully Disconnected!", "successfullyRemoved": "Successfully removed.", "successfullyUpdated": "Successfully updated!", "summary": "Summary", "supervisionEmail": "Supervision Email", "supervisionName": "Supervision Name", "supervisionPhone": "Supervision Phone", "supervisorEmail": "Supervisor Email", "supervisorInformation": "Supervisor Information", "supervisorName": "Supervisor Name", "supervisorPhone": "Supervisor Phone", "suppliers": "Suppliers", "supportedFormats": "Supported formats", "swift": "SWIFT", "switch": "Switch", "sync": "Sync", "synchroniseDataStartingFrom": "Synchronize data starting from", "tUnseenpropertyupdates": "{t(\"unseenPropertyUpdates\")}", "tapToAddAProblemReport": "Tap + to add a Problem Report", "task": "Task", "taskChecklists": "Task Checklists", "taskDueDate": "Task Due Date", "taskDueDateRemindersSuccessfullyUpdated": "Task due date reminders successfully updated!", "taskName": "Task name", "taskSuccessfullyArchived": "Task successfully archived", "taskSuccessfullyDeleted": "Task successfully deleted", "taskSuccessfullyRestored": "Task successfully restored", "taskSuccessfullyCreated": "Task successfully created", "taskTitle": "Task title...", "tasks": "Tasks", "tasksReport": "Tasks Report", "tax": "Tax", "taxTypeIsRequired": "Tax Type is required", "team": "Team", "teamMember1": "Team Member", "teamMember": "Team member", "teamMembers": "Team members", "templateToUse": "Template to use:", "tenancies": "Tenancies", "tenancyLength": "Tenancy Length", "tenancyReport": "Tenancy Report", "tenant1": "TENANT", "tenant": "Tenant", "tenantYouWantToLease": "Tenant you want to lease", "tenants": "Tenants", "tenantsLandlordsGuarantors": "Tenants, Landlords, Guarantors", "term": "Term", "terms": "Terms", "termsAndConditions": "Terms & Conditions", "text": "Text", "theAmountShouldDecimalMaximumTwoDigits": "The amount should be a decimal with maximum two digits after dot", "theContactHasBeenCreated": "The contact has been created!", "theContactHasBeenUpdated": "The contact has been updated!", "theContactHasBeenDeleted": "The contact has been deleted!", "theFirstLineOfTheAddressShouldBeYourLegalCompanyOrgan": "The first line of the address should be your legal company/organization name.", "theFollowingFieldsAreAvailableToIncludeIntoTemplateDocu": "The following fields are available to include into template documents", "theInvitationHasBeenResent": "The invitation has been resent", "theLinkIsBrokenPleaseRequestANewLink": "The link is broken, please request a new link", "theLogoIsAlsoDisplayedInTheTopLeftCornerOfThisApplic": "The logo is also displayed in the top left corner of this application.", "theNameMustBeUniqueInRentancy": "The name must be unique in LoftyWorks", "theNewTypeAddedSuccessfully": "The new type added successfully.", "thePageNumberMustNotBeGreaterThanPropsCount": "The page number must not be greater than {{count}}", "thePageYourAreLookingForDoesLessThanBrGreaterThanNot": "The page your are looking for does <br /> not exist or you don’t have <br /> permissions to see it.", "theReportSentToYourEmailPleaseCheckYourEmail": "The Report sent to your email, please check your email.", "theStatementSuccessfullySent": "The statement successfully sent!", "thereAreAvailableFundsHeldOnTheLedger": "There are available funds of {{sum}} held on the ledger", "thereIsNoPropertyOrTenancyWithThisName": "There is no property or tenancy with this name", "thereIsNoPropertyWithThisName": "There is no property with this name", "thereIsNoResultForThisPostCode": "There is no result for this post code", "thereIsNoSuchContact": "There is no such contact", "thisActionWillAlsoRemoveAllContractsLinkedToThisProper": "This action will also remove all contracts linked to this property.", "thisActionWillRemoveLandlordFromThisProperty": "This action will remove Landlord from this Property", "thisActionWillRemoveManagerFromThisProperty": "This action will remove Manager from this Property", "thisActionWillRemoveSelectedContact": "This action will remove selected contact", "thisActionWillRemoveTenantFromThisProperty": "This action will remove Tenant from this Property", "thisActionWillRemoveTitleFromThisProperty": "This action will remove {{title}} from this Property", "thisAllowsDifferentContractsToBeSetupDisplayingDifferent": "This allows different contracts to be setup displaying different lines text within an invoice.", "thisAllowsYouToMessageWithYourContactShareDocumentsAnd": "This allows you to message with your contact, share documents and contracts.", "thisApplicationNotFilled": "This application hasn’t been filled out yet.", "thisAppliesToTeamMembersAddedToAContract": "This applies to Team Members added to a Contract", "inventoryManagement": "Inventory Management", "inventoryRemindersSuccessfullyUpdated": "Inventory reminders successfully updated!", "thisAppliesToTeamMembersAssignedToAPropertyOrContract": "This applies to Team Members assigned to a Property or Contract", "thisAppliesToTeamMembersAssignedToAProperty": "This applies to team members assigned to a property", "thisAppliesToTeamMembersAssignedToATask": "This applies to Team Members assigned to a Task", "thisCanBeAOrFixedFeeAndInclusiveOrExclusiveOfVat": "This can be a % or fixed fee and inclusive or exclusive of VAT.", "thisChatDoesnAndAposTExistPleaseSelectAChatToGetSta": "This chat doesn&apos;t exist, Please select a chat to get started, <br /> or create a new one. <br />", "thisCouldBeYourBusinessNameLessThanBrGreaterThanOrTe": "This could be your business name or team name", "thisDocumentGivesPermissionToTheStatedAboveContractorTo": "This document gives permission to the stated above “Contractor” to carry out only the outlined above job. Any additional request from any other party apart from “Main Contact” are to be declined and placed forward for the approval by “Main Contact”.", "thisEmailIsNotValid": "This email is not valid.", "thisFormAllowsYouToSendDocumentsForESigning": "This form allows you to send documents for e-signing.", "thisFunctionAllowsYouToCreateMultipleContractsForThisP": "This function allows you to create multiple contracts for this property from a spreadsheet. Click", "thisIsAUniqueAddressForYourWorkspaceYouCanSetUpForwa": "This is a unique address for your workspace. You can set-up forwarding emails from say support@ or maintenance@ to this email.", "thisMessageHasBeenDeleted": "This message has been deleted.", "thisSectionAppliesToAllTeamMembers": "This section applies to All Team Members.", "thisSectionIsReadOnlyIfYouSeeAnythingThatShouldBeCha": "This section is read-only. If you see anything that should be changed or updated here, please contact your workspace admin.", "thisWillBeSentToAdminsUserRolesOnlyItWillProvideASu": "This will be sent to Admins user roles only. It will provide a summary of contract expiring, tasks expiring and new properties and contracts added in the last day.", "thisWillHelpEnsureConsistencyAcrossTeamMembers": "This will help ensure consistency across team members.", "timeline": "Timeline", "title": "Title", "titlePosition": "Title/Position", "to": "To", "toApprove": "to approve", "toAutomaticallySynchroniseWithRentancy": "To automatically synchronize with LoftyWorks.", "toAutomaticallySynchroniseYourFinanceWithRentancy": "To automatically synchronize your finance with LoftyWorks.", "toEnableAutoInvoicingForThisContractLessThanBrGreater": "To enable auto Invoicing for this contract <br /> please check the following:", "toInviteMoreColleaguesVisit": "To invite more colleagues, visit", "toInviteMorePeopleVisit": "To invite more people, visit", "toInvitePeopleAddFromContacts": "To invite people, add from Contacts", "toPayout": "To Payout", "toRentancy": "to LoftyWorks", "toViewTheStatementOpenAPropertyAndGenerateAnAdhocStat": "To view the statement, open a property and generate an adhoc statement.", "today": "Today", "tomorrow": "Tomorrow", "total": "Total", "totalArea": "Total Area", "totalIncome": "Total Income", "totalInvoice": "Total Invoice", "town": "Town", "townhouse": "TownHouse", "trackingCategories": "Tracking Categories", "trackingCategoriesUpdatedSuccessfully": "Tracking Categories updated successfully!", "tryRentancyLessThanBrGreaterThanWithYourTeamLessThan": "Try LoftyWorks <br /> with your team <br /> for free", "typeFrequency": "Type frequency", "typeRemovedSuccessfully": "Type removed successfully.", "typeSomething": "Type something", "types": "Types", "typeToSearch": "Type to search...", "unallocated": "Unallocated", "unarchive": "Unarchive", "unassigned": "Unassigned", "unemployed": "Unemployed", "unit": "Unit", "unitCreatedSuccessfully": "Unit Created Successfully!", "unitDetails": "Unit Details", "unitEditDiscardChange": "Yes, Ignore all changes", "unitEditDiscardChangeTips": "Are you sure to discard all the changes?", "unitEditPageTitle": "Marketing Information", "unitIsRequired": "Unit is Required!", "unitName": "Unit Name", "unitPrice": "Unit price", "unitPriceIsRequired": "Unit price is required", "unitRemoveSuccessfully": "Unit remove successfully!", "units": "Units", "unmute": "Unmute", "unpublished": "Unpublished", "unreadMessages": "Unread Messages", "unreconciled": "UNRECONCILED", "unresolve": "Unresolve", "unseenContractUpdates": "Unseen Contract Updates", "unseenPropertyAndContractUpdates": "Unseen Property and Contract updates.", "unseenPropertyUpdates": "Unseen Property Updates", "unsupportedFileExt": "Unsupported file extension, only support: {{types}}", "update": "Update", "updatedSuccessfully": "Updated successfully!", "updates": "Updates", "upload": "Upload", "uploadALogoForYourWorkspaceThisWillBeUsedInReportsSu": "Upload a logo for your workspace. This will be used in reports such as Property Statements.", "uploadComplete": "Upload complete", "uploadDocument": "Upload Document", "uploadDocuments": "Upload Documents", "uploadFile": "Upload File:", "uploadLogo": "Upload logo", "uploadNew": "Upload New", "uploadNewFile": "Upload new file", "uploadWasCanceled": "Upload was canceled", "uploadedDocuments": "Uploaded Documents", "uploading": "Uploading", "useDataForAuditPurposes": "Use data for audit purposes.", "useStandardTemplateAsDefaultOrEnterTheNameOfTheTempla": "Use Standard template as default or enter the name of the template required as setup in your General Ledger.", "useTheDisplayNameFieldToControlTheNameOfLedgerCodesI": "Use the Display Name field to control the name of ledger codes in property statement reports, if required.", "useThisPageForYourTeamClientsAndTenantsToAccessTheSy": "Use this page for your team, clients and tenants to access the system.", "usecontactMustBeInsideInContactcontext": "useContact must be inside in ContactContext", "userRoleSuccessfullyAddedFinance": "User role successfully added finance", "userRoleSuccessfullyRemovedFromFinance": "User role successfully removed from finance", "userSuccessfullyActivated": "User successfully activated", "userSuccessfullyAddedToTheContract1": "User successfully added to the contract.", "userSuccessfullyAddedToTheContract": "User successfully added to the contract", "userSuccessfullyAddedToTheProperty": "User successfully added to the property", "userSuccessfullyDeleted": "User Successfully Deleted", "userSuccessfullyDisabled": "User successfully disabled", "userSuccessfullyRemovedFromTheProperty": "User successfully removed from the property", "userSuccessfullyUpdatedToTheProperty": "User successfully updated to the property", "utilities": "Utilities", "utilitiesAmenities": "Utilities & Amenities", "utilitiesAndAmenities": "Utilities and Amenities", "validUntil1": "Valid until", "validUntil": "<PERSON>id <PERSON>", "vat": "Vat", "vatNumber": "VAT Number", "vehicleInformation": "Vehicle Information", "vehicles": "Vehicles", "version_1": "Version 1", "videos": "Videos", "view": "View", "viewAssignee": "View assignee", "viewListing": "View Listing", "warning": "Warning", "warningCheckClientBalance": "Warning Check Client Balance", "warningClientBalance": "Warning: Client Balance", "warningThisSettingChangesYourWorkspaceSetupLessThanBrG": "Warning: this setting changes your workspace setup. Do not change after initial configuration.", "weAndAposReWorkingHardInTheBackgroundToBringYouMore": "We're working hard in the background to bring you more reports. <br /> If you have any suggestions, please let us know by emailing us at", "weHaveSentAnEmailWithA_6DigitCodeLessThanBrGreaterT": "We have sent an email with a 6 digit code. \n Please now enter the code below:", "weHaveSentYouACodeTo": "We have sent a code to", "weSendtACodeTo": "We sent a code to", "weHaveSuccessfullyConnectedRentancyToYourChosenDocusign": "We have successfully connected LoftyWorks to your chosen DocuSign account. <br /> Our automated processes are already busy in the background getting everything you need.", "weRecommendTheseAreSetToOffTurnOnIfYouWantToReceive": "We recommend these are set to Off. Turn On if you want to receive email alerts where property or contract data is being modified by yourself or other users.", "weWereUnableToSendTheVerificationCodePleaseTryAgain": "We were unable to send the verification code. Please try again.", "website": "Website", "weightIbs": "Weight (ibs)", "welcomeToLoftyWorks": "Welcome to LoftyWorks!", "whatIsAWorkspace": "What is a Workspace?", "whatIsTheProblem": "What's the problem", "whatIsThePropertyAddress": "What is the property address?", "whatIsTheTermLease": "What is the term of this lease?", "whatRentCharges": "What are your rent charges?", "whatsTheProblem": "What’s the problem?", "whatsYourDesiredMoveInDate": "What’s your desired move in date?", "whatsYourName": "What’s your name?", "whatsapp": "WhatsApp", "whenDoYouWantToStartChargingRent": "When do you want to start charging rent?", "whenReadyClickSendToDocusign": "When ready click Send to Docusign.", "whenShouldChargeLateFee": "When should we charge this late fee?", "whereIsIt": "Where is it?", "whichKindOfPropertyDoYouManage": "Which kind of property do you manage?", "whichPropertyIsThisLeaseFor": "Which property is this lease for?", "who": "Who", "whoAreYouMovingWith": "Who are you moving in with", "whoIsTheTenant": "Who is the tenant?", "widowed": "Widowed", "willHoldingSecurityDepositLease": "Will you be holding a security deposit on this lease?", "work": "Work", "workOrders": "Workorders", "workOrder1": "Work Order", "workOrder": "+ Work Order", "workspace": "Workspace", "workspaceEmail": "Workspace Email", "workspaceJournal": "Workspace Journal", "workspaceName": "Workspace Name", "workspaceId": "Workspace ID", "workspacePhone": "Workspace Phone", "workspaceType": "Workspace Type", "workspaceWebsite": "Workspace Website", "workspaceWithSuchNameAlreadyExists": "Workspace with such name already exists.", "writeAComment": "Write a comment...", "writeMessage": "Write message...", "xero": "Xero", "xeroConnectionFailed": "Xero connection failed", "xeroIsConnected": "Xero is connected", "xlsGeneratedSuccessfully": "XLS generated and sent to your email successfully", "year": "Year", "yearly": "Yearly", "years": "Years", "yes": "Yes", "yesDeleteIt": "Yes, Delete It", "youAreGoingRemoveAllActivityAndRecordsRelatedToThisCo": "You are going remove all activity and records related to this contract.", "youCanAdjustTheFeePerPropertyButThisSettingWillBeUse": "You can adjust the fee per property but this setting will be used when you create a new contract.", "youCanAdvertiseYourProperties": "You can advertise your properties with our listing partners. Please turn on the switch for the partners you would like LoftyWorks to sync your listings with below:", "youCanSendTheLink": "You can send the link below or send a reminder to the applicant to fill it out.", "youCanUseThisEmailAddressDirectlyToAutomaticallyBringE": "You can use this email address directly to automatically bring emails into LoftyWorks, however, we recommend setting up email forwarding to this address from a known address, such as &quot;<EMAIL>&quot;. If you&apos;re not familiar with setting up forwarding rules, please consult your email provider for instructions, or email us as &quot;<EMAIL>&quot; and we can help point you in the right direction.", "youCannotInviteAUserAsNewPleaseSelectOneOfTheUserTy": "You cannot invite a user as NEW. Please select one of the user types from the dropdown menu in their contact information", "youCannotSendYourselfInvites": "You cannot send yourself invites", "youCantDeleteRegisteredUser": "You can't delete registered user", "youDontHaveAccessToThisModulePleaseContactYourWorkspac": "You don’t have access to this module. Please contact your workspace admin.", "youHaveAPendingInvitationButHaveNotYetCreatedYourAcco": "You have a pending invitation, but have not yet created your account. Please", "youWillBeConnectedWithPropertiesShortly": "You will be connected with properties shortly", "youWillSeeUpdatesToYourPropertyTasksAndConversationsHe": "You will see updates to your property, tasks and conversations here", "yourCurrentWhatsappNumber": "Your current WhatsApp number", "yourFirstAndLastName": "Your First & Last Name", "yourRequestIsCreated": "Your request is created successfully", "zipCode": "Zip Code", "zipcode": "Zip code", "yesRemove": "Yes, remove", "they": "They", "you": "You", "subscriptions": "Subscriptions", "updateCardInfo": "Update Card Info", "cancelMembership": "Cancel Membership", "extChargeDate": "Next Charge Date", "estimatedPaymentAmount": "Estimated Payment Amount", "history": "History", "basedOnNumberOfProperties": "This is based on the number of Properties in your system. We charge {{charge}} per Property in the system.", "expiryDate": "Expiry Date", "expiryDates": "Expiry Dates", "cardInformation": "Card Information", "cardNumber": "Card Number", "cvv": "CVV", "nameOnCard": "Name on Card", "cardNumberInvalid": "Card Number is Invalid", "cvvInvalid": "CVV is Invalid", "expiryDateInvalid": "Expiry Date is Invalid", "getStartedToday": "Get Started Today!", "weHelpAgentsAndLandlords": "We help agents and landlords with client accounting, rent collection and property management. Expert, professional and cost effective.", "newWorkspaceName": "New Workspace Name", "invitePeople": "Invite People", "inviteUpToFourPeople": "Invite up to four people", "startYourFreeTrial": "Start Your 14 Day Free Trial", "perPropertyPerMonthMinimum": "Per Property Per Month (Minimum {{ minimumAmount }} per month)", "previous": "Previous", "pleaseEnterYourDetails": "Please enter your details", "completeStartNow": "Complete & Start Now", "cancelYourMembership": "Are you sure you want to cancel your membership", "cardDetailsAddedSuccessfully": "Card Details Added Successfully", "cardDetailsUpdatedSuccessfully": "Card Details Updated Successfully", "welcomeBack": "Welcome Back", "dontHaveAnAccount": "Don’t have an account?", "signUpNow": "Sign Up Now", "yourCardRequires3DS2Verification": "Your card requires 3DS2 Verification", "pleaseSelectClickHereBelowToCompleteVerification": "Please select “Click Here” below to complete verification", "pleaseAddCardDetailsBelowPaymentWillBe": "Please add card details below. Payment will be taken after the first 14 days. Cancel at any time. Minimum charge is $30.00 per month ($25.00+VAT). ", "cardVerficiationFailed": "Card Verficiation failed. Please try again", "cardVerficiationSuccess": "Card Verficiation Success", "inOrderToBeChargedYourMonthlySubscription": "In order to be charged your monthly subscription, please complete 3DS2 verification on your Credit Card. ", "theTimeYouSetHasExpired": "The time you set has expired", "invalidTime": "Invalid time", "typeSearch": "Type a command or search", "viewMore": "View More", "viewLess": "View Less", "noResultsFoundFor": "No results found for", "problemReport": "Problem Report", "allSearchResult": "All search results", "seeAll": "See all", "loftyAPIProvidesEasySecureConnection": "LoftyWorks API provides easy and secure connection to your data. Use this API to synchronize Property, Tenancies and Contacts with 3rd party systems and applications.", "clickGenerateCreateKeyApi": "Click Generate to create a key for the api. Click Revoke to remove the key and a current API connection.", "youHaveNDaysLeftFreeTrial": "You have {{days}} days left of your free trial.", "unknown": "Unknown", "invalid": "Invalid", "landline": "Landline", "mobile": "Mobile", "confirmAndPay": "Confirm & Pay", "region": "Region", "locality": "Locality", "socialSecurityNumber": "Social Security Number", "employmentStatus": "Employment Status", "viewDetails": "View Details", "updateCreditCard": "Update Credit Card", "rechargeRenter": "Recharge to Renter", "rechargeAmount": "Recharge Amount", "costOfService": "Cost Of Service", "cardEndingIn": "Card Ending In", "connectSuccessful": "Connection Successful", "haveReadAndAgreeAgency": "I have read and agree to the {{agency}}", "retry": "Retry", "halfYearly": "Half Yearly", "custom": "Custom", "guarantorInfo": "Guarantor Info", "documentFile": "Document File", "draft": "Draft", "completed": "Completed", "retired": "Retired", "vouch": "Vouch", "day": "Day", "action": "Action", "return": "Return", "example": "Example", "stripe": "Stripe", "inviteYourTeam": "Invite your team", "workspaceDetail": "Workspace Detail", "studio": "Studio", "documentDates": "Document Dates", "logOut": "Log Out", "helpCenter": "Help Center", "switchWorkspace": "Switch Workspace", "uploadPhoto": "Upload Photo", "changePassword": "Change Password", "currentPassword": "Current Password", "confirmPassword": "Confirm Password", "confirmNewPassword": "Confirm New Password", "profileSuccessfullyUpdated": "Profile Successfully Updated", "passwordSuccessfullyChanged": "Password Successfully Changed", "enterYourCurrentPassword": "Enter your current password", "passwordMustContainLetter": "Password must contain at least one letter", "passwordMustContainNumber": "Password must contain at least one number", "passwordMustContainSpecialCharacter": "Password must contain at least one special character", "passwordShouldBeOfMinimumLength": "Password should be of minimum 8 characters length", "newPasswordMustBeDifferent": "New password must be different from current password", "newPasswordIsRequired": "New password is required", "passwordsDoNotMatch": "Passwords do not match", "confirmPasswordIsRequired": "Confirm password is required", "biAnnually": "Bi Annually", "invalidNumber": "Invalid Number", "thisQuestionIsUnanswered": "This question is unanswered.", "somethingWentWrongUploadingYourPicturePleaseTryAgainLater": "Something went wrong uploading your picture. Please try again later.", "DATE_FORMAT": "MM/DD/YYYY", "DATE_FORMAT_LONG": "MMM DD YYYY", "submissionFailed": "Submission Failed", "yourReferenceIsUnderReview": "Your reference is under review, the result will return once it’s complete.", "pleaseTryAgainShortly": "Please try again shortly", "returnBtn": "Return", "returnToEditing": "Return to editing", "weProvidePropertyManagersWhatNeededToScale": "Simple to use property management software which helps you scale and excel.", "onboardingCommunicationDescriptionText": "One easy inbox for all your communications. Hold emails, and conversations all in one place.", "marketyourProperties": "Market your Properties", "onboardingPropertiesDescriptionText": "Utilize our integrations with Zillow Connect to post your vacant properties, and create online links via LoftyWorks for your potential applicants that you can share on your Social Media channels to boost your leads.", "onboardingAccountingDescriptionText": "Utilize our in-house Accounting Ledger and market leading integrations with Plaid, Checkbook.io and National Processing to streamline your Rental Collection and Supplier paying processes", "taskManagement": "Task Management", "onboardingTaskDescriptionText": "Our project style boards help you stay on top. Use our integrated checklists to automate your tasks", "salesCRM": "Sales CRM", "onboardingCRMDescriptionText": "Delight your customers with our Sales CRM. Schedulers and automated marketing make tenant acquisition a breeze.", "logoTooltip1": "Upload a logo for your workspace. This will be used in reports such as Property Statements.", "logoTooltip2": "The logo is also displayed in the top left corner of this application.", "startNow": "Start Now!", "parentPropertiesImport": "Parent Properties", "needHelpSettingYourAccountUpForSuccess": "Need help setting your account up for success? Access our Getting Started guide and reach out to our Customer Success team at", "accessOurGettingStartedGuide": "Access our Getting Started Guide", "viewGuide": "View Guide", "image": "Image", "video": "Video", "emoji": "<PERSON><PERSON><PERSON>", "downloadAuditHistory": "Download Audit History", "incomeInsightsReport": "Income Insights Report", "incomeInsightsText": "Income Insights can help you determine when to request proof of income. Income Insights cannot be used to deny an applicant.", "selectAnOption": "Select an Option", "relevantData": "Relevant Data", "backToList": "Back To List", "noSubject": "No Subject", "issueDetails": "Issue Details", "apply": "Apply", "lofty": "Lofty", "landlordsToPay": "Landlords To Pay", "amountOwed": "Amount Owed", "sharedInbox": "Shared Inbox", "personalInbox": "Personal Inbox", "processing": "Processing", "added": "Added", "asTheirPropertyManagerYouWillBeResponsible": "As their Property Manager, you will be responsible for keeping their account up-to-date", "invitationDescription": "By accepting this invitation, the {{type}} will gain access to log into the {{type}} portal. As their property manager, you will be responsible for managing their business.", "invitationSuccessMessage": "Your invitation has successfully sent to your contact by email.", "noNotesFoundWriteAFirstNote": "No notes found, write a first note", "template": "Template", "bankInfo": "Bank Info", "basicInfo": "Basic Info", "postal": "Postal", "addBasicInfo": "Add Basic Info", "addAddressInfo": "Add Address", "addBankInfo": "Add Bank Info", "deleteTagWarning": "After deleting a tag, the conversations within the tag are not deleted.", "editAddress": "Edit Address", "sameHomeAddress": "Same as home address", "townCity": "Town/City", "enterAdress": "Enter address", "enterTownCity": "Enter Town/City", "enterZip": "<PERSON><PERSON>", "enterPostCode": "Enter Post Code", "tags": "Tags", "enterIban": "Enter IBAN", "enterSwift": "Enter SWIFT", "communications": "Communications", "notStarted": "Not Started", "vacancyDate": "Vacancy Date", "financial": "Financial", "addressLine1": "Address Line 1", "addressLine2": "Address Line 2", "confirmDeleteInternalNotes": "Are you sure you want to delete this Internal Notes?", "editInternalNotes": "Edit Internal Notes", "pleaseCompleteTheMissingInformationAndTryAgain": "Please complete the missing information and try again.", "newContact": "New Contact", "additionalInfo": "Additional Info", "addAdditionalInfo": "Add Additional Info", "exemptionCertificate": "Exemption Certificate", "createdtime": "Created time", "taskConfiguration": "Task Configuration", "checklists": "Checklists", "items": "Items", "createNewCheckList": "Create New Checklist", "taskTypes": "Task Types", "scheduledTask": "Scheduled Task", "automatedTask": "Automated Task", "noResultChecklists1": "Managing tasks can be a cumbersome process, requiring many steps. Our goal here is to never skip any steps or miss deadlines. ", "noResultChecklists2": " to create your first checklist template.", "addChecklistItem": "+ Add Checklist Item", "editCheckList": "Edit Checklist", "checkListRequired": "Checklist is required", "taskType": "Task Type", "myTasks": "My Tasks", "close": "Close", "addStatus": "Add Status", "taskStatusDescription": "This is the initial status when the task is created", "addWorkOrder": "Add Work Order", "time": "Time", "taskEditCloseMessage": "If you leave this page, we won't be able to save your data.", "deleteWorkOrderMessage": "Are you sure you want to delete this Work Order? After deletion, your data will not be recoverable.", "createScheduledTask": "Create Scheduled Task", "editScheduledTask": "Edit Scheduled Task", "taskSuccessfullyUpdated": "Task successfully updated", "toCreateNewScheduledTask": "to create new scheduled task", "propertyStatusChangedTo": "Property Status Changed To", "propertyAdded": "Property Added", "contractStartsIn": "Contract Starts in", "contractStartsToday": "Contract Starts Today", "contractEndsToday": "Contract Ends Today", "contractEndsIn": "Contract Ends in", "contractCreated": "Contract Created", "documentExpiryDateIsToday": "Document expiry date is today", "documentExpiryDateIsIn": "Document expiry date is in", "editAutomatedTask": "Edit Automated Task", "event": "Event", "workOrderUpdated": "Work Order Successfully Updated", "comments": "Comments", "fax": "Fax", "officePhone": "Office Phone", "officeExtension": "Office Extension", "mobilePhone": "Mobile Phone", "userName": "User Name", "company": "Company", "invalidCredentials": "Invalid Credentials", "personalInfo": "Personal Info", "customerId": "Customer ID", "zip": "Zip", "pleaseSelectEmailSenderFirst": "Please select email sender first", "emailContent": "Email Content", "totalAmount": "Total Amount", "checking": "Checking", "savings": "Savings", "selectSomething": "Select something...", "scheduledTasks": "Scheduled Tasks", "automatedTasks": "Automated Tasks", "statusAlreadyExists": "Status already exists.", "notActive": "Not Active", "allProperties": "All Properties", "searchByAddCityZipNeibour": "Search by Address, City, Zipcode, Neibourhood…", "pleaseEnterPhoneNumber": "Please enter phone number", "dndOr": "Drag and drop file or", "browse": "Browse", "enterTask": "Enter Task", "taskSuccessfullyUnarchived": "Task Successfully Unarchived", "expiring": "Expiring", "valid": "<PERSON><PERSON>", "requestFailed": "Request Failed", "unfortunatelyIfYouDoNotAgreeToLoftyWorksTermsOfUse": "Unfortunately, if you do not agree to LoftyWorks Terms of Use you will be unable to use the platform. Please contact the Support (<EMAIL>) with any questions or concerns.", "termsOfUse": "Terms of use", "hasUpdateIts": "has updated its", "open": "Open", "scheduled": "Scheduled", "taskTitleHeader": "Task Title", "deleteTaskType": "Delete Task Type", "areYouSureYouWantToDeleteThisTaskType": "Are you sure, you want to delete this Task Type?", "taskTypeSuccessfullyDeleted": "Task Type Successfully Deleted", "failed": "Failed", "uncertain": "Uncertain", "orderNumber": "Order Number", "priority": "Priority", "contactForAccess": "Contact for Access", "generalLabel": "General", "worksManager": "Works Manager", "dateReported": "Date Reported", "problemReported": "Problem Reported", "workOrderPdfFooter": "This document gives permission to the stated above “Contractor” to carry out only the outlined above job. Any additional request from any other party apart from “Main Contact” are to be declined and placed forward for the approval by “Main Contact”.", "createdBy": "Created By", "chargeTo": "Charge to", "accessDetails": "Access details", "quote": "Quote", "maximumCost": "Maximum cost", "actualCost": "Actual cost", "low": "Low", "high": "High", "medium": "Medium", "urgent": "<PERSON><PERSON>", "cancelled": "Cancelled", "activate": "Activate", "secondary": "Secondary", "welcomeToLandlordPortal": "Welcome to Landlord Portal", "workWithYourPropertyManager": "Work with your Property Manager to stay on top of your portfolio with the LoftyWorks Landlord Portal.", "tenantPortalTitle": "Welcome to Tenant Portal", "tenantPortalSubtitle": "Use your LoftyWorks Tenant Portal to communicate with your Property Manager, make rental payments, raise problem reports & view Documentation.", "reject": "Reject", "updateEmailPreferencesForEmail": "Update email preferences for {{email}}", "ifThisIsNotYourAddressPleaseIgnore": "If this is not your address, an email was most likely forwarded to you from someone else and you can safely ignore this message.", "skipAndSend": "Skip and Send", "yourEmailPreferencesUpdated": "Your email preferences were updated.", "reportAnIssue": "Report An Issue", "templateLetters": "Template Letters", "templateLetterName": "Template Letter Name", "searchBy": "Search by...", "searchByTemplateName": "Search by template name", "chooseTemplateLetter": "Choose template letter", "contactTypes": "Contact Types:", "editTemplateLetter": "Edit Template Letter", "addTemplateLetter": "Add Template Letter", "letterName": "Letter Name", "insertVariable": "Insert Variable", "templateLetter": "Template Letter", "composeNewLetter": "Compose New Letter", "portalMenu": "Portal", "notAllVariablesCouldBeAddedDueToMissingData": "Some variables could not be added due to missing data", "templateLettersNoEmail": "Contact does not have an email address stored. Please update their email address to send Template Letters", "templateLetterNotIntegrate": "Please connect your email account with LoftyWorks via the Integrations menu in Settings", "templateLetterSuccessfullyDeleted": "Template Letter Successfully Deleted", "deleteTemplateLetter": "Delete Template Letter", "areYouSureYouWantToDeleteThisTemplateLetter": "Are you sure, you want to delete this Template Letter?", "addCategory": "Add Category", "categoryExists": "Category Exists", "contactManager": "Contact Manager", "reportIssue": "Report Issue", "keyRevokedSuccessfully": "Key revoked successfully!", "skipAndProceed": "Skip and Proceed", "sharePDF": "Share PDF", "shareTo": "Share To", "preview": "Preview", "general1": "General", "newWorkOrder": "New WorkOrder", "billingName": "Billing Name", "addNewSupplier": "Add New Supplier", "portfolio": "Portfolio", "propertyStatus": "Property Status", "invalidValue": "Invalid Value", "workspacePhoneOrEmailIsMissing": "Workspace phone or email is missing", "addNow": "Add Now", "primaryTenantCantBeRemoved": "Primary Tenant Can't Be Unassigned from Contract", "inventoryDates": "Inventory Dates", "inventoryWarrantyNotificationDays": "Warranty Expiry Days", "templateLetterCreatedSuccessfully": "Template letter created successfully", "templateLetterUpdatedSuccessfully": "Template letter updated successfully", "documentUploadedSuccessfully": "Document uploaded successfully", "allDocuments": "All Documents", "general": {"moduleTitles": {"dashboard": "Dashboard", "communication": "Communication", "communications": "Communications", "inbox": "Inbox", "property": "Property", "contracts": "Contracts", "calendar": "Calendar", "tasks": "Tasks", "contacts": "Contacts", "finance": "Finance", "accounting": "Accounting", "documents": "Documents", "reports": "Reports", "applications": "Applications", "settings": "Settings", "reportIssue": "Report Issue", "applicationsSubMenu": {"prospect": "Prospect", "rental": "Rental Applications"}, "parentProperty": "Parent Property", "workorders": "Workorders"}, "topBar": {"search": "Search"}, "mainMenu": {"switchToDesktop": "Use desktop version to manage your workspace"}, "sorters": {"address": "Address A-Z", "lastUpdate": "Last Update", "status": "Status"}, "actions": {"cancel": "Cancel", "create": "Create", "save": "Save"}, "address": {"address": "Address", "addressLine1": "Address 1", "addressLine2": "Address 2", "addressLine3": "Address 3", "city": "City/Town", "country": "Country", "postCode": "Post Code", "street1": "Street 1", "street2": "Street 2", "state": "State"}, "pagination": {"country": "Country", "goTo": "Go to", "postCode": "Post Code", "show": "Show"}, "property": {"client": "Client", "landlord": "Landlord", "manager": "Primary Manager", "ref": "Ref", "reference": "Reference", "service": "Service", "status": "Status", "type": "Property Type"}, "validation": {"valueMustBeNumber": "Value must be number"}}, "modules": {"properties": {"tabs": {"all": "All", "occupied": "Occupied", "underOffer": "Under Offer", "vacant": "Vacant"}, "columns": {"refOrAddress": "Ref or Address", "type": "Type"}, "addProperty": {"addProperty": "Add Property", "editProperty": "Edit Property", "successMessage": "Property successfully added, click to see the property", "seeProperty": "See property", "refError": "Reference already exists. Please use another", "addressError": "Address already exists. Please use another", "fields": {"addImage": "Upload image of your property", "uploadImages": "upload images"}}}, "parentProperties": {"addParentProperty": {"addParentProperty": "Add Parent Property"}, "name": "Name", "properties": "Properties", "search": "Search", "deleteProperty": "Are you sure you want to delete this Parent Property?", "deleteYes": "Yes, Delete It"}, "prospectFiles": {"dragFilesOrClickHereToUpload": "Drag files or click here to upload", "pdfDocumentsSpreadsheetsOrGraphics": ".PDF, Document (.DOC, .DOCX, .TXT, .ODT), Spreadsheet (.CSV, .XLS, .XLSX, .ODS), or Graphic (.JPG, .PNG, .GIF).", "onlyMaxFileSizeText": "Max file size"}, "workorders": {"tabs": {"active": "Active", "history": "History"}}}, "components": {"PostCode": {"searchByPostCode": "Search by Zip Code"}}, "requestKeysFromNationalProcessing": "Request Keys from National Processing", "steamlineRentalCollectionWithNationalProcessingIntegration": "Streamline Rental Collection with National Processing Integration.", "retrieveAndInsertYourKeys": "Retrieve and Insert Your Keys from National Processing", "copyAndPasteKeys": "<PERSON><PERSON> and <PERSON><PERSON> Keys from National Processing into Integration Settings.", "wevePartneredWithANationalProcessing": "We've partnered with a market leader, National Processing, to streamline your Rental Collection process with tenants.", "enterAPIIntegrationKey": "Enter API Integration Key", "nationalProcessingSetUp": "National Processing Set-Up", "iAgreeToTheLoftyWorks": "I agree to the LoftyWorks", "iAgreeToTheNP": "I agree to the National Processing", "connectRequestSuccessfullySubmitted": "Connection Request Successfully Submitted", "nationalProcessingWillBeInContact": "National Processing will be in contact shortly to configure your account", "enterMerchantId": "Enter Merchant ID", "enterSiteId": "Enter Site ID", "nationalProcessingIntegratedSuccessfully": "National Processing Integrated Successfully", "forEachIncomingRentalPaymentNationalProcessingWillCharge": "For each incoming rental payment, National Processing will charge $0.50 per ACH transaction, and charge up to 2-2.8% for Credit Card Transactions. LoftyWorks do not profit from this, and this fee solely goes to National Processing.", "weUnderstandThatYouMightNotWantToBearThisCost": "We understand that you might not want to bear this cost, so here you can configure it as follows:", "rechargeForACH": "Recharge for ACH", "rechargeForCreditCard": "Recharge for Credit Card", "areYouSureYouWantTodisconnectNationalProcessing": "Are you sure you want to disconnect National Processing?", "nationalProcessingDisconnectedSuccessfully": "National Processing Disconnected Successfully", "bankAccountInfo": "Bank Account Info", "deductionDetails": "Deduction Details", "invoiceDue": "Invoice Due", "ach": "ACH", "creditCard": "Credit Card", "passwordMustContainUppercaseLetter": "Password must contain an uppercase letter", "passwordMustContainLowercaseLetter": "Password must contain a lowercase letter", "totalAmountForACH": "Total Amount for ACH", "totalAmountforCreditCard": "Total Amount for Credit Card", "tipsOfUnpaidableNP": "The Workspace you've been invited to hasn't connected with National Processing. Please reach out to your property manager and connect first.", "accountType": "Account Type", "feesHaveBeenConfiguredSuccessfully": "Fees have been configured successfully!", "paymentRequest": "Payment Request", "submittedSuccessfully": "Submitted Successfully", "howWouldYouLikeToPay": "How would you like to pay?", "creditCardNumber": "Credit Card Number", "expirationDate": "Expiration Date", "creditCardInfo": "Credit Card Info", "ACHKeys": "ACH Keys", "creditCardKeys": "Credit Card Keys", "APILoginID": "API Login ID", "securityKey": "Security Key", "merchantID": "Merchant ID", "siteID": "Site ID", "apiIntegrationKey": "API Integration Key", "retrieveAndInsertYourAPIKeyForACH": "Retrieve and Insert your API key from National Processing for ACH. Copy and Paste the API Key from National Processing into the ACH Integration Settings.", "retrieveAndInsertYourAPIKeyForCC": "Retrieve and Insert your API key from National Processing for Credit Card. Copy and Paste the API Key from National Processing into the Credit Card Integration Settings.", "paymentMethodKeyInfo": "Payment Method Key Info", "ACHhasBeenConfiguredSuccessfully": "ACH has been configured successfully!", "creditCardHasBeenConfiguredSuccessfully": "Credit Card has been configured successfully!", "achPaymentResult": "ACH Payment Result", "creditCardPaymentResult": "Credit Card Payment Result", "processingInitiated": "Processing Initiated", "achPaymentsTypicallyClearInThreeToFiveBusinessDays": "ACH Payments typically clear in 3-5 business days", "creditCardTransactionsMayTakeUpToThirtyMinutes": "Credit Card Transactions may take up to 30 minutes.", "wellNotifyYouOnceTheTransactionIsComplete": "We'll notify you once the transaction is completed.", "theWorkspaceYouveBeenInvitedToDoesntSupportCredcitCardPayment": "The Workspace you've been invited to doesn't support Credit Card payment.", "theWorkspaceYouveBeenInvitedToDoesntSupportACHPayment": "The Workspace you've been invited to doesn't support ACH payment.", "yourPropertyManagerHasNotYetConfiguredLoftyWorks": "Your Property Manager has not yet configured LoftyWorks to enable Rent Collection.", "rentPaymentSetup": "Rent Payment Set Up", "parentProperty": "Parent Property", "addParentProperty": "Add Parent Property", "editParentProperty": "Edit Parent Property", "createParentProperty": "Create Parent Property", "addAssociatedProperties": "Add Associated Properties", "basic": "Basic", "primary": "Primary", "country": "Country", "otherLandlord": "Other Landlord", "percentageSplit": "Percentage Split", "splitPayOutsToLandlords": "Split Pay-Outs to Landlords", "addAssociatedPropertiesWarning": "Once you link a Property to a Parent Property, the link is permanent and cannot be undone. If you are ready to proceed, click 'Submit'.", "createParentpropertySuccessfully": "Create parent property successfully", "shareDocumentSuccess": "Shared document with properties successfully", "modify": "Modify", "dataNotChange": "Data not changed", "successful": "Successful", "unsuccessful": "Unsuccessful", "addParentPropertySuccessfully": "Your Parent Property was successfully added.", "addParentPropertyUnsuccessfully": "You can only link Properties with the same Landlord. Please review the list and assign the correct properties.", "returnToMenu": "Return to Menu", "returnToEdit": "Return to Edit", "associatedProperties": "Associated Properties", "apartmentBlock": "Apartment Block", "businessPark": "Business Park", "block": "Block", "office": "Office", "sumEqual100": "Ensure each Landlord has a percentage split that adds up to 100", "archiveDocument": "Archive Document", "sharedDocumentDeleteWarning": "Deleting this will delete the document from all linked properties.", "areYouSureToDeleteIt": "Are you sure you want to delete it?", "sharedDocumentArchiveWarning": "Archiving this will move the Document to the archived section and remove it from the linked Properties.", "areYouSureToArchiveIt": "Are you sure you want to archive it?", "sharedDocumentRenameWarning": "Renaming this Document will update its name across all linked Properties.", "areYouSureToRenameIt": "Are you sure you want to rename it?", "sharedDocumentUnarchiveWarning": "Unarchiving this will move the Document to the active section and remove it from the linked Properties.", "areYouSureToUnarchiveIt": "Are you sure you want to unarchive it?", "parentPropertyDeletedSuccessfully": "Parent Property deleted successfully", "publishToZillow": "Publish To Zillow", "zillowConnectedSuccessfully": "Zillow Connected Successfully", "zillowUpdatedSuccessfully": "Zillow Updated Successfully", "viewTheListingsOnZillow": "View the listings on Zillow", "selectChildPropertiesAndConnectToZillow": "Select the child Property under this Parent Property that you want to publish to Zillow.", "selectChildPropertiesAndPublishToZillow": "You can view the Properties published to Zillow under this Parent Property and choose to turn them on or off from Zillow.", "linkProperties": "Link Properties", "previewSave": "Preview & Save", "propertyKeyDetail": "Property Key Detail", "yearBuilt": "Year Built", "lotSizeSqft": "Lot Size (Sqft)", "descriptionOptional": "Description (Optional)", "EnterYourContactDetailsWithNextSteps": "Enter your contact details, and we'll let the rental manager contact you with next steps.", "arrears": "Arrears", "outstanding": "Outstanding", "areYouSureToRemoveThisUnit": "Are you sure to remove this unit?", "searchByAddressCityZipcode": "Search by Address, City, Zipcode", "underContracts": "Under Contracts", "unavailable": "Unavailable", "propertyTourRequestCreatedSuccessfully": "Property Tour Request created successfully", "allContracts": "All Contracts", "publish": "Publish", "congratulationsPostToRightSuccessfully": "Congratulations! Post to right successfully!", "propertyUpdatedSuccessfully": "Property Updated Successfully", "squareFt": "Square Feet", "squareMtrs": "Square Meters", "fullBathRooms": "Full Bathrooms", "halfBathRooms": "Half Bathrooms", "availableNow": "Available Now", "rentFrequency": "Rent Frequency", "furnished": "Furnished", "newVersion": "New Version", "oldVersion": "Old Version", "propertyInformation": "Property Information", "supplementary": "Supplementary", "addressLine": "Address Line {{ number }}", "furnishedStatus": "Furnished Status", "addAmenities": "Add Amenities", "addIncludes": "Add Includes", "conversionSqftToSqm": "Conversion: 1 sqft = 0.092903 sqm", "conversionSqmToSqft": "Conversion: 1 sqm = 10.7639 sqft", "FULLY_FURNISHED": "Furnished", "NOT_FURNISHED": "Unfurnished", "PART_FURNISHED": "Part Furnished", "FURNISHED_OPTIONAL": "Furnished Optional", "aggregatedInformation": "Aggregated Information", "bedrooms": "Bedrooms", "areYouSureYouWantToUpdate": "Are you sure you want to update?", "multiFamilyHomes": "Multi-Family Homes", "duplex": "Duplex", "apartmentCommunity": "Apartment Community", "apartmentBuilding": "Apartment Building", "condos": "Condos", "theChildPropertyHasAlreadyBeenPostedToZillow": "The property has already been posted to Zillow as a Unit within an Apartment before. You cannot repost it.", "weAreUnableToSyncTheFollowingPropertiesWithLoftyDueToIncompleteInformation": "We’re unable to sync the following properties with Lofty due to incomplete information:", "partialSuccessSomePropertiesSentOthersIncompleteAndUnsyncedWithLofty": "Partial success: Some properties sent, others incomplete and unsynced with Lofty.", "percentageSplitError": "Ensure each Landlord has a percentage split that adds up to 100", "currentTenants": "Current Tenants", "arrearsBalance": "Arrears Balance", "nextRentDate": "Next Rent Date", "pendingApproval": "Pending Approval", "propertyBalance": "Property Balance", "totalBalance": "Total Balance", "marketing": "Marketing", "thePropertiesYouHaveSelectedIncludeOnesNotAssignedToYou": "The properties you have selected include ones not assigned to you. You can choose to skip these and proceed with the sync, or cancel the operation altogether.", "allTheSelectedPropertiesAreAssignedToAnotherAgentInLofty": "All the selected properties are assigned to another agent in Lofty.", "thePropertiesBelowAreAssignedToAnotherAgentInLofty": "The properties below are assigned to another agent in Lofty.", "thisPropertyIsNotAssignedToYou": "This property is not assigned to you.", "youCannotSelectThisPropertyBecauseItsTypeChanged": "You cannot select this property because its type changed from 'For Rent' to 'For Sale' in Lofty.", "inventory": "Inventory", "purchaseDate": "Purchase Date", "warrantyExpiryDate": "Warranty Expiry Date", "purchase": "Purchase", "warranty": "Warranty", "generateInventoryReport": "Generate Inventory Report", "inventoryReport": "Inventory Report", "noAssetsToGenerateReport": "No assets to generate report", "tooManyFiles": "Too many files", "invalidFileType": "Invalid file type", "fileTooLarge": "File is too large", "fileTooSmall": "File is too small", "propertyNotPublished": "Property Not Published", "yourRentalApplicationHasBeenSentSuccessfully": "Your rental application has been sent successfully!", "confirmationNumber": "Confirmation number", "youCanLogIntoYourPortalToCheckYourApplicationReview": "You can log into your portal to check your application review status", "submitApplicationInformation": "Submit Application Information", "appliactionContact": "Contact", "appliactionMoveIn": "Expected Move-in", "appliactionSubmittedAt": "Submitted At", "applicationStatus": "Status", "applicationProperty": "Property", "reg": "Reg", "contactInfo": "Contact Info", "prospects": "Prospects", "addProspectTitle": "Add Prospect", "editProspectTitle": "Edit Prospect", "typePlaceholder": "Type something…", "propertyPlaceholder": "Select Parent Property / Property", "taskChecklistPlaceholder": "Select Task Checklists", "City": "City", "labelMaxRent": "<PERSON>", "labelPropertyType": "Property Type", "labelMinBedrooms": "Min Bedrooms", "labelBathrooms": "Min Bathrooms", "detailInfo": "Detail Info", "areYouSureToChangeTheProspectStatus": "Are you sure to change the prospect status?", "yesChangeIt": "Yes，Change it", "regDate": "Reg Date", "expectedMoveInDate": "Expected Move-in Date", "addInterestedPropertyWhenBlank": "Add details of your property to start marketing your property on top listing site", "addInterestedPropertyWithPlus": "+ Add Interested Property", "confirmDeleteInterestProperty": "Are you sure to delete this Interest Property?", "confirmDeleteProspect": "Are you sure to remove the prospect?", "yesDelete": "Yes, Delete It", "interestProperty": "Interest Property", "keyDetails": "Key details", "maxPropertyTypesExceed": "Max count of property types: {{count}}", "creditScore": "Credit Score", "prospectCallButton": "Call", "filesTab": {"dragFilesOrClickHereToUpload": "Drag files or click here to upload", "pdfDocumentsSpreadsheetsOrGraphics": ".PDF, Document (.DOC, .DOCX, .TXT, .ODT), Spreadsheet (.CSV, .XLS, .XLSX, .ODS), or Graphic (.JPG, .PNG, .GIF).", "pdfDocuments": ".PDF, Document (.DOC, .DOCX, .TXT, .ODT)", "onlyMaxFileSizeText": "Max file size", "fileDeletedSuccessfully": "File deleted successfully.", "errorWhenDeletingFile": "An error was encountered while deleting the file. Please try again.", "downloadAll": "Download All", "addDescription": "Add Description", "enterDescription": "Enter description...", "areYouSureToDeleteTheFile": "Are you sure to delete the file?", "yesDeleteIt": "Yes, Delete it", "pleaseWaitAMoment": "Please wait a moment...", "fileExceededMaxSize": "{{fileName}} exceeded file max size"}, "interestPropertyRequired": "Interest property is required, please add at least one property", "invalidZipCode": "Invalid zip code format", "prospectEmailExists": "Email already exists. Please enter another.", "prospectCannotBeDeleted": "This prospect is associated with a rental application. Please delete the rental application first and try again.", "issue": "Issue", "selectPropertyToCreateReport": "Select property to create report", "problemPicture": "Problem Picture", "areYouSureYouWantToDeleteThisProblem": "Are you sure you want to delete this problem?", "problemResolvedSuccessfully": "Problem resolved successfully!", "areYouSureYouWantToCancelThisIssue": "Are you sure you want to cancel this issue?", "yesterday": "Yesterday", "thisWeek": "This Week", "lastWeek": "Last Week", "thisMonth": "This Month", "lastMonth": "Last Month", "thisQuarter": "This Quarter", "lastQuarter": "Last Quarter", "last6Months": "Last 6 Months", "last12Months": "Last 12 Months", "thisYear": "This Year", "week": "Week", "quarter": "Quarter", "groupBy": "Group By", "owner": "Owner", "reportBalanceSheet": "Report - Balance Sheet", "reportPL": "Report - P&L", "reportARSummary": "A/R Summary", "daysPerAgingPeriod": "Days per aging period", "numberOfPeriods": "Number of periods", "minDaysPastDue": "<PERSON>.Days Past Due", "apDetailTitle": "A/P Detail", "lease": "Lease", "openBalance": "Open Balance", "arDetailTitle": "A/R Detail", "reportAPSummary": "A/P Summary", "connectEmail": "Connect Email", "personalMessageType": "Personal (Messages are only visible to you)", "sharedMessageType": "Shared (Messages are visible to everyone)", "screenYourTenantsWithTransUnion": "Screen your Tenants with TransUnion", "toAutomaticallySynchroniseYourTenantReferencesBetweenTransUnion": "To automatically synchronize your Tenant References between TransUnion and LoftyWorks.", "weUtilizeTransUnionAsOurPartnerForTenantScreening": "We utilize TransUnion as our partner for Tenant Screening. By using our integration with TransUnion, you can effectively screen your Tenants.", "transUnionSetUp": "TransUnion Set-Up", "businessName": "Business Name", "businessAddress": "Business Address", "connectWithYourStripeAccount": "Connect with your <PERSON>e Account", "integrateWithStripeConnectToStreamlineYourRentalCollectionProcess": "Integrate with Stripe Connect to streamline your Rental Collection Process", "weHaveIntegratedWithAMarketLeaderInStripe": "We have integrated with a market leader in Stripe to allow you to streamline your Rental Collection process with your Tenants.", "configureFees": "Configure Fees", "forEachIncomingRentalPaymentStripeCharge": "For each incoming rental payment, Stripe charge 0.8% (up to $5 maximum) for each transaction. LoftyWorks do not profit from this, and this fee solely goes to Stripe for using their payment infrastructure.", "rehargeForACH": "Recharge for ACH", "rechargePercentage": "Recharge percentage", "maximumCharge": "Maximum Charge", "ifYourRentersMonthlyRentWas": "If your Renter's Monthly Rent was $1000, they would pay an additional $5 meaning that their total payment would be $1005.", "cannotBeZero": "Cannot be zero", "payYourLandlordsAndSuppliersWithCheckbook": "Pay your Landlords and Suppliers with Checkbook.io", "weWillUseCheckbookAsOurPartnerToFacilitateOutgoingPayments": "We utilize Checkbook as our partner to facilitate outgoing payments.", "configureProfile": "Configure Profile", "connectToABankAccount": "Connect to a bank account", "linkBankAccount": "Link Bank Account", "updateBankAccount": "Update Bank Account", "checkbook": "Checkbook.io", "business": "Business", "principleOwner": "Principle Owner", "officer": "Officer", "citizenShip": "Citizen Ship", "enterFirstName": "Enter First Name", "enterLastName": "Enter Last Name", "enterNumber": "Enter Number", "enterAddress": "Enter Address", "enterCity": "Enter City", "enterState": "Enter State", "enterZipCode": "Enter ZipCode", "successfullyLinked": "Successfully Linked", "taxID": "Tax ID", "enterTaxID": "Enter Tax ID", "enterBusinessName": "Enter Business Name", "occupation": "Occupation", "last4SSN": "Last 4 Digits of SSN", "birthday": "Birthday", "info": "Info", "returnToSettings": "Return to Settings", "linked": "Linked", "approvedConnectCheckbook": "Your company information has been approved by Checkbook, and the corresponding account has been generated in LoftyWorks. Please select which Banks you will be paying Suppliers from.", "connectWithPlaid": "Connect with Plaid", "pleaseSelectYourBank": "Please select the bank card you want to link with the Plaid account for LoftyWorks reconciliation. After connecting, your bank statements will be imported automatically. You have the option to disconnect this account from Plaid.", "rentDueAutomation": "Rent Due Automation", "lateRentAutomation": "Late Rent Automation", "triggerRentDueAutomation": "Trigger Rent Due Automation", "triggerLateRentAutomation": "Trigger Late Rent Automation", "editEmail": "<PERSON> Email", "triggerProcessOverdueAfter": "Trigger Process Overdue After", "automatedInvoice": "Automated Invoice", "editTemplate": "Edit Template", "emailSentFrom": "<PERSON><PERSON>", "infiniceptHeader": "Process your Inbound and Outbound Payments", "infiniceptSubHeader": "Collect rent and make payments all in one platform", "infiniceptConfigureFeesHint": "Configure fees for ACH and credit card transactions", "infiniceptWarningForEachPayment": "For each incoming rental payment, Infinicept will charge $0.50 per ACH transaction.  and charge 0.35% of transaction amount and $0.10 fixed for Credit Card Transaction. Loftyworks do not profit from this, and this fee solely goes to Infinicept.", "endOfTenancy": "End of Tenancy", "saveAndExit": "Save & Exit", "landlordReview": "Landlord Review", "tenantReview": "Tenant Review", "tenantReviewSuccessMessage": "The End of Tenancy process is now complete. If the tenancy is not renewing, remember to visit <0>link</0> to update the deposit status.", "emailSent": "<PERSON><PERSON>", "landlordReviewSuccessMessage": "You will receive an update shortly with next steps.", "inviteToClientPortal": "Invite to Client Portal", "past": "Past", "current": "Current", "future": "Future", "inviteToPortal": "Invite to portal", "invited": "Invited", "tenantPortal": "Tenant Portal", "emergencyContact": "Emergency Contact", "sentAt": "<PERSON><PERSON>", "loginEmail": "<PERSON><PERSON>", "weightLbs": "Weight(lbs)", "myPetIsAServiceAnimal": "My pet is a service animal", "editTenantTitle": "Edit Tenant", "addTenantTitle": "Add New Tenant", "addLeaseTitle": "Add <PERSON>se", "addTenantTips": "These details are required for rent reminders, tenant communications and portal access.", "confirmDeleteTenant": "Are you sure to delete this Tenant?", "tenantCannotBeDeleted": "This tenant is associated with a lease. Please delete the lease first and try again.", "invitationSentSuccessfully": "Invitation sent successfully", "invalidAge": "Invalid Age", "invalidYear": "Invalid Year", "invalidWeight": "Invalid Weight", "searchByName": "search by name", "tenantDeletedSuccessfully": "Tenant deleted successfully.", "todos": "To-dos", "submitted": "Submitted", "detailedInformation": "Detailed Information", "createReference": "Create Reference", "editReference": "Edit Reference", "paymentInformation": "Payment Information", "tenantScreening": "Tenant Screening", "inProgressFor": "In Progress For", "createdDate": "Created Date", "createReferenceDescription": "Conduct your referencing through LoftyWorks and take advantage of our workflows allowing you to share completed references with your Landlord, and create a Contract following a successful reference.", "recommendPlatform": "Recommend Platform", "transUnion": "TransUnion", "transUnionFeature1": "Use TransUnion with LoftyWorks to streamline your Tenant Screening process by eliminating double entry and saving you time whilst utilising a Market Leading Provider.", "transUnionFeature2": "Screening Renters via TransUnion costs $38 and includes Credit Report and Score, an Income Insight, Criminal Background Check and Eviction Related Proceeding Information.", "alternativeProvider": "Alternative Provider", "alternativeProviderFeature": "If you are already utilising another Screening Provider, you can still utilize LoftyWorks to upload your reference details, store any documents and share them with your Landlord", "viewReference": "View Reference", "shareDocument": "Share Document", "createContract": "Create Contract", "sent": "<PERSON><PERSON>", "tenantReferenceDeletedSuccessfully": "Tenant reference deleted successfully", "confirmDeleteTenantReference": "Are you sure to delete this Reference?", "tenantReferenceSharedSuccessfully": "Tenant reference shared with landlord successfully", "getStarted": "Get Started", "transUnionApplicationTitle": "Survey title", "transUnionApplicationDescription": "In order to provide you with better service, we hope you can take a few minutes to share your thoughts and suggestions with us. We value the opinions of each user and look forward to your participation! Let's get started now!", "incomeFrequency": "Income Frequency", "transUnionIsProcessingYourRequest": "TransUnion is processing your request...", "weWillLetYouKnowWhenYouCanProceedToTheNextStage": "We will let you know when you can proceed to the next stage.", "theSurveyConcludesHere": "The survey concludes here.", "thankYouForYourParticipation": "Thank you for your participation!", "yourAnswersCouldNotBeVerified": "Your answers could not be verified. Please try again", "weAreUnableToVerifyYourIdentityOnline": "We are unable to verify your identity online. Please call customer support at (************* for assistance with completing your screening over the phone.", "decline": "Decline", "denial": "Denial", "workorder": "Workorder", "reasonForRejection": "Reason for rejection", "pleseEnterTheReasonForRejection": "Please enter the reason for rejection", "rejectedSuccessfully": "Rejected successfully", "reportedBy": "Reported by", "supplierReported": "Supplier reported", "contactManger": "Contact manager"}