import { BillModal, InvoiceModal } from "loftypay";
import {
  Add as AddIcon,
  MailOutline as MailOutlineIcon,
  MoreHoriz as MoreHorizIcon,
  Visibility,
} from "@mui/icons-material";
import {
  Box,
  Card,
  IconButton,
  ListItemText,
  Menu,
  MenuItem,
  Stack,
  Typography,
} from "@mui/material";
import { Button } from "@rentancy/ui";
import {
  CreateInvoicePopup,
  SubscriptionType,
  Tenancy,
  hasSubscription,
  useApi,
  usePropertyDetails,
  useUser,
  useXeroConnection,
  useGlobal,
  getPropertyContracts,
} from "@rentancy/common";
import { EndOfTenancyStage } from "Containers/Contracts/EndOfTenancy/types";
import { FC, useContext, useEffect, useState, useCallback } from "react";
import { getEotDetailByTenancyId } from "Api/EndOfTenancy";
import { getApplicationDetail } from "Api/Application/index";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";

import { MoveOutContext } from "../hooks/MoveOutContext";
import { TenancyActionConfig, TenancyStatus, TenancyActionType } from "../consts";
import { StatusModal } from "./StatusModal";
import { getManageCardActionLabel } from "./utils";
import { TenancyInformation } from "./components/TenancyInformation";

type ManageCardProps = {
  tenancy: Tenancy;
  refetchTenancy?: () => void;
};

export const ManageCard: FC<ManageCardProps> = ({ tenancy, refetchTenancy }) => {
  const { t } = useTranslation();
  const params = useParams();
  const { user } = useUser();
  const { emitter } = useGlobal();
  const { id: propertyId } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { getInvoices } = usePropertyDetails();
  const { integrationStatus } = useXeroConnection();
  const [openCreateInvoice, setOpenCreateInvoice] = useState(false);
  const [openCreateAccountingInvoice, setOpenCreateAccountingInvoice] = useState(false);
  const [openCreateBill, setOpenCreateBill] = useState(false);
  const [tenancyAction, setTenancyAction] = useState<TenancyActionType | null>(null);
  const [showTenancyInformation, setShowTenancyInformation] = useState(false);

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const showMoreMenu = Boolean(anchorEl);
  const { apiCall: getEotDetail, data: eotDetail } = useApi(getEotDetailByTenancyId);
  const { apiCall: getApplicationDetailApi, data: applicationDetail } =
    useApi(getApplicationDetail);
  const { stage, id: eotId } = eotDetail?.data || {};
  const { updateEOTDetail } = useContext(MoveOutContext);
  useEffect(() => {
    updateEOTDetail(eotDetail?.data ?? null);
  }, [eotDetail, updateEOTDetail]);
  const organisationType = user?.organisation?.type;
  const location = useLocation();
  const isRenewTenancyDisabled = () => {
    if (!eotId) {
      return true;
    }
    if (organisationType === "LANDLORD") {
      return stage !== EndOfTenancyStage.WaitingToStart;
    }
    return ["COMPLETED", "EXPIRED", "TERMINATED"].includes(stage!);
  };

  const { apiCall: getPropertyContractsApi, data: propertyContracts } =
    useApi(getPropertyContracts);

  const existMultipleTenancy = propertyContracts?.data?.listTenanciesForProperty?.some(
    item => item?.status === "ACTIVE",
  );

  const handleMenuItemClick = (item: TenancyActionType) => {
    if (isDisabledRenewTenancy && item.type === TenancyStatus.RENEWING) {
      return;
    }
    if (!item?.type && item.from === TenancyStatus.VACATING) {
      handleEndTenancy();
    } else if (!item?.type && item.from === TenancyStatus.APPLICATION && existApplications) {
      navigate(`/property/${propertyId}/application`);
    } else {
      setTenancyAction(item);
    }
    setAnchorEl(null);
  };

  const handleMoreButtonClick = (event?: React.MouseEvent<HTMLButtonElement>) => {
    // handle more button click
    setAnchorEl(event?.currentTarget ?? null);
  };

  const handleEndTenancy = () => {
    navigate({
      pathname: `/property/${params.id}/contracts/${params.tenancyId}/move-out`,
      search: location.search,
    });
  };
  const handleNavigateToLedgers = () => {
    navigate({
      pathname: `/property/${params.id}/contracts/${params.tenancyId}/ledgers`,
      search: `${location.search}&landlordId=${tenancy.landlords?.[0]?.id}&tenantId=${tenancy.primaryTenant?.id}`,
    });
  };
  const handleCreateInvoiceSuccess = () => {
    getInvoices();
    navigate(`/property/${params.id}/documents?tab=invoices`);
  };

  const showTenancyDetail = useCallback(() => {
    setShowTenancyInformation(true);
  }, []);

  useEffect(() => {
    if (tenancy?.id) {
      getEotDetail(tenancy?.id);
    }
  }, [getEotDetail, tenancy?.id]);

  useEffect(() => {
    if (propertyId) {
      getApplicationDetailApi(propertyId);
      getPropertyContractsApi(propertyId);
    }
  }, [propertyId, getApplicationDetailApi, getPropertyContractsApi]);

  useEffect(() => {
    emitter?.on("openTenancyInformation", showTenancyDetail);
    return () => {
      emitter?.off("openTenancyInformation", showTenancyDetail);
    };
  }, [emitter, showTenancyDetail]);

  const existApplications = applicationDetail?.data?.length > 0;
  const actionConfig = TenancyActionConfig[tenancy?.status as TenancyStatus];
  const showActions = actionConfig?.actions?.map(item => {
    if (
      item.type === TenancyStatus.ACTIVE &&
      item.from === TenancyStatus.DRAFT &&
      existMultipleTenancy
    ) {
      return {
        ...item,
        title: t("existingActiveTenancyFound"),
        body: t("existingActiveTenancyFoundTips"),
      };
    }
    return item;
  });

  const enableMenuIcon = (actionConfig?.actions?.length ?? 0) > 0;
  const isDisabledRenewTenancy = isRenewTenancyDisabled();

  return (
    <>
      <Card variant="elevation" elevation={0} sx={{ mb: 2.5, p: 2.5, borderRadius: 1 }}>
        <Box display="flex" justifyContent="space-between">
          <Typography variant="subtitle1" fontWeight="bold">
            {t("manage")}
          </Typography>
          {enableMenuIcon && (
            <IconButton onClick={handleMoreButtonClick} data-testid="tenancy-management-more-btn">
              <MoreHorizIcon />
            </IconButton>
          )}
        </Box>
        <Stack spacing={1.5} mt={1}>
          <Box display="flex" gap={1.5}>
            {hasSubscription(user, [SubscriptionType.LOFTYPAY, SubscriptionType.FULL]) && (
              <>
                <Button
                  variant="contained"
                  color="primary"
                  fullWidth
                  startIcon={<AddIcon />}
                  size="large"
                  onClick={() => setOpenCreateAccountingInvoice(true)}
                  data-testid="create-accounting-invoice-btn"
                >
                  {t("createCharge")}
                </Button>
                <Button
                  variant="contained"
                  color="primary"
                  fullWidth
                  startIcon={<AddIcon />}
                  size="large"
                  onClick={() => setOpenCreateBill(true)}
                  data-testid="create-bill-btn"
                >
                  {t("createInvoice")}
                </Button>
              </>
            )}
            {hasSubscription(user, [SubscriptionType.LOFTYWORKS]) && (
              <Button
                variant="contained"
                color="primary"
                fullWidth
                startIcon={<AddIcon />}
                size="large"
                onClick={() => setOpenCreateInvoice(true)}
                data-testid="create-invoice-btn"
                disabled={integrationStatus.status !== "CONNECTED"}
              >
                {t("createInvoice")}
              </Button>
            )}
          </Box>
          {hasSubscription(user, [SubscriptionType.LOFTYPAY, SubscriptionType.FULL]) && (
            <Button
              variant="contained"
              size="large"
              fullWidth
              startIcon={<Visibility />}
              data-testid="view-ledgers-btn"
              onClick={handleNavigateToLedgers}
            >
              {t("viewLedgers")}
            </Button>
          )}
          <Button
            variant="outlined"
            size="large"
            fullWidth
            startIcon={<MailOutlineIcon />}
            disabled
            data-testid="compose-letter-btn"
          >
            {t("composeLetter")}
          </Button>
        </Stack>
        <Menu
          anchorEl={anchorEl}
          open={showMoreMenu}
          onClose={() => setAnchorEl(null)}
          MenuListProps={{
            "aria-labelledby": "basic-button",
          }}
        >
          {showActions?.map(item => (
            <MenuItem
              key={item?.label}
              onClick={() => handleMenuItemClick(item)}
              data-testid="tenancy-action-end-tenancy"
            >
              <ListItemText
                sx={{
                  opacity: isDisabledRenewTenancy && item.type === TenancyStatus.RENEWING ? 0.5 : 1,
                }}
              >
                {getManageCardActionLabel(t, item, existApplications)}
              </ListItemText>
            </MenuItem>
          ))}
        </Menu>
        {tenancyAction && (
          <StatusModal
            tenancy={tenancy}
            tenancyAction={tenancyAction}
            onClose={shouldRefresh => {
              setTenancyAction(null);
              shouldRefresh && refetchTenancy?.();
            }}
          />
        )}
      </Card>
      {openCreateInvoice && (
        <CreateInvoicePopup
          open={openCreateInvoice}
          onClose={() => setOpenCreateInvoice(false)}
          contract={tenancy}
          onCreateSuccess={handleCreateInvoiceSuccess}
        />
      )}
      {openCreateBill && (
        <BillModal
          goBackOnClose={false}
          onClose={() => setOpenCreateBill(false)}
          propertyId={params.id}
        />
      )}
      {openCreateAccountingInvoice && (
        <InvoiceModal
          onClose={() => setOpenCreateAccountingInvoice(false)}
          tenantId={tenancy.primaryTenant?.id}
          tenancyPropertyId={params.id}
        />
      )}

      {showTenancyInformation && (
        <TenancyInformation
          tenancy={tenancy}
          onClose={shouldRefresh => {
            setShowTenancyInformation(false);
            shouldRefresh && refetchTenancy?.();
          }}
        />
      )}
    </>
  );
};
