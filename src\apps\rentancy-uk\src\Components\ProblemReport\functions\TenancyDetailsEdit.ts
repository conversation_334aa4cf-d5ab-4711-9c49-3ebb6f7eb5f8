import {
  convertDateUTC,
  multipleMoneyBy100,
  Tenancy,
  updateTenancy as update<PERSON><PERSON><PERSON><PERSON><PERSON>,
  TENANCY_STATUS_LIST_ENUM,
} from "@rentancy/common";

export async function updateTenancy(
  tenancyId: string,
  setLoading: (arg: boolean) => void,
  detailsValues: Partial<Tenancy>,
  handleBack: (values?: Tenancy) => void,
) {
  setLoading(true);
  const values = JSON.parse(JSON.stringify(detailsValues));
  try {
    values.id = tenancyId;
    if (values.rent) {
      values.rent = multipleMoneyBy100(Number(values.rent));
    }
    if (values.area || values.area === "") {
      values.area = multipleMoneyBy100(Number(values.area));
    }
    if (values.openingBalance) {
      values.openingBalance = multipleMoneyBy100(Number(values.openingBalance));
    }
    if (values.settings) {
      delete values.settings;
    }
    if (values.breakClauseItems) {
      delete values.breakClauseItems;
    }
    if (values.deposit) {
      values.deposit = multipleMoneyBy100(Number(values.deposit));
    }
    if (values.askingPrice) {
      values.askingPrice = multipleMoneyBy100(Number(values.askingPrice));
    }
    if (values.agreedPrice) {
      values.agreedPrice = multipleMoneyBy100(Number(values.agreedPrice));
    }
    if (values.reservation) {
      values.reservation = multipleMoneyBy100(Number(values.reservation));
    }
    if (values.startDate) {
      values.startDate = convertDateUTC(values.startDate);
    }
    if (values.endDate) {
      values.endDate = convertDateUTC(values.endDate);
    }
    if (values.breakClauseDate) {
      values.breakClauseDate = convertDateUTC(values.breakClauseDate);
    }

    for (const propName in values) {
      if (values[propName] === undefined || values[propName] === "") {
        delete values[propName];
      }
    }

    const { data } = await updateTenancyApi(values);
    if (data?.updateTenancy?.id) {
      handleBack(values);
      setLoading(false);
    } else {
      throw new Error("");
    }
  } catch (error) {
    setLoading(false);
    console.error("Error in updating tenancy details", error);
  }
}

export const periodOptions = [
  { label: "Weekly", value: "WEEKLY" },
  { label: "Every 2 Weeks", value: "TWO_WEEKLY" },
  { label: "PCM", value: "MONTHLY" },
  { label: "Quarterly", value: "QUARTERLY" },
  { label: "UK Quarterly", value: "UK_QUARTERLY" },
  { label: "Six Monthly", value: "SIX_MONTHLY" },
  { label: "Annually", value: "ANNUALLY" },
  { label: "Biannually", value: "BI_ANNUALLY" },
  { label: "5 Year", value: "FIVE_YEAR" },
  { label: "10 Year", value: "TEN_YEAR" },
  { label: "15 Year", value: "FIFTEEN_YEAR" },
  { label: "20 Year", value: "TWENTY_YEAR" },
  { label: "25 Year", value: "TWENTY_FIVE_YEAR" },
];
export const monthDays = [
  { label: "1st", value: 1 },
  { label: "2nd", value: 2 },
  { label: "3rd", value: 3 },
  { label: "4th", value: 4 },
  { label: "5th", value: 5 },
  { label: "6th", value: 6 },
  { label: "7th", value: 7 },
  { label: "8th", value: 8 },
  { label: "9th", value: 9 },
  { label: "10th", value: 10 },
  { label: "11th", value: 11 },
  { label: "12th", value: 12 },
  { label: "13th", value: 13 },
  { label: "14th", value: 14 },
  { label: "15th", value: 15 },
  { label: "16th", value: 16 },
  { label: "17th", value: 17 },
  { label: "18th", value: 18 },
  { label: "19th", value: 19 },
  { label: "20th", value: 20 },
  { label: "21st", value: 21 },
  { label: "22nd", value: 22 },
  { label: "23rd", value: 23 },
  { label: "24th", value: 24 },
  { label: "25th", value: 25 },
  { label: "26th", value: 26 },
  { label: "27th", value: 27 },
  { label: "28th", value: 28 },
  { label: "29th", value: 29 },
  { label: "30th", value: 30 },
  { label: "31st", value: 31 },
];

export const DEPOSIT_PROTECTION_SCHEME_LIST = [
  { value: "NA", label: "NA" },
  { value: "DPS_CUSTODIAL", label: "DPS Custodial" },
  { value: "DPS_INSURANCE", label: "DPS Insurance" },
  { value: "MY_DEPOSITS", label: "My Deposits" },
  { value: "TDS_INSURANCE", label: "TDS Insurance" },
  { value: "TDS_CUSTODIAL", label: "TDS Custodial" },
  { value: "REPOSIT", label: "Reposit" },
  { value: "DISPUTE_SERVICE_CUSTODIAL", label: "Dispute Service Custodial" },
  { value: "INSURANCE", label: "Insurance" },
  { value: "HELD_BY_AGENT", label: "Held by Agent" },
  { value: "HELD_BY_LANDLORD", label: "Held by Landlord" },
];
export enum CONTRACT_TYPES {
  AST = "AST",
  ASSURED = "ASSURED",
  CONTRACTUAL = "CONTRACTUAL",
  COMMON_LAW = "COMMON_LAW",
  LETTING = "LETTING",
  SALE = "SALE",
  LICENSE = "LICENSE",
  COMMERCIAL = "COMMERCIAL",
  LEASE = "LEASE",
  SERVICE_CHARGE = "SERVICE_CHARGE",
  GROUND_RENT = "GROUND_RENT",
  HOLIDAYLET = "HOLIDAYLET",
  NONHOUSINGACT = "NONHOUSINGACT",
  PROJECT = "PROJECT",
}
export const typeOptions = [
  { value: CONTRACT_TYPES.AST, label: "AST" },
  { value: CONTRACT_TYPES.ASSURED, label: "Assured" },
  { value: CONTRACT_TYPES.COMMON_LAW, label: "Common Law" },
  { value: CONTRACT_TYPES.LETTING, label: "Letting" },
  { value: CONTRACT_TYPES.CONTRACTUAL, label: "Contractual" },
  { value: CONTRACT_TYPES.LETTING, label: "Letting" },
  { value: CONTRACT_TYPES.SALE, label: "Sale" },
  { value: CONTRACT_TYPES.LEASE, label: "Lease" },
  { value: CONTRACT_TYPES.LICENSE, label: "License" },
  { value: CONTRACT_TYPES.COMMERCIAL, label: "Commercial" },
  { value: CONTRACT_TYPES.SERVICE_CHARGE, label: "Service Charge" },
  { value: CONTRACT_TYPES.HOLIDAYLET, label: "Holiday Let" },
  { value: CONTRACT_TYPES.NONHOUSINGACT, label: "Non-Housing Act" },
  { value: CONTRACT_TYPES.PROJECT, label: "Project" },
  { value: CONTRACT_TYPES.GROUND_RENT, label: "Ground Rent" },
];
export const CONTRACT_TYPES_CATEGORY = [
  { value: CONTRACT_TYPES.AST, text: "AST", category: "Residential" },
  { value: CONTRACT_TYPES.ASSURED, text: "Assured", category: "Residential" },
  { value: CONTRACT_TYPES.COMMON_LAW, text: "Common Law", category: "Residential" },
  { value: CONTRACT_TYPES.LETTING, text: "Letting", category: "Residential" },
  { value: CONTRACT_TYPES.CONTRACTUAL, text: "Contractual", category: "Residential" },
  { value: CONTRACT_TYPES.SALE, text: "Sale", category: "Residential" },
  { value: CONTRACT_TYPES.LICENSE, text: "License", category: "Residential" },
  { value: CONTRACT_TYPES.HOLIDAYLET, text: "Holiday Let", category: "Residential" },
  { value: CONTRACT_TYPES.NONHOUSINGACT, text: "Non-Housing Act", category: "Residential" },
  { value: CONTRACT_TYPES.PROJECT, text: "Project", category: "Residential" },
  { value: CONTRACT_TYPES.SERVICE_CHARGE, text: "Service Charge", category: "Commercial" },
  { value: CONTRACT_TYPES.LEASE, text: "Lease", category: "Commercial" },
  { value: CONTRACT_TYPES.GROUND_RENT, text: "Ground Rent", category: "Commercial" },
];

export const CONTRACT_TYPES_STATUSES: {
  value: CONTRACT_TYPES;
  statuses: TENANCY_STATUS_LIST_ENUM[];
}[] = [
  {
    value: CONTRACT_TYPES.AST,
    statuses: [
      TENANCY_STATUS_LIST_ENUM.DRAFT,
      TENANCY_STATUS_LIST_ENUM.APPLICATION,
      TENANCY_STATUS_LIST_ENUM.ACTIVE,
      TENANCY_STATUS_LIST_ENUM.RENEWED,
      TENANCY_STATUS_LIST_ENUM.PERIODIC,
      TENANCY_STATUS_LIST_ENUM.NOTICE_GIVEN,
      TENANCY_STATUS_LIST_ENUM.VACATING,
      TENANCY_STATUS_LIST_ENUM.VACATED,
      TENANCY_STATUS_LIST_ENUM.CANCELLED,
      TENANCY_STATUS_LIST_ENUM.LET_EXTERNALLY,
      TENANCY_STATUS_LIST_ENUM.ARCHIVED,
      TENANCY_STATUS_LIST_ENUM.RENEWING,
      TENANCY_STATUS_LIST_ENUM.EXPIRING,
      TENANCY_STATUS_LIST_ENUM.UPCOMING,
    ],
  },
  {
    value: CONTRACT_TYPES.ASSURED,
    statuses: [
      TENANCY_STATUS_LIST_ENUM.DRAFT,
      TENANCY_STATUS_LIST_ENUM.APPLICATION,
      TENANCY_STATUS_LIST_ENUM.ACTIVE,
      TENANCY_STATUS_LIST_ENUM.RENEWED,
      TENANCY_STATUS_LIST_ENUM.PERIODIC,
      TENANCY_STATUS_LIST_ENUM.NOTICE_GIVEN,
      TENANCY_STATUS_LIST_ENUM.VACATING,
      TENANCY_STATUS_LIST_ENUM.VACATED,
      TENANCY_STATUS_LIST_ENUM.CANCELLED,
      TENANCY_STATUS_LIST_ENUM.LET_EXTERNALLY,
      TENANCY_STATUS_LIST_ENUM.ARCHIVED,
      TENANCY_STATUS_LIST_ENUM.RENEWING,
      TENANCY_STATUS_LIST_ENUM.EXPIRING,
      TENANCY_STATUS_LIST_ENUM.UPCOMING,
    ],
  },
  {
    value: CONTRACT_TYPES.COMMON_LAW,
    statuses: [
      TENANCY_STATUS_LIST_ENUM.DRAFT,
      TENANCY_STATUS_LIST_ENUM.APPLICATION,
      TENANCY_STATUS_LIST_ENUM.ACTIVE,
      TENANCY_STATUS_LIST_ENUM.RENEWED,
      TENANCY_STATUS_LIST_ENUM.PERIODIC,
      TENANCY_STATUS_LIST_ENUM.NOTICE_GIVEN,
      TENANCY_STATUS_LIST_ENUM.VACATING,
      TENANCY_STATUS_LIST_ENUM.VACATED,
      TENANCY_STATUS_LIST_ENUM.CANCELLED,
      TENANCY_STATUS_LIST_ENUM.LET_EXTERNALLY,
      TENANCY_STATUS_LIST_ENUM.ARCHIVED,
      TENANCY_STATUS_LIST_ENUM.RENEWING,
      TENANCY_STATUS_LIST_ENUM.EXPIRING,
      TENANCY_STATUS_LIST_ENUM.UPCOMING,
    ],
  },
  {
    value: CONTRACT_TYPES.LETTING,
    statuses: [
      TENANCY_STATUS_LIST_ENUM.DRAFT,
      TENANCY_STATUS_LIST_ENUM.APPLICATION,
      TENANCY_STATUS_LIST_ENUM.ACTIVE,
      TENANCY_STATUS_LIST_ENUM.RENEWED,
      TENANCY_STATUS_LIST_ENUM.PERIODIC,
      TENANCY_STATUS_LIST_ENUM.NOTICE_GIVEN,
      TENANCY_STATUS_LIST_ENUM.VACATING,
      TENANCY_STATUS_LIST_ENUM.VACATED,
      TENANCY_STATUS_LIST_ENUM.CANCELLED,
      TENANCY_STATUS_LIST_ENUM.LET_EXTERNALLY,
      TENANCY_STATUS_LIST_ENUM.ARCHIVED,
    ],
  },
  {
    value: CONTRACT_TYPES.CONTRACTUAL,
    statuses: [
      TENANCY_STATUS_LIST_ENUM.DRAFT,
      TENANCY_STATUS_LIST_ENUM.APPLICATION,
      TENANCY_STATUS_LIST_ENUM.ACTIVE,
      TENANCY_STATUS_LIST_ENUM.RENEWED,
      TENANCY_STATUS_LIST_ENUM.PERIODIC,
      TENANCY_STATUS_LIST_ENUM.NOTICE_GIVEN,
      TENANCY_STATUS_LIST_ENUM.VACATING,
      TENANCY_STATUS_LIST_ENUM.VACATED,
      TENANCY_STATUS_LIST_ENUM.CANCELLED,
      TENANCY_STATUS_LIST_ENUM.LET_EXTERNALLY,
      TENANCY_STATUS_LIST_ENUM.ARCHIVED,
      TENANCY_STATUS_LIST_ENUM.RENEWING,
      TENANCY_STATUS_LIST_ENUM.EXPIRING,
      TENANCY_STATUS_LIST_ENUM.UPCOMING,
    ],
  },
  {
    value: CONTRACT_TYPES.LETTING,
    statuses: [
      TENANCY_STATUS_LIST_ENUM.DRAFT,
      TENANCY_STATUS_LIST_ENUM.APPLICATION,
      TENANCY_STATUS_LIST_ENUM.ACTIVE,
      TENANCY_STATUS_LIST_ENUM.RENEWED,
      TENANCY_STATUS_LIST_ENUM.PERIODIC,
      TENANCY_STATUS_LIST_ENUM.NOTICE_GIVEN,
      TENANCY_STATUS_LIST_ENUM.VACATING,
      TENANCY_STATUS_LIST_ENUM.VACATED,
      TENANCY_STATUS_LIST_ENUM.CANCELLED,
      TENANCY_STATUS_LIST_ENUM.LET_EXTERNALLY,
      TENANCY_STATUS_LIST_ENUM.ARCHIVED,
    ],
  },
  {
    value: CONTRACT_TYPES.NONHOUSINGACT,
    statuses: [
      TENANCY_STATUS_LIST_ENUM.DRAFT,
      TENANCY_STATUS_LIST_ENUM.APPLICATION,
      TENANCY_STATUS_LIST_ENUM.ACTIVE,
      TENANCY_STATUS_LIST_ENUM.RENEWED,
      TENANCY_STATUS_LIST_ENUM.PERIODIC,
      TENANCY_STATUS_LIST_ENUM.NOTICE_GIVEN,
      TENANCY_STATUS_LIST_ENUM.VACATING,
      TENANCY_STATUS_LIST_ENUM.VACATED,
      TENANCY_STATUS_LIST_ENUM.CANCELLED,
      TENANCY_STATUS_LIST_ENUM.LET_EXTERNALLY,
      TENANCY_STATUS_LIST_ENUM.ARCHIVED,
      TENANCY_STATUS_LIST_ENUM.RENEWING,
      TENANCY_STATUS_LIST_ENUM.EXPIRING,
      TENANCY_STATUS_LIST_ENUM.UPCOMING,
    ],
  },
  {
    value: CONTRACT_TYPES.SALE,
    statuses: [
      TENANCY_STATUS_LIST_ENUM.DRAFT,
      TENANCY_STATUS_LIST_ENUM.UNDER_OFFER,
      TENANCY_STATUS_LIST_ENUM.OFFER_ACCEPTED,
      TENANCY_STATUS_LIST_ENUM.SOLD_STC,
      TENANCY_STATUS_LIST_ENUM.ARCHIVED,
    ],
  },
  {
    value: CONTRACT_TYPES.LICENSE,
    statuses: [
      TENANCY_STATUS_LIST_ENUM.DRAFT,
      TENANCY_STATUS_LIST_ENUM.CHECK_IN,
      TENANCY_STATUS_LIST_ENUM.ACTIVE,
      TENANCY_STATUS_LIST_ENUM.VACATED,
      TENANCY_STATUS_LIST_ENUM.CANCELLED,
      TENANCY_STATUS_LIST_ENUM.ARCHIVED,
    ],
  },
  {
    value: CONTRACT_TYPES.HOLIDAYLET,
    statuses: [
      TENANCY_STATUS_LIST_ENUM.DRAFT,
      TENANCY_STATUS_LIST_ENUM.CHECK_IN,
      TENANCY_STATUS_LIST_ENUM.ACTIVE,
      TENANCY_STATUS_LIST_ENUM.VACATED,
      TENANCY_STATUS_LIST_ENUM.CANCELLED,
      TENANCY_STATUS_LIST_ENUM.ARCHIVED,
    ],
  },
  {
    value: CONTRACT_TYPES.LEASE,
    statuses: [
      TENANCY_STATUS_LIST_ENUM.DRAFT,
      TENANCY_STATUS_LIST_ENUM.APPLICATION,
      TENANCY_STATUS_LIST_ENUM.ACTIVE,
      TENANCY_STATUS_LIST_ENUM.RENEWED,
      TENANCY_STATUS_LIST_ENUM.PERIODIC,
      TENANCY_STATUS_LIST_ENUM.NOTICE_GIVEN,
      TENANCY_STATUS_LIST_ENUM.VACATING,
      TENANCY_STATUS_LIST_ENUM.VACATED,
      TENANCY_STATUS_LIST_ENUM.CANCELLED,
      TENANCY_STATUS_LIST_ENUM.LET_EXTERNALLY,
      TENANCY_STATUS_LIST_ENUM.ARCHIVED,
      TENANCY_STATUS_LIST_ENUM.RENEWING,
      TENANCY_STATUS_LIST_ENUM.EXPIRING,
      TENANCY_STATUS_LIST_ENUM.UPCOMING,
    ],
  },
  {
    value: CONTRACT_TYPES.SERVICE_CHARGE,
    statuses: [
      TENANCY_STATUS_LIST_ENUM.DRAFT,
      TENANCY_STATUS_LIST_ENUM.ACTIVE,
      TENANCY_STATUS_LIST_ENUM.VACATED,
      TENANCY_STATUS_LIST_ENUM.ARCHIVED,
    ],
  },
  {
    value: CONTRACT_TYPES.GROUND_RENT,
    statuses: [
      TENANCY_STATUS_LIST_ENUM.DRAFT,
      TENANCY_STATUS_LIST_ENUM.ACTIVE,
      TENANCY_STATUS_LIST_ENUM.VACATED,
      TENANCY_STATUS_LIST_ENUM.ARCHIVED,
    ],
  },
  {
    value: CONTRACT_TYPES.PROJECT,
    statuses: [
      TENANCY_STATUS_LIST_ENUM.DRAFT,
      TENANCY_STATUS_LIST_ENUM.ACTIVE,
      TENANCY_STATUS_LIST_ENUM.VACATED,
      TENANCY_STATUS_LIST_ENUM.ARCHIVED,
    ],
  },
];
