export default {
  addNewTenancy: "Tenancy",
  endOfTenancy: "End of Tenancy",
  applicant: "Applicant",
  applications: "Applications",
  endTenancy: "End Tenancy",
  addNew: "Add New",
  saveAndExit: "Save & Exit",
  landlordReview: "Landlord Review",
  tenantReview: "Tenant Review",
  confirmation: "Confirmation",
  tenantReviewSuccessMessage:
    "The End of Tenancy process is now complete. If the tenancy is not renewing, remember to visit <0>link</0> to update the deposit status.",
  emailSent: "Email Sent",
  landlordReviewSuccessMessage: "You will receive an update shortly with next steps.",
  tenancyInformation: "Tenancy Information",
  createTenancy: "Create Your First Tenancy",
  createTenancyDesc:
    "Set up tenants, rent details, and agreements to manage your tenancy smoothly in one place.",
  createTenancyDoc: "Add tenancy agreement or related documents.",
  tenancyDetail: "Tenancy Details",
  oneOffChargesDesc:
    "Add non-recurring costs such as move-in fees, maintenance, late payments, or admin charges.",
  currentRent: "Current Rent",
  newRent: "New Rent",
  newRentDesc: "Only change amount if rent change recommended",
  newRentError: "New Rent should greater than or equal to 0",
  letterForLandlord: "Letter for Landlord",
  landlordEmailNotExist: "Landlord email does not exist",
  tenantEmailNotExist: "Tenant email does not exist",
  addEmail: "Add Email",
  depositManagement: "Deposit Management",
  messageLandlord: "Message Landlord",
  messageTenant: "Message Tenant",
  tenancyTerminated: "Tenancy Terminated",
  tenancyCompleted: "End of Tenancy Completed",
  waitingLandlordreply: "Waiting for Landlord to reply",
  waitingTenantreply: "Waiting for Tenant to reply",
  endProcessMessage:
    "The End of Tenancy process is now complete.\nRemember to visit Deposit Management to update the deposit status.",
  successProcessMessage: "The End of Tenancy process is now complete. ",
  waitingLandlordreplyMessage:
    "You will be notified once the landlord replies. In the meantime,\nyou can use our communication tool to send them a message.",
  waitingTenantreplyMessage:
    "You will be notified once the tenant replies. In the meantime,\nyou can use our communication tool to send them a message.",
  backEOT: "Back to End of Tenancy",
  formSubmit: "Form Submitted",
  landlordConfirmationEmailSuccess:
    "Thank you. We will contact the tenant to notify them of your decision and update you accordingly.",
  tenantConfirmationEmailSuccess1: "Thanks for completing the form.",
  tenantConfirmationEmailSuccess2: "We will be in touch with next steps.",
  commonConfirmationSuccess: "Thanks for completing the form. We will be in touch with next steps.",
  landlordResponse: "Landlord Response",
  tenantResponse: "Tenant Response",
  letterForTenant: "Letter For Tenant",
  createTask: "Create Task",
  decision: "Decision",
  rent: "Rent",
  renewal: "Renewal",
  addTask: "Add Task",
  addATask: "Add a task",
  proposedRent: "Proposed Rent",
  endOfTenancyCompletedMessage: "The End of Tenancy process is now complete.",
  useDocusign: "Use Docusign",
  sendOffline: "Send Offline",
  signNewContract: "Sign a New Contract",
  tenancyTerminatedMessage:
    "The End of Tenancy process is now complete. Remember to visit Deposit Management to update the deposit status.",
  leadTenant: "Lead Tenant",
  address: "Address",
  questionAcceptIncreasePrice:
    "Would you like to accept the recommended rent increase of {price} ?",
  questionAcceptDecreasePrice:
    "Would you like to accept the recommended rent decrease of {price} ?",
  questionIncreasePrice: "What is your proposed rent?",
  questionAcceptRenewTenancy: "Would you like to renew with the current tenant ?",
  questionAcceptRenewYourTenancy: "Would you like to renew your tenancy ?",
  questionPeriodRenew: "How long would you like to renew the tenancy for ?",
  questionRenewTenancyFurther: "Would you like to renew for a further {period} months ?",
  questionRenewRollingPeriod: "Would you like to renew on a month-to-month basis?",
  questionAcceptRecommendedIncreasePrice:
    "Do you accept the recommended rent increase of {price} ?",
  questionTenantProvideProposeIncreasePrice:
    "Please propose a rent increase amount below. We will get back to you with a confirmation.",
  months: "months",
  proposalPeriod: "{period} months",
  rolling: "Rolling",
  confirmationEmailSent: "Confirmation Email Sent",
  EOTCompleted: "End Of Tenacy Completed",
  pleaseInputAmount: "Please input a valid amount",
  tenancyConfirmation: "Tenancy Confirmation",
  addATenancy: "Add A Tenancy",
  addTenancy: "Add Tenancy",
  addATenancyMessage:
    "Manage your property by adding tenant details, lease terms, and rent information in one place.",
  currentTenancy: "Current Tenancy",
  tenancyStatus: "Tenancy Status",
  tenancyType: "Tenancy Type",
  tenancyStart: "Tenancy Start",
  deposit: "Deposit",
  depositHeld: "Deposit Held",
  rentAmount: "Rent Amount",
  nextPayment: "Next Payment",
  lastPayment: "Last Payment",
  letting: "Letting",
  forLet: "For Let",
  composeLetter: "Compose Letter",
  renewTenancy: "Renew Tenancy",
  manage: "Manage",
  tenantName: "Tenant Name",
  addPermittedOccupiers: "Add Permitted Occupiers",
  erv: "ERV",
  rightToRent: "Right to Rent",
  guarantorName: "Guarantor Name",
  permittedOccupierName: "Occupier Name",
  members: "Members",
  editTenancy: "Edit Tenancy",
  enterTitle: "Enter Title",
  changeDateWarning: "Change Date Warning",
  changeDateWarningMsg:
    "Changing the date will result in the tenancy status update and may impact associated operations. Do you wish to proceed?",
  unsavedChanges: "Unsaved Changes",
  unsavedChangesMsg1: "Are you sure you want to leave this page?",
  unsavedChangesMsg2: "Changes you made will not be saved.",
  enabledForTenancy: "Enabled for tenancy",
  nextSendDate: "Next Send Date",
  nextDueDate: "Next Due Date",
  lastPaymentTo: "Last Payment to",
  nextRentInvoice: "Next Rent Invoice",
  cancel: "Cancel",
  confirmAndEnd: "Confirm & End",
  endTenancyTips:
    "Are you sure you want to cancel your tenancy before the end date? This action cannot be undone.",
  reminder: "Reminder",
  reminderTips: `This tenancy is set to end on {{date}}. Move-out details will be available after the tenancy ends.`,
  moveOut: "Move-Out",
  moveOutDateIsRequired: "Move-out date is required",
  moveOutTips: "Complete move-out to start a new tenancy",
  moveOutCompleted: "Move Out Completed",
  moveOutDate: "Move-Out Date",
  moveOutDateTips: "Confirm your move-out date for a smooth transition",
  utilitiesUpdate: "Utilities Update",
  utilitiesUpdateTips: "Update the latest utility meters",
  checkoutReport: "Checkout Report",
  checkoutReportTips: "Upload checkout report for tenancy to keep record",
  addTaskForTenancy: "Add task for this tenancy",
  outstandingPayments: "Outstanding Payments",
  outstandingPaymentsTips: "Reminder for outstanding payments",
  notificationEmails: "Notification Emails",
  notificationEmailsTips: "Notify landlord and tenant for ending tenancy",
  tenantNotificationLetter: "Tenant Notification Letter",
  landlordNotificationLetter: "Landlord Notification Letter",
  rentGuaranteeRequired: "Rent Guarantee Required",
  existingActiveTenancyFound: "Existing Active Tenancy Found",
  existingActiveTenancyFoundTips:
    "This property already has an active tenancy. Please ensure the new tenancy details do not conflict with the existing active tenancy before proceeding. Would you like to continue?",
  checkApplication: "Check Application",
  addApplication: "Add Application",
  inviteToClientPortal: "Invite to Client Portal",
  iHaveAlreadySigned: "I’ve already signed – Just want to update the record.",
  iWantToRenew: "Let’s do it here – I’d like to renew through here.",
  allTenancies: "All Tenancies",
  duplicate: "Duplicate",
  addressReferenceOrPrimaryTenant: "Address, Reference, Primary Tenant...",
};
