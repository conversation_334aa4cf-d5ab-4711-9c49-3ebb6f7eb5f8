import { Box, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";

export const ConnectionReceive = () => {
  const { t } = useTranslation("loftyPay");

  return (
    <Box p={3}>
      <Typography variant="h5" gutterBottom>
        {t("connectionReceive")}
      </Typography>
      <Typography variant="body1" color="textSecondary">
        Bank connection receive functionality will be implemented here.
      </Typography>
    </Box>
  );
};
