# Lettings Accounting 微应用

这个应用支持两种运行模式：

## 1. 独立应用模式

作为独立应用运行，可以直接访问：

```bash
# 开发模式
npm run dev
# 访问 http://localhost:4000/lettings-accounting.html

# 生产构建
npm run build
# 部署 build/lettings-accounting.html
```

## 2. qiankun 微应用模式

作为qiankun微应用集成到主应用中：

### 主应用配置示例

```javascript
import { registerMicroApps, start } from 'qiankun';

registerMicroApps([
  {
    name: 'lettings-accounting',
    entry: '//localhost:4000/lettings-accounting.html',
    container: '#subapp-container',
    activeRule: '/lettings-accounting',
  },
]);

start();
```

### 微应用特性

- ✅ 支持独立运行
- ✅ 支持qiankun微应用模式
- ✅ 自动检测运行环境
- ✅ 路由隔离
- ✅ 样式隔离
- ✅ 生命周期管理

### 生命周期函数

微应用导出了标准的qiankun生命周期函数：

- `bootstrap()` - 应用启动
- `mount(props)` - 应用挂载
- `unmount(props)` - 应用卸载

### 环境检测

应用会自动检测是否在qiankun环境中运行：

```javascript
if (!(window as any).__POWERED_BY_QIANKUN__) {
  // 独立运行模式
  render();
}
```

## 技术栈

- React 18
- TypeScript
- Vite
- qiankun
- Material-UI
- React Router

## 开发注意事项

1. 微应用的路由配置会根据运行模式自动调整
2. 支持热更新和开发调试
3. 构建产物同时支持两种运行模式
4. 样式和脚本资源会自动处理跨域问题
