{"reselect": "Reselect", "selectATenantToCreateInvoice": "Select a Tenant to Create <PERSON><PERSON><PERSON>", "searchByContactName": "Search by contact name", "vendor": "<PERSON><PERSON><PERSON>", "expired": "Expired", "pastDue": "Past Due", "receivePayment": "Receive Payment", "newAccount": "New Account", "accountCode": "Account Code", "accountName": "Account Name", "enterAccountName": "Enter account name", "enterAccountCode": "Enter account code", "enterDescription": "Enter description", "accountInfo": "Account Info", "revenue": "Revenue", "expense": "Expense", "youWouldMakeadjustments": "if you would like to make any adjustments", "alreadyEnteredOpeningBalances": "You already entered your opening balances for this account.", "whenStartTrackingFinances": "When do you want to start tracking your finances for this account?", "whenStartTrackingFinancesDescription": "Your accounting start date is very important, because this is the day you begin tracking transactions for this bank account in LoftyWorks. Everything before this date is summarized in the opening balance you set for this bank account.", "accountingStartDate": "Accounting Start Date", "WhatTotalAccountBalanceOn": "What was your total account balance on ", "accountBalance": "Account <PERSON><PERSON>", "enterBalance": "Enter balance", "knowTheBalanceByProperty": "We also need to know the balance by property", "knowTheBalanceByPropertyDescription": "Please add the balance per property below. The total per property must be equal to the total Account Balance. This should include the total amount for each property,including deposits and reserves.", "addAnotherProperty": "Add Another Property", "remainingBalance": "Remaining Balance", "remainingBalanceMustBeZero": "Remaining balance must be zero", "thisFieldIsRequired": "This field is required", "outstandingTransaction": "Outstanding Transaction", "charges": "Charges", "paymentAmount": "Payment Amount", "receivedFrom": "Received from", "depositAccount": "Deposit account", "amountReceived": "Amount received", "paymentMethod": "Payment method", "amountToApply": "Amount to Apply", "amountToCredit": " Amount to Credit", "zeroAmount": "$0.00", "nameExists": "This name already exists", "codeExists": "This code already exists", "systemAccountsCannotBeChanged": "System accounts cannot be changed", "selectDifferentAccountOrCreateNewOne": "Please select a different account or create a new one.", "accountCannotExceed1000": "The number of account can’t exceed 1000", "selectProperty": "Select property", "newExpense": "New Expense", "expenseDate": "Expense date", "payFromAccount": "Pay from account", "accountbalance": "Account balance", "oneTimeExpense": "One time Expense", "recurringExpense": "Recurring Expense", "expenseSelection": "Expense Selection", "memo": "Memo", "addLineItems": "+ Add Line Item", "startDate": "Start Date", "endDate": "End Date", "frequency": "Frequency", "repeatForever": "Repeat Forever", "selectMethod": "Select method", "enterAmount": "Enter Amount", "noDepositsHeldByTheAccount": "There are no deposits held by the account", "withholdDeposit": "With<PERSON>", "amountWithhold": "Amount withhold", "leaseOutstandingBalance": "Lease Outstanding Balance", "totalDepositsWithheld": "Total Deposits Withheld", "amountHeld": "Amount Held", "invoiceSelection": "Invoice Selection", "oneTimeInvoice": "One time Invoice", "recurringInvoice": "Recurring Invoice", "invoiceData": "Invoice Date", "dueDate": "Due Date", "daily": "Daily", "weekly": "Weekly", "every2Weeks": "Every 2 Weeks", "biWeekly": "Bi-Weekly", "monthly": "Monthly", "every2Months": "Every 2 Months", "quarterly": " Quarterly", "ukQuarterly": "UK Quarterly", "every6Months": "Every 6 Months", "annually": "Annually", "biAnnually": "Bi Annually", "fiveYears": "Five Years", "tenYears": "Ten Years", "fifteenYears": "Fifteen Years", "twentyYears": "Twenty Years", "twentyFiveYears": "Twenty Five Years", "numberOnDaysUntilDue": "Number on days until due", "account": "Account", "selectAccount": "Select account", "description": "Description", "addLineItem": "Add Line Item", "note": "Note", "amountWithholdCantBeGreaterThanAmountHeld": "Amount Withhold can’t be greater than Amount Held", "createBill": "New Bill", "createBill2": "Create Bill", "createCredit": "Issue Credit", "createInvoice": "Create Invoice", "selectAContactToCreateBill": "Select a Contact to Create Bill", "selectAContactToIssueCredit": "Select a Contact to issue credit", "billSelection": "<PERSON>", "oneTimeBill": "One Time Bill", "recurringBill": "Recurring Bill", "billDate": "<PERSON>", "success": "Success", "DragFilesOrClickHereToUpload": " Drag files or click here to upload", "selectATenantToWithholdDeposit": "Select a Tenant to withhold deposit", "selectAContactToCreateExpense": "Select a Contact to create expense", "issueCredit": "Issue Credit", "selectATenantToRecordPayment": "Select a Tenant to record payment", "successfullySaveAutomaticAllocation": "Automatic Allocation has been saved successfully", "supplier": "Supplier", "balance": "Balance", "invoice": "Invoice", "credit": "Credit", "amountMustBeSmallerThanBalance": "Amount must be smaller than balance", "theAmountReceivedcantBeLess": "The Amount Received (plus credits) can't be less than the selected charges.", "alsoTheSelectedInvoicesCantBeLess": "Also, the selected invoices can't be less than the selected credits.", "payment": "Payment", "noOutstandingInvocie": "There is no outstanding invocie", "incomes": "Incomes", "expenses": "Expenses", "item": "<PERSON><PERSON>", "bill": "Bill", "landlordPayout": "Landlord Payout", "multiLineBill": "Multi-line Bill", "bacsReport": "BACS Report", "createABacsReport": "Create BACS report to upload bank. Supported banks NetWest, Metro, Barclays", "accounting": "Accounting", "min": "min", "sessionTimeoutWarning": "Session Timeout Warning", "yourSessionIsAboutToExpire": "Your session is about to expire due to inactivity. <br/>You will be automatically logged off in {{time}} minutes. <br /><br /> To keep your session active, preform any operation", "createCharge": "Create Charge", "updateCharge": "Update Charge", "SaveAndContinue": "Save and Continue Later", "SaveAndComplete": "Save and Complete", "application": "Application", "Passed": "Passed", "PassedWithGuarantor": "Passed with <PERSON><PERSON><PERSON><PERSON>", "Failed": "Failed", "documentTip": "Upload documents for rent deed deposit", "applications": "Applications", "addApplication": "Add Application", "completeApplication": "You're about to complete the application. Please note that once this application is marked as completed, it can't be changed.", "cancelApplication": "Would you like to cancel this application?", "deleteApplication1": "Would you like to delete this application?", "deleteApplication2": "Please note that deleting this task is irreversible.", "deleteApplication3": "Proceed with caution.", "completeApplication1": "You're about to complete the application.", "completeApplication2": "Please note that once this application is marked as completed, it can't be changed.", "emptyApplication": "Everything that needs to be done before move-in, tracked and managed in one place.", "markCompleted": "Mark as completed", "markFail": "<PERSON> as Failed", "Action": "Action", "Assign": "Assign", "Cancel": "Cancel", "Complete": "Complete", "Delete": "Delete", "HoldingDeposit": "Holding Deposit", "Completed": "Completed", "Cancelled": "Cancelled", "inProgress": "In Progress", "Referencing": "Referencing", "RightToRent": "Right to Rent", "InventoryInformation": "Inventory Information", "UploaddocumentForReference": "Upload document for reference", "MoveInDate": "Move-in Date", "failTips": "Selecting 'Failed' will mark the whole application as failed.", "uploadTips": "Upload document for right to rent", "statusLabelCompleted": "Completed", "statusLabelPending": "Pending", "statusLabelCancelled": "Cancelled", "cancellationReason": "Cancellation Reason: ", "taskLabelCompleted": "Completed", "taskLabelToDos": "{{count}} To-Dos", "taskLabelZeroToDos": "0 To-Dos", "pin": "<PERSON>n", "recent": "Recent", "unpin": "Unpin", "tag": "Tag", "tagName": "Tag Name", "markAsUnread": "<PERSON> as Unread", "markAsRead": "<PERSON> <PERSON>", "doNotDisturb": "Do Not Disturb", "enableNotifications": "Enable Notifications", "deleteChat": "Delete Chat", "personal": "Personal", "noPinnedMessages": "No pinned messages...", "noRecentMessages": "No recent messages...", "areYouSureYouWantToDeleteThisChat": "Are you sure you want to delete this chat?", "addTag": "Add Tag", "addNewTag": "Add new Tag", "plusAddTag": "+ Add Tag", "manageTags": "Manage Tags", "noMessageATM": "No messages at the moment.", "noResults": "No results", "startingNewConversation": "Start a new conversation", "pleaseEnterFirstMessageBelow": "Type your first message below", "newMessage": "New Message", "noTagsToDisplay": "No tags to display.", "tagNameAlreadyExist": "Tag name already exist.", "afterDeletingATagTheConversationsWithinTheTagAreNotDeleted": "After deleting a tag, the conversations within the tag are not deleted.", "conversationHistory": "Conversation History", "conversation": "Conversation", "textMessage": "Text Message", "linkedConversation": "Linked Conversation", "linkedConversations": "Linked Conversations", "unread": "Unread", "recipient": "Recipient", "writeANote": "Write a note...", "pressEnterToSave": "Press \"Enter\" to save", "editAssignedTo": "Edit Assigned to", "addAdministrator": "Add administrator", "viewAll": "View All", "areYouSureYouWantToDeleteThisInternalNote": "Are you sure you want to delete this Internal Note?", "editInternalNote": "Edit Internal Note", "internalNote": "Internal Note", "linkForMoreInformation": "Link for more information", "editLinks": "Edit Links", "doYouWantToActionTheConversation": "Do you want to {{action}} the conversation?", "scheduledEmailCreated": "Scheduled email created", "whatsApp": "WhatsApp", "leaveGroupChat": "Leave Group Chat", "saveToProperty": "Save to Property", "saveToTask": "Save to Task", "saveToParentProperty": "Save to Parent Property", "preScheduled": "Pre-scheduled", "chats": "Chats", "createCommunication": "Create Communication", "hereYouCanInitiateConversations": "Here, you can initiate conversations.", "clickHereToEditPersonalInfo": "Click here to edit personal information.", "auditHistory": "Audit History", "communication": "Communication", "unknownInbounds": "Unknown Inbounds", "emailSentSuccessfully": "<PERSON>ail sent successfully", "allContacts": "All Contacts", "scheduleViewing": "Schedule Viewing", "applicantName": "Applicant Name", "leadName": "Lead Name", "phoneName": "Phone Number", "assignPropertyManager": "Assign Property Manager", "placeholderSelectManager": "Select Property Manager", "selectDates": "Select Dates", "titleProperty": "Property", "placeholderSelectProperty": "Select Property", "location": "Location", "budget": "Budget", "numberOfRooms": "Number of Rooms", "numberOfBathRooms": "Number of Bathrooms", "desiredMoveInDate": "Desired Move In Date", "incomePerMonth": "Income (Per Month)", "numberOfOccupants": "Number of Occupants", "extraNotes": "Extra Notes", "lookingFor": "Looking For", "qualifyLead": "Qualify Lead", "createScheduleViewSuccess": "Create schedule viewing successfully", "createScheduleViewFailed": "Create schedule viewing failed", "prospectApplicationInfo": "Completed results will be emailed to the property manager.", "applicaitonQuestionnaire": "Questionnaire", "personalDetails": "Personal Details", "searchCriteria": "Search Criteria", "priceRangeIncorrect": "The price range you entered is incorrect. Please enter a valid range.", "enterEllipses": "Enter...", "numberSelect": "Number Select", "updateSuccessful": "Update successful", "lettingsNegotiator": "<PERSON><PERSON>s Negotiator", "contactPropertyManager": "<PERSON><PERSON>s Negotiator", "contactPropertyManagers": "Lettings Negotiators", "addContactPropertyManager": "Add Lettings Negotiator", "priceRangeIsIncorrect": "The price range you entered is incorrect. Please enter a valid range.", "viewing": "Viewing", "checkCrmWrapperForError": "Please check CRM context provider for errors.", "applicationQuestionnaireSharedSuccess": "Questionnaire shared successfully!", "shareApplicationQuestionnaire": "Share Questionnaire", "confirmToDeleteInterestedProperty": "Are you sure you want to delete this Interested Property?", "confirmToSendPropertyToApplicant": "Are you sure you want to share this property?", "dateOfInterest": "Date of Interest", "leadManagement": "Lead Management", "leadStatus": "Lead Status", "lastUpdatedDate": "Last Updated Date", "showingsArranged": "Showings Arranged", "nextShowingDate": "Next Showing Date", "viewingsArranged": "Viewings Arranged", "nextViewingDate": "Next Viewing Date", "createDate": "Create Date", "assignLettingsNegotiator": "Assign <PERSON><PERSON><PERSON> Negotiator", "tenantScreeningStatus": "Tenant Screening Status", "interested": "Interested", "suggested": "Suggested", "upcomingViewings": "Upcoming Viewings", "allViewings": "All Viewings", "deleteViewingConfirmText": "Are you sure you want to delete this Scheduled Viewing?", "lookingForNoDataText": "Complete the lead's preference for better listing recommendation and engagement", "addLookingFor": "Add Looking For", "addQualifyLead": "Add Qualify Lead", "leadEmail": "Lead Email", "sendFrom": "Send From", "leadNumber": "Send Number", "numberMaxinum100": "Maximum 100", "dateRangeIsIncorrect": "The date range you entered is incorrect. Please enter a valid range.", "electricity": "Electricity", "water": "Water", "gas": "Gas", "tv": "TV", "internetAndPhone": "Internet and Phone", "internet": "Internet", "phone": "Phone", "contractor": "Contractor", "handyman": "<PERSON><PERSON>", "electrician": "Electrician", "cleaner": "Cleaner", "plumber": "Plumber", "inventoryClerk": "Inventory Clerk", "supplierType": "Supplier Type", "preferred": "Preferred", "addPreferredSupplier": "Add Preferred Supplier", "userWithPhoneExistsAlready": "User with this phone number already exists", "dashboard": "Dashboard", "whatsNewAtLoftyWorks": "What's new at LoftyWorks", "whatsNewAtLoftyWorksDescirption": "Click the link below to see our latest releases. Then bookmark the page so you always know what’s coming up.", "integrationsDescirption": "We’ve integrated with market leading software including Xero, Google, Outlook and WhatsApp to provide you with the best all in one solution. If you want to connect your existing accounts to LoftyWorks, follow our guides or contact us via", "addFirstPropertyTitle": "Add Your First Property", "addFirstPropertyContent": "Add your first property to start managing details, tracking updates, and keeping everything organized in one place.", "addFirstPropertyDescription": "In LoftyWorks, the Property represents the rentable space. Just fill in the details then market your properties on Rightmove and social media to generate new leads.", "addFirstTenancy": "Add Your First Tenancy", "addFirstTenancyDescription": "Within LoftyWorks, you use Contracts to add your Tenancies. Then manage the end-to-end journey by following the steps", "needHelpGettingStarted": "Need Help Getting Started?", "needHelpGettingStartedDescription": "Need help setting up your account? Use our Getting Started guide or reach out to our Customer Success team at ", "contactUs": "Contact Us", "supportingYourBusiness": "Supporting Your Business", "supportingYourBusinessDescription": "If you’re short on staff or want to increase your efficiency, why not find out more about our Services team? Our ARLA qualified bookkeepers and property managers help streamline your operations and improve business success.", "connectXero": "Connect Xero", "usingXero": "Using Xero", "usingXeroDescription": "To streamline accounting, we offer deep integration with Xero. Click below to learn more and connect your account", "editDashboard": "Edit Dashboard", "hide": "<PERSON>de", "show": "Show", "tasksDueNextDays": "Tasks Due Next {{days}} Days", "tasksDueToday": "Tasks Due Today", "overdueTasks": "Overdue Tasks", "unassignedTasks": "Unassigned Tasks", "certificates": "Certificates", "expiredGasSafety": "Expired Gas Safety", "expiredEPC": "Expired EPC", "expiredOther": "Expired Other", "upcoming": "Upcoming", "recentlyViewed": "Recently Viewed", "overDueAmount": "Overdue Amount", "rentDueToday": "Rent Due Today", "rentOverdue": "Rent Overdue", "upcomingMoveIns": "Upcoming Move Ins", "upcomingRenewals": "Upcoming Renewals", "upcomingVacancies": "Upcoming Vacancies", "newLeads": "New Leads", "viewingScheduledToday": "Viewing Scheduled Today", "contractors": "Contractors", "contractorsToPay": "Contractors to Pay", "occupancyRate": "Occupancy Rate", "propertyOwnerPayout": "Property Owner Payout", "rentCollection": "Rent Collection", "workOrderRequiringApproval": "Workorder Requiring Approval", "viewWorkers": "View Workers", "monthToDate": "Month to Date", "yearToDate": "Year to Date", "noNewUpdate": "No new updates, visit <ItemLink>{{linkText}}</ItemLink>", "compliance": "Compliance", "taskWorkOrder": "Task & Work Order", "endOfTenancy": "End of Tenancy", "shareFeedbackMessage": "Share your feedback or suggestions to help us improve. We’re always looking for ways to enhance your experience!", "viewLess": "View Less", "messages": "Messages", "incomplete": "Incomplete", "ready": "Ready", "todos": "To-dos", "addNewRow": "Add New Row", "allocateBy": "Allocate by", "allocation": "Allocation", "amountToJournal": "Amount to Journal", "arrears": "Arrears", "autoJournal": "Auto Journal", "autoJournalDescription": "Auto journal feature automates the creation of financial entries by automatically recording transactions based on predefined rules and criteria.", "approve": "Approve", "approved": "Approved", "bills": "Bills", "billsToBeCarriedForward": "{{ amount }} bills to be carried forward", "clientIsRequired": "Client is required", "contractNumber": "Contract Number", "count": "Count", "currentMonth": "Current Month", "dayOfMonth": "Day of Month", "enableAutoInvoicing": "Enable Auto Invoicing", "exclusiveOfTax": "Exclusive of Tax", "featureIsInBetaModeWarning": "This feature is currently in Beta mode. Are you sure you want to continue", "finaliseRaise": "Finalise & Raise", "finalisingStatementForDates": "Finalising statement for dates", "firstProcess": "First Process", "grossAmount": "Gross Amount", "grossAmountAndTotalValueAreDifferentPleaseCheck": "Gross amount and total value are different please check", "groupByProperty": "Group By Property", "groupByType": "Group By Type", "inclusiveOfTax": "Inclusive of Tax", "itemsBroughtForward": "Items Brought Forward", "lastProcess": "Last Process", "ledgerCodeSetup": "Ledger Code Setup", "proRataFirstMonth": "Pro Rata First Month", "configureDistributionoOfFunds": "Configure Distribution of Funds", "proRataFormula": "Pro Rata is calculated through the following Days till Month End x Monthly Rent Amount x 12 Months) / 365 Days", "manual": "Manual", "nextJournalDay": "Next Journal Day", "nextProcess": "Next Process", "noArrears": "No Arrears", "noLandLordGenerateBillWarning": "Property has no assigned landlord. Please add landlord to continue.", "noTax": "No Tax", "noVat": "No VAT", "notSetUp": "Not set up", "payoutMethod": "Pay-Out Method", "payoutMethodByLandlord": "Pay-Out By Landlord", "payoutSuccessful": "Payout Successful", "payouts": "Payouts", "periodLengthInMonths": "Period Length (Months)", "periodToJournal": "Period to Journal", "pleaseConfirmTheFollowing": "Please confirm the following", "previewStatement": "Preview Statement", "manageFloat": "Manage Float", "available": "Available", "currentFloat": "Current Float", "targetFloat": "Target Float", "adjustment": "Adjustment", "inputAmount": "Input Amount", "previousJournalDay": "Previous Journal Day", "priorMonth": "Prior Month", "quantity": "Quantity", "raiseClientBillPayoutFromAmount": "Raise client bill payout from amount", "floatDeduction": "Float Deduction", "rentPaymentReport": "Rent Payment Report", "returnToPayouts": "Return to Payouts", "searchClientName": "Search client name", "selectClient": "Select Client", "selectSupplier": "Select supplier:", "selectVendor": "Select Vendor", "shareStatementWithLandlord": "Share Statement with Landlord", "showSelect": "Show & Select", "statementConfirmation": "Statement Confirmation", "subtotal": "Subtotal", "unfundedItems": "Unfunded Items", "upTo": "Up to {{amount}}", "vatOnOperation": "{{ vat }} (VAT on {{ operation }})", "yourPayoutWasSuccessful": "Your payout was successful.", "zeroRatedOperation": "Zero Rated {{ operation }}", "clientAccountingService": "Client Accounting as a Service", "LetUsTakeTheHassleHeadachesAway": "Let us take the hassle and headaches away from dealing with monthly client accounting.", "OurExpertARLAQualifiedTeamAreReady": "Our expert ARLA qualified team are ready to do the processing for you. It is just like having a finance team member working with you. Simplify your working day and enjoy more time back to focus on landlord relationships.", "payWithFloat": "Pay With Float", "paywithFloat": "Pay with Float", "currentFloatBalance": "Current Float Balance", "newFloatBalance": "New Float Balance", "arrearsChasing": "Arrears Chasing", "landlordStatements": "Landlord Statements", "landlordPayments": "Landlord Payments", "supplierPayments": "Supplier Payments", "managementFeeBilling": "Management <PERSON><PERSON>", "enquireForMoreInfo": "Enquire For More Info", "lookingForSelfService": "Looking for Self-Service", "selectAll": "Select All", "deselectAll": "Deselect All", "theMinimumCostForTheSubscription": "The minimum cost for the subscription is £30 (£25+VAT) per month", "landlordStatementAdHoc": "Landlord Statement Ad Hoc", "landlordStatementFinalised": "Landlord Statement Finalised", "tenantStatement": "Tenant Statement", "depositManagement": "Deposit Management", "refund": "Refund", "receive": "Receive", "register": "Register", "transfer": "Transfer", "depositRegistration": "Deposit Registration", "DepositScheme": "Deposit Scheme", "registrationDate": "Registration Date", "certificateNumber": "Certificate Number", "certificateSharp": "Certificate #", "markAsRegistered": "Mark as Registered", "depositValue": "Deposit Value", "transferDeposit": "Transfer Deposit", "depositTransfer": "Deposit Transfer", "processReturn": "Process Return", "deductions": "Deductions", "refundDeposit": "Refund Deposit", "markAsInDispute": "<PERSON> as <PERSON> Dispute", "netReturn": "Net Return", "addCharge": "Add Charge", "markDepositAsInDisputeConfirm": "Are you sure you want to mark this deposit as In Dispute?", "transferredDate": "Transferred Date", "refundedDate": "Refunded Date", "refunded": "Refunded", "notRefunded": "Not Refunded", "returnDepositConfirm": "Are you sure you want to refund this deposit?", "releaseDeposit": "Release Deposit", "releaseDepositConfirm": "Are you sure you want to mark as released?", "released": "Released", "notReleased": "Not Released", "toReceive": "To Receive", "toRegister": "To Register", "toTransfer": "To Transfer", "toProcess": "To Process", "toRelease": "To Release", "addNewLedgerCode": "Add New Ledger Code", "invalidCode": "Invalid Code", "recurringCharges": "Recurring Charges", "oneOffCharges": "One Off Charges", "charge": "Charge", "contactType": "Contact Type", "addOneOffCharge": "Add One Off Charge", "pleaseConnectToXero": "Please Connect To Xero to enable this section. You can still complete this step below", "addNewContact": "Add New Contact", "noOptions": "No Options", "datePaid": "Date Paid", "payoutsLoadingText": "Please wait. We’re fetching your Payouts", "autoJournalDisabledBecause": "Auto Invoice is now active, and Auto Journal has been disabled.", "autoInvoicingDisabledBecause": "Auto Journal is now active, and Auto Invoice has been disabled.", "autoJournalArrears": "Auto Journal Arrears", "contractsUpdateInProgress": "Contracts are being updated on background, we will notify you once it's done", "ledgerCodesUpdated": "Ledger Codes Updated", "generateNow": "Generate Now", "unableToGenerateInvoiceEarly": "Unable to generate invoice early", "earlyInvoiceGeneratedSuccessfully": "System attempted to create invoice. Please confirm in Xero", "lastPayment": "Last Payment", "lastPaymentCoveredTo": "Last Payment Covered To", "enterRentAmount": "Enter Rent Amount", "enterReservation": "Enter Reservation", "enterLedgerCode": "Enter Ledger Code", "assetName": "Asset Name", "enterAssetName": "Enter Asset Name", "editAsset": "Edit Asset", "addNewAsset": "Add New Asset", "deletedAsset": "Deleted asset", "lockboxCode": "Lockbox Code", "inputMake": "Input Make", "inputModel": "Input Model", "assetCreatedSuccessfully": "Asset Created Successfully", "assetUpdatedSuccessfully": "Asset Updated Successfully", "selectInput": "Select", "deleteAsset": "Delete Asset?", "deletedAssetSuccessFully": "Asset Deleted Successfully", "1Day": "1 day", "1SelectTemplate": "1. Select template", "25March_24June_29September_25December": "25 March, 24 June, 29 September, 25 December", "2UploadTheDocumentYouWishToSign": "2. Upload the document you wish to sign", "2ndImage": "2nd Image", "3AddTheContactsYouWouldLikeToSign": "3. Add the contacts you would like to sign", "3rdImage": "3rd Image", "4thImage": "4th Image", "5thImage": "5th Image", "loading": "loading...", "AddNewContact": "+ Add “{{value}}” as new contact", "AddNewLease": "Add New Lease", "AddNewTenant": "Add New Tenant", "ContactInformation": "Contact Information", "SMSSentSuccessfully": "SMS sent successfully", "aCompilationOfAllActiveTasksWithinTheWorkspace": "A compilation of all active tasks within the workspace", "aUserWithThatEmailAddressAlreadyExists": "A user with that email address already exists.", "accessFullLogOfTheChangesMadeAcrossYourWholeWorkspace": "Access full log of the changes made across your whole workspace, performed by all your colleagues and contacts.", "accessFullLogOfTheSummaryOfClientBalancesByProperty": "Access full log of the summary of client balances by property", "accessFullLogOfTheSummaryOfPropertyBalancesByProperty": "Access full log of the summary of property balances by property", "achievedPrice": "Achieved Price", "achivedPrice": "Achived Price", "activateNow": "Activate Now", "active": "Active", "blocked": "Blocked", "notActive": "Not Active", "activeContracts": "Active Contracts", "activity": "Activity", "add": "Add", "addQuestion": "Add Question", "add1": "+ Add", "addActivity": "Add Activity", "addAddress": "Add address…", "addAllYourUnitsBelow": "Add all your units below", "addAnEmailAddressThatYouWouldLikeYourWorkspaceToSend": "Add an email address that you would like your workspace to send emails from. E.g. relaying messages or notifications.", "addAnother": "+ Add another", "addAnotherUnit": "Add another unit", "addAsNewContact": "+ Add as new Contact", "addAttachment": "+ Add Attachment", "addBill": "Add Bill", "addBill1": "+ Add Bill", "addBudget": "Add Budget", "addChecklist": "+ Add Checklist", "addContacts": "Add Contacts", "addContact": "Add Contact", "addContract": "Add Contract", "addTenancy": "Add Tenancy", "tenancy1": "Tenancy...", "addDeposit": "<PERSON><PERSON>", "addDescription": "Add description…", "addDetails": "Add details…", "addDocument": "Add document", "addEachFeatureOnASeparateLine": "* Add each feature on a separate line", "addExpiry": "+ Add Expiry", "addFile": "Add File", "addFile1": "Add File", "addHyperlink": "Add hyperlink", "addInputValueAsNewContact": "+ Add “{{inputValue}}” as new contact", "addInternalNote": "Add internal note…", "addKeyFeatures": "Add key features…", "addLandlord": "+ Add <PERSON>lord", "addManager": "+ Add Manager", "addMore": "Add More", "addNew": "Add New", "addNewNoPlus": "Add New", "addNewProperty": "+ Add {{value}} as new property", "addNewProperty1": "+ Add New Property", "addNote": "Add note…", "addNote1": "Add note...", "addNote2": "Add note", "addNotes": "Add notes...", "addNumber": "Add number...", "addPeople": "Add People", "addPeople1": "Add people", "addPhoto": "Add Photo", "addPhotos": "Add Photos", "addPhotos1": "+ Add Photos", "addProperty": "Add Property", "addRent": "Add Rent", "addReservation": "Add Reservation", "addRole": "+ Add {{role}}", "addRow": "+Add row", "addRow1": "Add row", "addTags": "+ Add tags", "addTask": "Add Task", "addTask1": "Add task", "addType": "Add Type", "addType1": "+ Add type", "addUnit": "Add Unit", "addVat": "Add VAT", "addWorkspace": "Add Workspace", "addedContract": "Added contract:", "addedTask": "Added task:", "addedToContacts": "Added to contacts", "additionalMessage": "Additional Message", "additionalIncome": "Additional Income", "additionalInformation": "Additional Information", "address": "Address", "addressPostCodeReference": "Address, post code, reference…", "address_1": "Address 1", "address_2": "Address 2", "address_3": "Address 3", "admin": "Admin", "adminsCanClickInviteToAddTeamMembersToTheWorkspace": "Admins can click In<PERSON>te to add team members to the workspace.", "adminsOnlyExportDataInCsvFileFormat": "Admins only, export data in csv file format", "afterSendingCheckInYourDocusignAccountForSigningStatus": "After sending, check in your Docusign account for signing status", "age": "Age", "agent": "Agent", "agentIdTitleFnameSnameEmailsEmailTypePhonesPhoneTypeI": "agent { id title fname sname emails{email type} phones{phone type} image{key} }", "ago": "ago", "all": "All", "allBills": "All Bills", "allTypes": "All Types", "allAssignee": "All Assignees", "allAddress": "All Addresses", "allStatuses": "All Statuses", "allocateTo": "Allocate to:", "allocated": "Allocated", "allocationOfBill": "Allocation of Bill", "allocationTime": "Allocation Time", "alreadyUsingLofty": "Already using <br /> LoftyWorks?", "amount": "Amount", "annualIncome": "Annual Income", "applicantInfo": "Applicant Info", "applicationInfo": "Application Info", "applicants": "Applicants", "applicationDeletedSuccessfully": "Application deleted successfully", "applicationPayment": "Online payment needs to be activated in order to charge applications a fee and request credit, criminal and eviction reports from Transunsion.", "applicationRequestCredit": "Request Credit, Criminal and Eviction History reports from Transunion (Cost: ${{cost}})", "applictionTenantScreening": "Who pays for the tenant screening?", "applyAs": "Apply As", "applyAsCosigner": "Apply as a co-signer", "applyAsTenant": "Apply as a tenant", "approveNegativeBalance": "Approve negative balance", "archive": "Archive", "archiveTask": "Archive Task", "archived": "Archived", "archivedTasks": "Archived Tasks", "areYouAwareThatYouAreAboutToPayABillWithoutHavingFu": "Are you aware, that you are about to pay a bill without having funds in the account?", "areYouSureChangeTheStatusProperty": "Are you sure to change the status of this property?", "areYouSureYouWantToDeleteThisChecklist": "Are you sure, you want to delete this checklist?", "areYouSureYouWantToDeleteThisContact": "Are you sure you want to delete this contact?", "areYouSureYouWantToDeleteThisTask": "Are you sure, you want to delete this task?", "areYouSureYouWantToLeaveThisGroup": "Are you sure, you want to leave this group?", "areYouSureYouWantToRemoveChecklistnameChecklist": "Are you sure, you want to remove checklist?", "areYouSureYouWantToRemoveTheWholePhotosSectionThisAc": "Are you sure you want to remove the whole photos section? This action remove all photos", "areYouSureYouWantToRemoveThisDocument": "Are you sure, you want to remove this document?", "areYouSureYouWantToRemoveThisUnit": "Are you sure, you want to remove this unit?", "areYouSureYouWantToRemoveThisUserFromGroup": "Are you sure, you want to remove this user from group?", "areYouSureYoudLikeToRemoveThisMapping": "Are you sure you’d like to remove this mapping?", "areYouTenantCosigner": "Are you applying as a tenant or a co-signer?", "area": "Area", "arrearsAmount": "Arrears Amount", "arrearsFinanceRentancy": "Arrears - Finance - LoftyWorks", "askingPrice": "Asking Price", "assignedTo": "Assigned to", "assignee": "Assignee", "atLeastOneLandlordHasBeenAssignedToTheProperty": "At least one landlord has been assigned to the property", "atLeastOneTenantHasBeenAssignedToTheContract": "At least one tenant has been assigned to the contract", "attach": "Attach", "attachDocument": "+ Attach Document", "attachPhotos": "+ Attach Photos", "attached": "Attached", "attachmentHasBeenDeleted": "Attachment has been deleted", "attachments": "Attachments", "asset": "<PERSON><PERSON>", "authorisingThisBillWillCreateANegativeBalance": "Authorising this bill will create a negative balance", "authorisingThisBillWillCreateANegativeClientBalance": "Authorising this bill will create a negative client balance", "automatedApplicationOfTenantFundsToOutstandingRent": "Automated application of tenant funds to outstanding rent", "autoInvoice": "Auto Invoice", "automaticAllocationofTenantFundstoOutstandingRent": "Automatic Allocation of Tenant Funds to Outstanding Rent", "automaticRentInvoiceGeneration": "Automatic Rent Invoice Generation", "availableStartDate": "Available Start Date", "atLeastOneAssigneeIsRequired": "At least one assignee is required", "back": "Back", "back1": "BACK", "backToPreviousStep": "Back to previous step", "backToTenancy": "Back to tenancy", "backgroundImages": "Background images", "backgroundImagesLoadRandomlyEachTimeThePageIsOpenedRem": "Background images load randomly each time the page is opened. Remove or add images as desired.", "balances": "Balances", "bank": "Bank", "bankDetails": "Bank Details", "bankingFinanceRentancy": "Banking - Finance - LoftyWorks", "baths": "Baths", "beds": "Beds", "bedrooms": "Bedrooms", "bathrooms": "Bathrooms", "beingPartOfTheTeamYouCanInviteMoreTeamMembersAllInfo": "Being part of the team you can invite more team members. All information within your workspace is shared with your team.", "beneficiary": "Beneficiary", "billAmount": "<PERSON>", "billGeneratedSuccessfully": "Bill generated successfully!", "billsFinanceRentancy": "Bills - Finance - LoftyWorks", "billsOutstanding": "Bills Outstanding", "billsSelectedValue": "bills selected, value", "board": "Board", "boardView": "Board View", "breakClause": "Break Clause", "breed": "Breed", "budgetSuccessfullyRemoved": "Budget successfully removed", "bulkApproval": "Bulk Approval", "bulkApprove": "Bulk Approve", "bulkPayout": "Bulk Payout", "bulkUpload": "Bulk Upload", "bulkUpload1": "Bulk upload", "businessDetails": "Business Details", "buyers": "Buyers", "byAssignee": "By Assignee", "byContinuingYoureAgreeingToOur": "By continuing, you’re agreeing to our", "byContinuingYoureAgreeingToOurIAccept": "By continuing, you’re agreeing to our I accept", "agreeTermAndCondition": "I agree to the<TermLink>Terms & Conditions</TermLink> and<PolicyLink>Privacy Policy</PolicyLink>", "subscribeNewLetter": "Subscribe to our newsletter, unsubscribe any moment", "byDefaultTheyWillBeNamedPropertyAndContractHoweverIfY": "By default they will be named Property and Contract. However if you are already using Tracking Categories in your Xero instance, you can specify their names here.", "byDefaultThisIsUnpublishedSetToPublishedIfYouWantThe": "By default this is unpublished. Set to Published if you want the page to be visible on the internet and able to be connected to your own website.", "byDueDate": "By Due Date", "byProperty": "By Property", "calculationError": "Calculation Error", "calendar": "Calendar", "calendarRentancy": "Calendar - LoftyWorks", "cancel": "Cancel", "cashBalanceReport": "Cash Balance Report", "category": "Category", "allCategories": "All categories", "change": "Change", "changeStatus": "Change status ?", "changeName": "Change Name", "changesHaveBeenSavedSuccessfully": "Changes have been saved successfully!", "chargeAppFees": "Charge Application Fees", "chargeRentFrequency": "Charge Rent Frequency", "chartOfAccount": "Chart Of Account", "chat": "Cha<PERSON>", "chatMembers": "Chat Members", "chatRentancy": "Chat - LoftyWorks", "checkYourEmail": "Check your Email", "checkYourEmail1": "Check your email!", "checklistItems": "Checklist items", "checklistSuccessfullyAddedToTheTask": "Checklist successfully added to the task!", "checklistSuccessfullyCreated": "Checklist successfully created", "checklistSuccessfullyRemoved": "Checklist successfully removed", "checklistSuccessfullyUpdated": "Checklist successfully updated", "checklistTitle": "Checklist title", "CheckOutThis": "Check out this", "city": "City", "clearAllFilters": "Clear All Filters", "clickHere": "Click Here", "clickTheButtonAboveToAddAProblemReport": "Click the button above to add a Problem Report", "client": "Client", "landlordApprovalRequired": "Landlord A<PERSON> required", "landlordApproved": "Landlord Approved", "landlordAcceptanceStatus": "Landlord Acceptance Status", "pending": "Pending", "declined": "Declined", "clientBalance": "Client Balance", "clientBalancesFinanceRentancy": "Client Balances - Finance - LoftyWorks", "clientBalancesReport": "Client Balances Report", "clientFundsInsufficient": "Client funds insufficient", "clients": "Clients", "clipboardNotSupported": "Clipboard not supported", "closing": "Closing", "closingBalance": "Closing Balance", "closingBalances": "Closing Balances", "code": "Code", "codeIsRequired": "Code is required", "codeIsRequired1": "code is required", "collectRent": "Collect Rent", "color": "Color", "companyAddress": "Company Address", "companyName": "Company Name", "companyWebsite": "Company Website", "configureForAgentWorkflowsLessThanBrGreaterThanAddClie": "Configure for agent workflows <br />Add Client Account Balances tab <br />Add Client Statement Automations <br />Default management fee 5% <br />Appends landlord details to demands/ invoices", "configureForLandlordWorkflowsLessThanBrGreaterThanDefau": "Configure for landlord workflows <br />Default No Management Fee <br />Workspace name used for landlord default", "confirm": "Confirm", "connect": "Connect", "connectToXero": "Connect to Xero", "connectToYourFinancialGeneralLedgerByMappingTheCodesIn": "Connect to your financial general ledger by mapping the codes in LoftyWorks.", "connectWithPeopleUsingWhatsapp": "Connect with people using Whatsapp", "connectWithYourDocusignAccount": "Connect with your Docusign Account", "connectWithYourXeroAccount": "Connect with your Xero Account", "connectedPropertiesAndContracts": "Connected Properties and Contracts", "connectedProperties": "Connected Properties", "connectionEstablished": "Connection Established", "connectionFailed": "Connection Failed", "contact": "Contact", "contactEmail": "Contact Email", "contactInformation": "Contact Information", "contactName": "Contact Name", "salutation": "Salutation", "contactPhone": "Contact Phone", "contacts": "Contacts", "contactsLofty": "Contacts - LoftyWorks", "containedInTheSpreadsheetIsATabListingTheFieldDefiniti": "Contained in the spreadsheet is a tab listing the field definitions.", "continue": "Continue", "contract": "Contract", "contractAddedSuccessfully": "Contract added successfully", "contractBreak": "Contract Break", "contractBreakDate": "Contract Break Date", "contractDates": "Contract Dates", "contractDetails": "Contract Details", "contractDeletedSuccessfully": "Contract deleted successfully", "contractEndDate": "Contract End Date", "contractRenewalDate": "Contract Renewal Date", "contractNotesInternalOnly": "Contract Notes (Internal Only)", "contractReference": "Tenancy Reference", "contractRemindersSuccessfullyUpdated": "Contract reminders successfully updated", "contractReviewDate": "Contract Review Date", "contractStartDate": "Contract Start Date", "contractStatus": "Contract Status", "contractStatusIsActiveOrPeriodic": "Contract status is “Active” or Periodic", "contractTags": "Contract Tags", "contractTitle": "Contract Title", "contractType": "Contract Type", "contractTypes": "Contract Types", "contractValue": "Contract Value", "contracts": "Contracts", "controlTheNotificationsYouReceiveToYourEmail": "Control the notifications you receive to your email.", "conversationAssignedTo": "Conversation Assigned to", "copyLink": "Copy Link", "copied": "<PERSON>pied", "copyFailed": "Co<PERSON> failed", "councilTax": "Council Tax", "create": "Create", "createABill": "Create a bill", "createABrandNewWorkspaceForYouYourTeamAndYouProperties": "Create a brand-new workspace <br /> for you, your team and your <br /> properties", "createADocusignAccount": "Create a DocuSign account", "createAXeroAccount": "Create a Xero account", "createAndNext": "Create & Next", "createChecklistsForProceduresUsedByYourTeamForExample": "Create checklists for procedures used by your team. For example:", "createChecklistsForProceduresUsedByYourTeamForExampleP": "Create checklists for procedures used by your team. For example: Property Inspection\\n\" + \"Viewing\\n\" + \"Tenant Reference\\n", "createGroupChat": "Create Group Chat", "createNew": "+ Create New", "createPassword": "Create password", "createReport": "Create Report", "createTask": "Create Task", "createNewTaskType": "Create New Task Type", "editTaskType": "Edit Task Type", "taskTypeSuccessfullyCreated": "Task Type successfully created", "tastTypeSuccessfullyUpdated": "Task Type successfully updated", "createWorkspace": "CREATE WORKSPACE", "currency": "<PERSON><PERSON><PERSON><PERSON>", "currentAddress": "Current Address", "currentStatus": "Current Status", "dailyUpdates": "Daily Updates:", "dailyUpdates1": "Daily Updates", "date": "Date", "DATE_FORMAT_LONG": "DD MMM YYYY", "date-0": "Date ...", "dateAdded": "Date added", "dateIsRequired": "Date is required", "dateOfBirth": "Date of Birth", "dateOfBirthday": "Date of Birthday", "dateRegistered": "Date Registered", "dateValidateMessage": "The date must be in the past", "dates": "Dates", "days": "Days", "daysInArrears": "Days in Arrears", "daysAfterRent": "Days after rent is due", "deadline": "Deadline", "debit": "Debit", "defaultInvoiceSettings": "Default Invoice Settings", "defaultManagementFee": "Default Management Fee", "defineYourLease": "Define Your Lease", "delete": "Delete", "deleteApplication": "Are you sure to delete this application?", "deleteApplicationYes": "Yes, Delete it", "deleteAttachment": "Delete attachment", "deleteChecklist": "Delete Checklist", "deleteColumn": "Delete column", "deleteContact": "Delete contact", "deleteContact-0": "Delete Contact", "deleteContact1": "Delete contact?", "deleteContract": "Delete Contract?", "deleteContract-0": "Delete Contract", "deleteContract1": "Delete contract", "deleteDocument": "Delete Document?", "deleteDocumentfordeleteName": "Delete \"{{documentForDelete}}\" ?", "deleteFilename": "Delete \"{{fileName}}\" ?", "deletePhoto": "Delete photo", "deletePhotosSection": "Delete photos section", "deleteProperty": "Delete Property?", "deleteProperty1": "Delete Property", "deleteTask": "Delete task", "deleteTask1": "Delete Task", "deleteTeamLandlord": "Delete Team Landlord?", "deleteTenant": "Delete Tenant?", "deleteThisManager": "Delete this Manager?", "deleteTitle": "Delete {{title}}?", "demoAndCo": "Demo & Co", "dependantInformation": "Dependant Information", "dependants": "Dependants", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "deposits": "Deposits", "depositAmount": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "detailedDescription": "Detailed Description", "detailedListOfAllActiveTenancyContractsByProperty": "Detailed list of all active tenancy contracts by property", "details": "Details", "direct": "Direct", "directChat": "Direct chat", "disable": "Disable", "disabled": "Disabled", "disconnect": "Disconnect", "divorced": "Divorced", "doYouChargeLateFees": "Do you charge late fees for unpaid rent?", "doYouHaveAnyDependants": "Do you have any Dependants?", "doYouHaveAnyVehicles": "Do you have any vehicles?", "doYouHaveSnyPets": "Do you have any pets?", "document": "Document", "documentDetails": "Document Details", "documentExpiryDate": "Document Expiry Date", "documentSentSuccessfully": "Document sent successfully!", "documentTemplates": "Document templates", "documentUpdatedSuccessfully": "Document updated successfully!", "documents": "Documents", "documentsRemindersSuccessfullyUpdated": "Documents reminders successfully updated", "documentsReport": "Documents Report", "docusign": "DocuSign", "docusignIntegrationRequest": "Docusign Integration Request", "done": "Done", "download": "Download", "downloading": "Downloading", "downloadComplete": "Download Complete", "downloadWasCanceled": "Download was canceled", "downloadCsvFile": "Download CSV File", "downloadDraftVersion": "Download draft version", "downloadPdfVersion": "Download PDF version", "downloadType": "Download {{type}}", "driversLicenseNumber": "Drivers License Number", "driversLicenseState": "Drivers License State", "feedbackSuccessfully": "Your feedback has been received! We value your opinion.", "skipSuccessfully": "Skipped. You can always take action later.", "addFileSuccessfully": "File Added", "due": "Due", "dueDate1": "Due date...", "dueDateIsRequired": "Due date is required", "dueDateTime": "Due Date/ Time", "dueDay": "Due Day", "dueToClient": "Due to Client", "edit": "Edit", "editFile": "Edit File", "editQuestion": "Edit Question", "editClientBillAmount": "Edit Client <PERSON>", "editContact": "Edit Contact", "editDetails": "Edit Details", "editList": "Edit list", "editList1": "Edit List", "editProblemReport": "Edit Problem Report", "editProfile": "Edit profile", "editTheAmount": "Edit the amount", "editToDate": "Edit to date", "email": "Email", "emailAddress": "Email Address", "emailInvoices": "Email Invoices", "emailIsMissing": "<PERSON><PERSON> is missing", "emailIsRequired": "Email is required", "emailIsInvalid": "<PERSON><PERSON> is invalid", "emailMessage": "Email message", "emailNotifications": "Email Notifications", "emailStatements": "Email Statements", "emergencyContacts": "Emergency Contacts", "emergencyContactsInformation": "Emergency Contacts Information", "emergencyLine": "Emergency line", "employed": "Employed", "employment": "Employment", "employmentInfo": "Employment Info", "employmentInformation": "Employment Information", "enable": "Enable", "end": "End", "endDateMustBeAfterStartDate": "End Date must be after Start Date", "ends": "Ends", "enter": "Enter", "enterAnAddressToBeIncludedOnStatementsAndInvoices": "Enter an address to be included on statements and invoices.", "enterDetailsHere": "Enter details here", "enterEmail": "Enter email", "enterNumber": "Enter Number", "enterMonthNumber": "Enter Months Number", "enterYearNumber": "Enter Years Number", "enterName": "Enter name", "enterNumbers": "Enter Numbers", "enterPhoneNumber": "Enter phone number", "enterValidNumber": "Enter valid number", "enteringATextStringWillDisplayAsTextStringEnteredStart": "Entering a text string will display as: [Text string entered], [start period] - [end period].", "enterYourContactDetails": "Enter your contact details, and we'll let the rental manager know you want to submit an application. If they're interested, they'll contact you with next steps.", "epcAndTheEnvironment": "EPC & The Environment", "epcRating": "EPC Rating", "createdDate": "Created Date", "errorOnUploadingItemNameFile": "Error on uploading {{name}} file!", "exchangeDate": "Exchange Date", "exclusiveVat": "Exclusive VAT", "exemptionCert": "Exemption Cert", "exemptionCertificateNo": "Exemption Certificate No", "exemptionDate": "Exemption Date", "expectedMoveIn": "Expected Move-in", "expensesOutstanding": "Expenses Outstanding", "expensesPropertyFinance": "Expenses - Property - Finance", "expires": "Expires", "expiredDate": "Expired Date", "export": "Export", "every": "Every", "failedToConnect": "Failed to Connect", "fee": "Fee", "female": "Female", "fields": "Fields", "file": "File", "fileName": "File Name", "fileName0": "File Name...", "fileName1": "File name...", "fileUpdatedSuccessfully": "File updated successfully!", "files": "Files", "filter": "Filter", "filterByProperty": "Filter by Property", "filterByAssignee": "Filter by <PERSON><PERSON><PERSON>", "filterByDueDate": "Filter by Due Date", "filterInArrears": "Filter in Arrears", "finalizedBy": "Finalized by", "finance": "Finance", "finish": "Finish", "firstName": "First Name", "firstNameIsRequired": "First Name is required", "firstReminder": "First Reminder", "firstTakeAPhotoOfTheProblemYouWantToReport": "First, take a photo of the problem you want to report", "float": "Float", "forAdminsOnlyClickTemplateForEachDataSetToDownloadAS": "For Admins only. Click ‘template’ for each data set to download a spreadsheet which can be imported.", "forAdminsOnlyExportPropertiesContractsAndContactsAsThe": "For Admins only. Export Properties, Contracts and Contacts as the core data from LoftyWorks.", "forClientAccountingGenerateACashBalanceAndAllocationRep": "For client accounting: generate a cash balance and allocation report", "forInternalUseOnly": "For Internal Use Only", "forThePeriod": "For the Period", "forTheTemplateSpreadsheet": "for the template spreadsheet.", "forgotMyPassword": "Forgot my password", "forgotPassword": "Forgot Password", "forgottenPasswordRentancy": "Forgotten Password - LoftyWorks", "forwardingInbox": "Forwarding inbox", "from": "From", "fromDate": "From Date", "fromIsRequired": "From is required", "fromTo": "From/To", "fullName": "Full Name", "fullProfile": "Full Profile", "fundClient": "Fund Client", "fundSuppliers": "Fund suppliers", "furnitureFurnished": "Furniture Furnished", "furtherDataCanBeExportedDirectlyFromYourFinancialGenera": "Further data can be exported directly from your financial general ledger.", "gender": "Gender", "generalInfo": "General Info", "generate": "Generate", "generatePdf": "Generate PDF", "generateReport": "Generate Report", "giveYourNewLessThanBrGreaterThanWorkspaceAName": "Give your new <br /> workspace a name", "global": "Global", "globalCurrency": "Global Currency", "goTo": "Go to", "group": "Group", "groupInfo": "Group Info", "guarantor": "Guarant<PERSON>", "guarantor1": "GUARANTOR", "guarantors": "Guarantors", "healthAndSafety": "Health & Safety", "held": "Held", "home": "Home", "homeAddress": "Home Address", "howMuchCharge": "How much would you like to charge", "howMuchShouldCharge": "How much should we charge?", "howOftenChargeRent": "How often do you charge rent?", "howToUse": "How to use:", "hyperlink": "Hyperlink", "iConfirmThatIHaveReadTheRelevantAdviceProvidedInTheP": "I confirm that I have read the relevant advice provided in the PDF below", "iCurrentlyWorkHere": "I currently work here", "iCurrentlyWorkStudyHere": "I currently work/study here", "iHaveCoApplicants": "I have co-applicants", "iOwnThisProperty": "I own this property", "iRentThisProperty": "I rent this property", "identification": "Identification", "ifYouAreVatRegisteredEnterYourVatCodeHere": "If you are VAT  registered, enter your VAT code here.", "ifYourWorkspaceIsSetToAgentSpecifyYourManagementFeeYo": "If your workspace is set to Agent specify your management fee you charge for contracts.", "imApplyingAlone": "I’m applying alone", "import": "Import", "importExport": "Import / Export", "importingDataWillAppendNewRecordsToThisWorkspaceAndCan": "Importing data will append new records to this workspace and cannot be undone.", "inArrears": "In Arrears", "inCaseOfEmergency": "In case of emergency", "inactive": "Inactive", "inbox": "Inbox", "inboxEmail": "Inbox Email", "includeTheWorkspaceWebsiteIncludingHttpsAndTheLogoInTh": "Include the workspace website including https:// and the logo in the top left corner will hyperlink to the address", "inclusiveVat": "Inclusive VAT", "includes": "Includes", "income": "Income", "incomeOutstanding": "Income Outstanding", "incomeRaisedNotPaid": "Income Raised Not Paid", "integrations": "Integrations", "internal": "Internal", "internalNotes": "Internal Notes", "internalOnly": "Internal Only", "invalidDate": "Invalid date", "invalidEmailAddress": "Invalid email address", "invitationPending": "Invitation pending", "invite": "Invite", "inviteTeamMember": "Invite Team Member", "inviteTo": "Invite to", "inviteToWorkspace": "Invite to Workspace", "invoiceDate": "Invoice Date", "invoiceGeneratedSuccessfully": "Invoice generated successfully!", "invoiceGenerationTime": "Invoice Generation Time", "invoiceInAdvance": "Invoice in Advance", "invoiceLineItem": "Invoice Line Item", "invoiceReference": "Invoice Reference", "invoiceRentInAdvanceDaysErrorMessage": "Please note that the invoice due date and send date need to be in the future. This is to prevent an invoice period being missed", "invoiceTemplates": "Invoice Templates", "invoices": "Invoices", "invoicesCount": "Invoices Count", "invoicesFinanceRentancy": "Invoices - Finance - LoftyWorks", "invoicing": "Invoicing", "isServicePet": "Is Service Pet", "itIsNotPossibleToEditYourEmailAddressPleaseContactRen": "It is not possible to edit your email address. Please contact <NAME_EMAIL>", "itemNameUploadedSuccessfully": "{{name}} uploaded successfully!", "jobDescription": "Job Description", "jobType": "Job Type", "joined": "Joined", "jumpHereIfYouAlreadyHaveAnLessThanBrGreaterThanActiv": "Jump here if you already have an <br /> active account or invite to an <br /> existing workspace.", "keyFeatures": "Key Features", "landingPage": "<PERSON>", "landingPageUrl": "Landing page URL", "landlord": "Landlord", "landlord1": "LANDLORD", "landlordBillSkipped": "Landlord bill skipped!", "landlordBillUpdatedSuccessfully": "Landlord bill updated successfully!", "landlordInformation": "Landlord Information", "landlordItemCompanyName": "Landlord - {{companyName}}", "landlordName": "Landlord Name", "landlordReport": "Landlord Report", "landlords": "Landlords", "landlordsIdTitleFnameSnameEmailsEmailTypePhonesPhoneTy": "landlords { id title fname sname emails{email type} phones{phone type} image{key} }", "lastDayOfMonth": "Last Day Of Month", "lastName": "Last name", "lastNameIsRequired": "Last Name is required", "lastSynchronizationWasOn": "Last synchronization was on", "lastUpdate": "Last Update", "lateFees": "Late Fees", "lateFeeFrequency": "Late Fee Frequency", "leave": "Leave", "leaveFieldsBlankToUseSystemDefaultsOfContractTypeConta": "Leave fields blank to use system defaults of [contract type], [contact title], [start period] - [end period]. Entering values will display as [Text entered], [start period] - [end period].", "leaveFieldsBlankToUseTheDefaultContractTypeContactTitl": "Leave fields blank to use the default: [contract type], [contact title], [start period] - [end period].", "leaveGroup": "Leave group", "ledgerCode": "Ledger Code", "ledgerCodes": "Ledger Codes", "ledgers": "Ledgers", "less": "Less", "lessThanPGreaterThanThisIsWhereYouCanManageAllOfYou": "<p> This is where you can manage all of your properties, <br /> conversations, tenancies and securely store your related documents. </p> <p>You can also invite team members, tenants, and landlords.</p>", "letterGeneratedSuccessfully": "Letter generated successfully", "letterPreview": "Letter preview", "letterTemplate": "Letter template", "licensePlate": "License Plate", "lineItems": "Line Items", "link": "Link", "unLink": "Unlink", "linkPropertyOrContract": "Link Property or Contract…", "linkToClipboard": "Link copied to clipboard.", "linkToProblemReport": "Link to Problem Report...", "links": "Links", "list": "List", "listView": "List View", "listingStatus": "Listing Status", "listingSyndication": "Listing Syndication", "logIn": "<PERSON><PERSON>", "loginRightmoveAccount": "Login Rightmove account, go to settings, click Connect My CRM and enter the code below.", "loginZooplaAccount": "Login Zoopla account, go to settings, click Connect My CRM and enter the code below.", "logInLofty": "Log In - Lofty Works", "longTerm": "Long Term", "toRentancy": "to LoftyWorks", "noNotificationsRightNow": "No notifications right now", "youWillSeeUpdatesToYourPropertyTasksAndConversationsHe": "You will see updates to your property, tasks and conversations here", "youCurrentlyHaveNoActions": "You currently have no actions", "youCurrentlyHaveNoUpdates": "You currently have no updates", "youCurrentlyHaveNoChats": "You currently have no chats", "notifications": "Notifications", "markAllAsRead": "Mark all as read", "more1": "+ More", "viewMore": "View More", "showPast": "+ Show Past", "noActivity": "No activity…", "propertyComplianceDescription": "Add safety checks, certificates, insurances, and warranties.", "propertyReference": "Property Reference", "propertyProfit": "Property Profit", "totalArea": "Total Area", "userSuccessfullyAddedToTheProperty": "User successfully added to the property", "userSuccessfullyUpdatedToTheProperty": "User successfully updated to the property", "userSuccessfullyAddedToTheContract": "User successfully added to the contract", "teamMember": "Team member", "logo": "Logo", "mainContact": "Main contact", "mainImage": "Main Image", "make": "Make", "makeSureItHasAtLeast_8CharactersWeSuggestLessThanBrG": "Make sure it has at least 8 characters, we suggest using upper and lower case, as well as numbers and special symbols.", "male": "Male", "manageDocumentTemplatesHere": "Manage document templates here", "manageWorkspaces": "Manage Workspaces", "deleteWorkspace": "Delete Workspace", "manageYourInvoicesExpensesAndLessThanBrGreaterThanChec": "Manage your invoices, expenses and check bank transactions here.", "managementFee": "Management Fee", "manager": "Manager", "managers": "Managers", "mapLedgerCode": "Map Ledger Code", "maritalStatus": "Marital Status", "markAsResolved": "<PERSON> as Resolved", "marketingDetails": "Marketing Details", "married": "Married", "maxFileSizeMaxsize_1000000Mb": "Max file size: {{maxSize}}Mb.", "menu": "<PERSON><PERSON>", "message": "Message", "method": "Method", "middleName": "Middle Name", "mine": "Mine", "minimumBalance": "Minimum Balance", "model": "Model", "month": "Month", "monthlyPayment": "Monthly Payment", "monthlyRent": "Monthly Rent", "months": "months", "monthsRemaining": "Months Remaining", "more": "More", "moveInDate": "Move-in Date", "moveInMoveOut": "Move-in/Move-out", "mute": "Mute", "myPetIsSpayedNeutered": "My pet is spayed/neutered ", "myWorkspaces": "My Workspaces", "name": "Name", "nameEmailOrPhone": "Name, Email, Phone...", "nameOrEmail": "Name or email", "nameYourWorkspaceWithAUniqueNameThatRepresentsYourComp": "Name your workspace with a unique name that represents your company or organization.", "loftyworksGeneratesYourWorkspaceIDByRemovingSpacesFromYourWorkspaceName": "LoftyWorks generates your Workspace ID by removing spaces from your Workspace name from the first time you enter. The Workspace ID is unique for each workspace in Rentancy, it remains fixed and cannot be changed.", "netIncomeInPeriod": "Net Income in Period", "neverMarried": "Never married", "new": "New", "newApplication": "New Application", "newApplications": "New Applications", "newName": "New Name", "newPassword": "New Password", "newProspect": "Add New Prospect", "newWorkspace": "New Workspace", "newest": "Newest", "next": "Next", "next1": "NEXT", "nextDays": "Next {{item}} days", "nextRentInvoiceAmount": "Next Rent Invoice Amount", "nextRentInvoiceDueDate": "Next Rent Invoice Due Date", "nextRentInvoiceSendDate": "Next Rent Invoice Send Date", "no": "No", "noApplications": "No Applications", "noAttachmentsYet": "No attachments yet…", "noContractYet": "No Contract yet…", "noDataListed": "No data listed", "noDocumentsYet": "No documents yet…", "noFee": "No Fee", "noInvoiceFoundForThisSupplier": "No invoice found for this supplier", "noOptionClickHereToCreateYourFirstChecklistInSettings": "No option, Click here to create your first Checklist in Settings!", "noRecords": "No records…", "noResultsFor": "No results for", "nonResident": "Non Resident", "none": "None", "notSet": "Not Set", "noteClientsAndTenantsCanOnlyAccessALimitedSubsetOfThe": "Note: clients and tenants can only access a limited subset of the data contained in LoftyWorks related to the properties and contracts they are connected.", "notes": "Notes", "nothingFound": "Nothing Found", "noticePeriod": "Notice Period", "notificationSuccessfullyUpdated": "Notification successfully updated", "nrlSummaryReport": "NRL Summary Report", "nrlTax": "NRL Tax", "nrl": "NRL", "nonResidentLandlord": "Non-Resident Landlord", "approvalNumberObtained": "Approval Number Obtained", "approvalNumber": "Approval Number", "approvalEffectiveDate": "Approval Effective Date", "approvalWithdrawalDate": "A<PERSON><PERSON><PERSON> Withdrawal Date", "enabled": "Enabled", "notEnabled": "Not Enabled", "number": "Number", "numberRefOrTo": "Number, Ref or To...", "ok": "OK", "oldest": "Oldest", "once": "Once", "onceSavedTheSystemWillAutomaticallySendAnEmailToThisA": "Once saved, the system will automatically send an email to this address (from Amazon Web Services). Click the link in the email and verify usage.", "onceSetThisWillApplyAsDefaultToNewInvoices": "Once set this will apply as default to new invoices.", "onceSetWeDoNotRecommendChanging": "Once set, we do not recommend changing.", "onceTheyHaveAcceptedTheInviteTheirStatusWillChangeToA": "Once they have accepted the invite, their status will change to a green tick.", "onceVerifiedTheSystemWillStartSendingEmailUsingThisAdd": "Once verified the system will start sending email using this address as the from address.", "onlyThoseTeamMembersAssignedTheSupportUserRoleWillBeI": "Only those team members assigned the Support user role will be included on new emails sent inbound.", "openPdf": "Open PDF", "openTasks": "Open Tasks", "opening": "Opening", "openingBalance": "Opening Balance", "openingBalances": "Opening Balances", "orUseTheLinkInTheInvitationEmail": "or use the link in the invitation email.", "orderBy": "Order by", "orderChangedSuccessfully": "Order changed successfully!", "orderType": "Order type", "organisation": "Organisation", "other": "Other", "others": "Others", "otherNotes": "Other Notes", "overallocated": "Overallocated", "owedDueToLandlord": "Owed/Due to <PERSON>lord", "pageNotFound": "Page Not Found", "paid": "Paid", "paidDeposit": "<PERSON><PERSON>", "paidIncome": "Paid <PERSON>", "paidThisMonth": "Paid This Month", "paidTotal": "Paid Total", "password": "Password", "passwordIsRequired": "Password is required", "phoneIsRequired": "Phone is required", "passwordMustBeAtLeast_8Characters": "Password must be at least 8 characters", "passwordUpdated": "Password Updated", "payOut": "Pay out", "payments": "Payments", "payout": "Payout", "payoutsCanBeProcessed": "Payouts can be processed", "pendingSince": "Pending since", "people": "People", "peopleRentancy": "People - LoftyWorks", "percentage": "Percentage (%)", "period": "Period", "petInformation": "Pet Information", "petName": "Pet Name", "pets": "Pets", "phoneNumber": "Phone Number", "phoneNumberIsRequired": "Phone number is required", "photos": "Photos", "photosVideo": "Photos & Videos", "pickYourEmoji": "Pick your emoji…", "planned": "Planned", "planned-0": "Planned", "pleaseAddEmailToInvite": "Please add email to invite", "pleaseAddWhatsappDetailsToContact": "Please add Whatsapp details to contact", "pleaseCheckYourEmail": "Please check your email.", "pleaseContactYourWorkspaceAdminToSetupXeroConnection": "Please contact your workspace admin to setup Xero connection.", "pleaseDoubleCheckYourEmail": "Please double check your email.", "pleaseEnterCorrectEmail": "Please enter correct email!", "pleaseEnterCorrectPhone": "Please enter correct phone!", "pleaseEnterCorrectUrl": "Please enter correct url!", "pleaseEnterEmailAddress": "Please enter email address", "pleaseEnterName": "Please enter name", "pleaseEnterTheCorrectEmailAddress": "please enter the correct email address", "pleaseSelectAChatToGetStartedLessThanBrGreaterThanOr": "Please select a chat to get started, <br /> or create a new one.", "pleaseSelectAFile": "Please select a file!", "pleaseSelectASupplier": "Please select a supplier", "pleaseSelectAccount": "Please select account!", "pleaseSelectPropertyOrContract": "Please select property or contract", "pleaseSelectTheColumn": "Please select the column", "pleaseSelectTheRole": "Please select the role:", "pleaseSetupYourXeroConnectionToBegin": "Please setup your Xero connection to begin.", "pleaseSignThisDocument": "Please sign this document", "pleaseTryDifferentSearchTerms": "Please try different search terms", "pleaseUploadTheSignedLease": "Please upload the signed lease", "pleaseTryAgainShortly": "Please try again shortly", "portal": "Portal(Landlord or Tenant)", "portalHomepage": "Portal Homepage", "portfolioDataReportXls": "Portfolio Data Report XLS", "portfolioDeletedSuccessfully": "portfolio deleted successfully", "post": "Post", "postalAddress": "Postal Address", "postcode": "Post Code", "prefStartDate": "Pref Start Date", "preferredStartDate": "Preferred start date:", "preferredTemplate": "Preferred Template", "preferredTemplateUpdatedSuccessfully": "Preferred Template updated successfully!", "price": "Price", "primaryContact": "Primary contact", "primaryLandlord": "Primary Landlord", "primaryLandlordSuccessfullyChangedToLandlordCompanyName": "Primary landlord successfully changed to {{companyName}}", "primaryManager": "Primary Manager", "primaryPhone": "Primary Phone", "primaryTenant": "Primary Tenant", "primaryTenant1": "Primary tenant", "primaryTenantChangedSuccessfully": "Primary tenant changed successfully!", "primarytenantIdTitleFnameSnameEmailsEmailTypePhonesPhon": "primaryTenant { id title fname sname emails{email type} phones{phone type} image{key} }", "print": "Print", "problemReportSuccessfullyUpdated": "Problem Report successfully updated", "problemReports": "Problem Reports", "profile": "Profile", "profileImageSuccessfullyChanges": "Profile image successfully changes", "profileImageSuccessfullyUpdated": "Profile image successfully updated", "profit": "Profit", "properties": "Properties", "property": "Property", "property1": "Property ...", "propertyAddressline1CityPostcode": "property { addressLine1 city postcode }", "propertyBalancesReport": "Property Balances Report", "propertyContractOrClientName": "Property, Contract or Client Name…", "propertyContracts": "Property & Contracts ({{length}})", "propertyCount": "Property Count", "propertyCreatedSuccessfully": "Property created successfully!", "propertyDeletedSuccessfully": "Property deleted successfully", "propertyFloat": "Property Float", "propertyIsRequired": "Property is required!", "propertyLease": "Property & Lease", "propertyLeaseTerm": "Property & Lease Term", "propsTitleImportedSuccessfully": "{{title}} imported successfully.", "protectionScheme": "Protection scheme", "providesConsolidatedListOfAllDocumentsLinkedToYourPrope": "Provides consolidated list of all documents linked to your properties along with their expiration dates.", "providerName": "Provider Name", "published": "Published", "qty": "Qty", "qtyIsRequired": "Qty is required", "rate": "Rate", "rateIsRequired": "Rate is required", "readyToApply": "Ready To Apply", "reasonForLeaving": "Reason for leaving", "receiveThePdfVersionOfTheStatementInYourMailbox": "Receive the PDF version of the statement in your mailbox", "received": "Received", "receivers": "Receivers", "reconciled": "RECONCILED", "reconnect": "Reconnect", "ref": "Ref", "reference": "Reference", "refreshTable": "Refresh table", "relationship": "Relationship", "remainingApiCallsAre": "Remaining API calls are", "reminders": "Reminders", "remittedAmount": "Remitted Amount", "remove": "Remove", "removeAttachment": "Remove attachment", "removeChecklist": "Remove Checklist", "removeChecklist1": "Remove checklist", "removeCodeMapping": "Remove Code Mapping", "removeExpiry": "Remove Expiry", "removeFromGroup": "Remove from group", "removeUnit": "Remove Unit", "removeUser": "Remove user", "removeWholeSection": "Remove whole section", "rename": "<PERSON><PERSON>", "renameColumn": "Rename column", "renameDocument": "Rename Document", "renameGroup": "Rename group", "renewalDate": "Renewal Date", "rent": "Rent", "rentAmount": "Rent Amount", "paymentSchedule": "Payment Schedule", "recurringInvoices": "Set-up Recurring Invoice", "rentAmountIsSpecified": "Rent amount is specified", "rentDay": "Rent day", "rentFrequency": "Rent Frequency", "rentIncludes": "Rent Includes", "rentInvoiceDay": "Rent Invoice Day", "rentReview": "Rent Review", "rentalApplications": "Rental Applications", "loftyWorks": "LoftyWorks", "rentancyLetsYouApplyDifferentInvoiceTemplatesToDifferent": "LoftyWorks lets you apply different invoice templates to different contract types.", "rentancyPeriodicallyIncreasesTheFormatsOfStatementsOffere": "LoftyWorks periodically increases the formats of statements offered. Select a type of statement and press save.", "rentancyProvidesAPublicHomepageWhichYouCanIntegrateWith": "LoftyWorks provides a public homepage which you can integrate with your website.", "rentancyStandard": "LoftyWorks Standard", "rentancyUsesXeroTrackingCategoriesToTrackInvoicesAndBil": "LoftyWorks uses Xero Tracking Categories to track invoices and bills by property and contract.", "rentancyWillReadEmailsSentToThisAddressAndIncludeThem": "LoftyWorks will read emails sent to this address and include them in the Inbox.", "rentancyWillSendAnyUnreadMessages_10MinutesAfterTheyHav": "LoftyWorks will send any unread messages 10 minutes after they have been sent.", "reportAProblem": "Report a Problem", "reportProblem": "Report problem", "request": "Request", "requestHasBeenSentToYourEmail": "Request has been sent to your email", "requestSetupByOurAdmins": "Request setup by our admins", "required": "Required", "requiredField": "Required field", "resendApplicationDoubleRequest": "This request has already been sent, and you cannot send a double request.", "resendApplicationRequest": "Are you sure to send this application request again?", "resendApplicationRequestYes": "Yes, re-send it", "resendCode": "Resend code", "resendInvite": "Resend Invite", "resendInvite1": "Resend invite", "sendInvitation": "Send Invitation", "resendRequest": "Re-send request", "reservation": "Reservation", "reset": "Reset", "residence": "Residence", "residenceMonthlyPayment": "Residence Monthly Payment", "residentialHistory": "Residential History", "resolve": "Resolve", "resolved": "Resolved", "unresolved": "Unresolved", "restore": "Rest<PERSON>", "resultsFor": "results for", "retrievingDataForNewConnectionItCanTakeSeveralMinutes": "Retrieving data for new connection, it can take several minutes…", "returnToListingDetail": "Return to listing detail", "returnBtn": "Return", "reviewDate": "Review Date", "reviewNotice": "Review Notice", "requestATour": "Request a tour", "requestToApply": "Request to Apply", "requestToTour": "Request a tour", "roleIsRequired": "Role is required", "rolesCanBeAssignedToTeamMembersOnlyAfterTheyHaveCreat": "Roles can be assigned to team members only after they have created their user account.", "room": "Room", "salesFee": "Sales Fee", "save": "Save", "saveAndNext": "Save and Next", "savedSuccessfully": "Saved successfully!", "scheme": "Scheme", "school": "School", "screeningReports": "Screening Reports", "search": "Search", "searchByNameEmail": "Search by name, email", "secondReminder": "Second Reminder", "secrityDeposit": "Secrity Deposit", "seeMore": "See More", "select": "Select", "select-0": "Select", "select1": "Select", "selectATemplateThenContactsForSigningWillAppearHere": "Select a template, then contacts for signing will appear here.", "selectApplicant": "Please select applicant that you want to send the application to", "selectProspect": "Please select prospect that you want to send the application to", "selectCategory": "Select category", "selectCountry": "Select Country", "selectDate": "Select Date", "selectFile": "Select file…", "selectThisOnlyTheFirstTimeYouUseRentancyWeDoNotRecom": "Select this only the first time you use LoftyWorks. We do not recommend changing this setting after you have started using LoftyWorks.", "selectType": "Select Type", "selectType1": "Select type", "selectTypeAndTimeframe": "Select type, user and timeframe to create a ladger", "selectedTenantsDontHaveEmail": "Please select tenants with email specified", "selectedTenantsDontHavePhone": "Please select tenants with phone number specified", "selfEmployed": "Self Employed", "send": "Send", "sendEmail": "Send Email", "sendInvite": "Send Invite", "sendLetter": "Send Letter", "sendReportByEmail": "Send Report By Email", "sendRequest": "Send Request", "sendSMS": "Send SMS", "sendToDocusign": "Send to Docusign", "sendToDocusign1": "Send to DocuSign", "separated": "Separated", "service": "Service", "setTheCurrencyFlagToMatchYourFinancialLedgerThatRentan": "Set the currency flag to match your financial ledger that LoftyWorks connects to.", "settingsSuccessfullyUpdated": "Settings successfully updated", "setupUploadedDocuments": "Setup uploaded documents:", "share": "Share", "shared": "Shared", "shareLink": "Share Link", "sharedWith": "Shared With", "sharing": "Sharing", "sharePropertyTo": "Share Property to", "shortTerm": "Short Term", "eitherTerm": "Either", "showBillsWithWarning": "Show bills with warning", "showOnMap": "Show on Map", "signOut": "Sign out", "signUp": "Sign Up", "simpleTable": "simple table", "since": "since", "sizeSqft": "Size (Sqft)", "skip": "<PERSON><PERSON>", "skipForNow": "Skip for now", "skipIntro": "SKIP INTRO", "skipThisStep": "Skip this step", "somethingIsWrongWithYourDocusignIntegrationStatus": "Something is wrong with your DocuSign integration status", "somethingIsWrongWithYourWhatsappIntegrationStatus": "Something is wrong with your whatsapp integration status", "somethingIsWrongWithYourXeroIntegrationStatus": "Something is wrong with your Xero integration status", "somethingWentWrong": "Something went wrong", "somethingWentWrongPleaseTryAgainLater": "Something went wrong. Please try again later", "somethingsLessThanBrGreaterThanMissing": "Something’s missing…", "sorrySomethingWentWrong": "Sorry, something went wrong!", "sort": "Sort", "sortCode": "Sort Code", "source": "Source", "specificContract": "Specific Contract", "specifyIfYouWouldLikeAnInvoiceToBeGeneratedANumberOf": "Specify if you would like an invoice to be generated a number of days before it becomes due.", "spent": "Spent", "ssnOrItiN": "SSN or ITIN", "start": "Start", "startAConversation": "Start a Conversation", "startDateIsCompleted": "Start date is completed", "startFrom": "Start from", "startFrom-0": "Start From", "starts": "Starts", "state": "State", "statement": "Statement", "statementSentToYourEmailPleaseCheck": "Statement sent to your email, please check", "statementToDate": "Statement To Date :", "statements": "Statements", "status": "Status", "step_1CreateAWordDocumentIncludingFieldsYouWantToMerge": "Step 1: create a word document including fields you want to merge from LoftyWorks.", "step_2UploadHereMarkApprovedIfFinalVersion": "Step 2: upload here. <PERSON> approved if final version.", "step_3ForManyDocumentsClickEditListAndAddTypesToGive": "Step 3: for many documents, click Edit List and add types to give groupings to your", "step_4ClickOnTheCogIconToAssignTheDocumentToAPropert": "Step 4: click on the Cog icon to assign the document to a property, contract or contact.", "step_5VisitDocumentsSectionsUnderPropertyContractOrConta": "Step 5: visit documents sections under property, contract or contact and click Generate to create a merge document that uses the templates and relevant fields.", "student": "Student", "submit": "Submit", "submitted": "Submitted", "succesfullyRemoved": "Succesfully removed!", "successfullyDisconnected": "Successfully Disconnected!", "successfullyRemoved": "Successfully removed.", "successfullyUpdated": "Successfully updated!", "summary": "Summary", "supervisionEmail": "Supervision Email", "supervisionName": "Supervision Name", "supervisionPhone": "Supervision Phone", "supervisorEmail": "Supervisor Email", "supervisorInformation": "Supervisor Information", "supervisorName": "Supervisor Name", "supervisorPhone": "Supervisor Phone", "suppliers": "Suppliers", "supportedFormats": "Supported formats", "switch": "Switch", "sync": "Sync", "synchroniseDataStartingFrom": "Synchronise data starting from", "tUnseenpropertyupdates": "{t(\"unseenPropertyUpdates\")}", "tapToAddAProblemReport": "Tap + to add a Problem Report", "task": "Task", "taskChecklists": "Task Checklists", "taskDueDate": "Task Due Date", "taskDueDateRemindersSuccessfullyUpdated": "Task due date reminders successfully updated!", "taskName": "Task name", "taskSuccessfullyArchived": "Task successfully archived", "taskSuccessfullyDeleted": "Task successfully deleted", "taskSuccessfullyRestored": "Task successfully restored", "taskSuccessfullyCreated": "Task successfully created", "taskTitle": "Task title...", "tasks": "Tasks", "tasksReport": "Tasks Report", "tax": "Tax", "taxTypeIsRequired": "Tax Type is required", "team": "Team", "teamMember1": "Team Member", "teamMembers": "Team members", "templateToUse": "Template to use:", "tenancies": "Tenancies", "tenancyLength": "Tenancy Length", "tenancyReport": "Tenancy Report", "tenant": "Tenant", "tenant1": "TENANT", "tenants": "Tenants", "tenantsLandlordsGuarantors": "Tenants, Landlords, Guarantors", "tenantYouWantToLease": "Tenant you want to lease", "term": "Term", "terms": "Terms", "termsAndConditions": "Terms & Conditions", "text": "Text", "theContactHasBeenCreated": "The contact has been created!", "theContactHasBeenUpdated": "The contact has been updated!", "theContactHasBeenDeleted": "The contact has been deleted!", "theFirstLineOfTheAddressShouldBeYourLegalCompanyOrgan": "The first line of the address should be your legal company/organization name.", "theFollowingFieldsAreAvailableToIncludeIntoTemplateDocu": "The following fields are available to include into template documents", "theInvitationHasBeenResent": "The invitation has been resent", "theLinkIsBrokenPleaseRequestANewLink": "The link is broken, please request a new link", "theLogoIsAlsoDisplayedInTheTopLeftCornerOfThisApplic": "The logo is also displayed in the top left corner of this application.", "theNameMustBeUniqueInRentancy": "The name must be unique in LoftyWorks", "theNewTypeAddedSuccessfully": "The new type added successfully.", "thePageNumberMustNotBeGreaterThanPropsCount": "The page number must not be greater than {{count}}", "thePageYourAreLookingForDoesLessThanBrGreaterThanNot": "The page your are looking for does <br /> not exist or you don’t have <br /> permissions to see it.", "theReportSentToYourEmailPleaseCheckYourEmail": "The Report sent to your email, please check your email.", "theStatementSuccessfullySent": "The statement successfully sent!", "thereIsNoPropertyOrTenancyWithThisName": "There is no property or tenancy with this name", "thereIsNoPropertyWithThisName": "There is no (parent) property with this name", "thereIsNoSuchContact": "There is no such contact", "thereIsNoResultForThisPostCode": "There is no result for this post code", "thisActionWillAlsoRemoveAllContractsLinkedToThisProper": "This action will also remove all contracts linked to this property.", "thisActionWillRemoveLandlordFromThisProperty": "This action will remove Landlord from this Property", "thisActionWillRemoveManagerFromThisProperty": "This action will remove Manager from this Property", "thisActionWillRemoveSelectedContact": "This action will remove selected contact", "thisActionWillRemoveTenantFromThisProperty": "This action will remove Tenant from this Property", "thisActionWillRemoveTitleFromThisProperty": "This action will remove {{title}} from this Property", "thisAllowsDifferentContractsToBeSetupDisplayingDifferent": "This allows different contracts to be setup displaying different lines text within an invoice.", "thisAllowsYouToMessageWithYourContactShareDocumentsAnd": "This allows you to message with your contact, share documents and contracts.", "thisAppliesToTeamMembersAddedToAContract": "This applies to Team Members added to a Contract", "inventoryManagement": "Inventory Management", "inventoryRemindersSuccessfullyUpdated": "Inventory reminders successfully updated!", "thisAppliesToTeamMembersAssignedToAPropertyOrContract": "This applies to Team Members assigned to a Property or Contract", "thisAppliesToTeamMembersAssignedToAProperty": "This applies to team members assigned to a property", "thisApplicationNotFilled": "This application hasn’t been filled out yet.", "thisAppliesToTeamMembersAssignedToATask": "This applies to Team Members assigned to a Task", "thisCanBeAOrFixedFeeAndInclusiveOrExclusiveOfVat": "This can be a % or fixed fee and inclusive or exclusive of VAT.", "thisChatDoesnAndAposTExistPleaseSelectAChatToGetSta": "This chat doesn&apos;t exist, Please select a chat to get started, <br /> or create a new one. <br />", "thisCouldBeYourBusinessNameLessThanBrGreaterThanOrTe": "This could be your business name or team name", "thisDocumentGivesPermissionToTheStatedAboveContractorTo": "This document gives permission to the stated above “Contractor” to carry out only the outlined above job. Any additional request from any other party apart from “Main Contact” are to be declined and placed forward for the approval by “Main Contact”.", "thisEmailIsNotValid": "This email is not valid.", "thisFormAllowsYouToSendDocumentsForESigning": "This form allows you to send documents for e-signing.", "thisFunctionAllowsYouToCreateMultipleContractsForThisP": "This function allows you to create multiple contracts for this property from a spreadsheet. Click", "thisIsAUniqueAddressForYourWorkspaceYouCanSetUpForwa": "This is a unique address for your workspace. You can set-up forwarding emails from say support@ or maintenance@ to this email.", "thisMessageHasBeenDeleted": "This message has been deleted.", "thisSectionAppliesToAllTeamMembers": "This section applies to All Team Members.", "thisSectionIsReadOnlyIfYouSeeAnythingThatShouldBeCha": "This section is read-only. If you see anything that should be changed or updated here, please contact your workspace admin.", "thisWillBeSentToAdminsUserRolesOnlyItWillProvideASu": "This will be sent to Admins user roles only. It will provide a summary of contract expiring, tasks expiring and new properties and contracts added in the last day.", "thisWillHelpEnsureConsistencyAcrossTeamMembers": "This will help ensure consistency across team members.", "timeline": "Timeline", "titlePosition": "Title/Position", "to": "To", "toApprove": "to approve", "toAutomaticallySynchroniseWithRentancy": "To automatically synchronise with LoftyWorks.", "toAutomaticallySynchroniseYourFinanceWithRentancy": "To automatically synchronise your finance with LoftyWorks.", "toEnableAutoInvoicingForThisContractLessThanBrGreater": "To enable auto Invoicing for this contract <br /> please check the following:", "toInviteMoreColleaguesVisit": "To invite more colleagues, visit", "toInviteMorePeopleVisit": "To invite more people, visit", "toInvitePeopleAddFromContacts": "To invite people, add from Contacts", "toPayout": "To Payout", "toViewTheStatementOpenAPropertyAndGenerateAnAdhocStat": "To view the statement, open a property and generate an adhoc statement.", "today": "Today", "tomorrow": "Tomorrow", "total": "Total", "totalIncome": "Total Income", "totalInvoice": "Total Invoice", "trackingCategories": "Tracking Categories", "trackingCategoriesUpdatedSuccessfully": "Tracking Categories updated successfully!", "tryLoftyWorksForFree": "Try LoftyWorks <br /> with your team <br /> for free", "tryLoftyWorksWithYourTeamForFree": "Try LoftyWorks <br /> with your team <br /> for free", "type": "Type", "typeFrequency": "Type frequency", "typeRemovedSuccessfully": "Type removed successfully.", "typeSomething": "Type something", "selectSomething": "Select something...", "types": "Types", "typeToSearch": "Type to search...", "unallocated": "Unallocated", "unarchive": "Unarchive", "unassigned": "Unassigned", "unemployed": "Unemployed", "unit": "Unit", "unitCreatedSuccessfully": "Unit Created Successfully!", "unitIsRequired": "Unit is Required!", "unitName": "Unit Name", "unitPrice": "Unit price", "unitPriceIsRequired": "Unit price is required", "unitRemoveSuccessfully": "Unit remove successfully!", "units": "Units", "unmute": "Unmute", "unpublished": "Unpublished", "unreadMessages": "Unread Messages", "unreconciled": "UNRECONCILED", "unresolve": "Unresolve", "unseenContractUpdates": "Unseen Contract Updates", "unseenPropertyAndContractUpdates": "Unseen Property and Contract updates.", "unseenPropertyUpdates": "Unseen Property Updates", "update": "Update", "updatedSuccessfully": "Updated successfully!", "updates": "Updates", "upload": "Upload", "uploading": "Uploading", "uploadComplete": "Upload complete", "uploadWasCanceled": "Upload was canceled", "uploadALogoForYourWorkspaceThisWillBeUsedInReportsSu": "Upload a logo for your workspace. This will be used in reports such as Property Statements.", "uploadDocument": "Upload Document", "uploadDocuments": "Upload Documents", "uploadFile": "Upload File:", "uploadLogo": "Upload logo", "uploadNew": "Upload New", "uploadNewFile": "Upload new file", "uploadedDocuments": "Uploaded Documents", "useDataForAuditPurposes": "Use data for audit purposes.", "useStandardTemplateAsDefaultOrEnterTheNameOfTheTempla": "Use Standard template as default or enter the name of the template required as setup in your General Ledger.", "useTheDisplayNameFieldToControlTheNameOfLedgerCodesI": "Use the Display Name field to control the name of ledger codes in property statement reports, if required.", "useThisPageForYourTeamClientsAndTenantsToAccessTheSy": "Use this page for your team, clients and tenants to access the system.", "usecontactMustBeInsideInContactcontext": "useContact must be inside in ContactContext", "userRoleSuccessfullyAddedFinance": "User role successfully added finance", "userRoleSuccessfullyRemovedFromFinance": "User role successfully removed from finance", "userSuccessfullyActivated": "User successfully activated", "userSuccessfullyAddedToTheContract1": "User successfully added to the contract.", "userSuccessfullyDisabled": "User successfully disabled", "userSuccessfullyRemovedFromTheProperty": "User successfully removed from the property", "userSuccessfullyDeleted": "User Successfully Deleted", "validUntil": "<PERSON>id <PERSON>", "validUntil1": "Valid until", "vat": "Vat", "vatNumber": "VAT Number", "vehicleInformation": "Vehicle Information", "vehicles": "Vehicles", "version_1": "Version 1", "view": "View", "viewAssignee": "View assignee", "viewListing": "View Listing", "warning": "Warning", "warningCheckClientBalance": "Warning Check Client Balance", "warningClientBalance": "Warning: Client Balance", "warningThisSettingChangesYourWorkspaceSetupLessThanBrG": "Warning: this setting changes your workspace setup. Do not change after initial configuration.", "weAndAposReWorkingHardInTheBackgroundToBringYouMore": "We're working hard in the background to bring you more reports. <br /> If you have any suggestions, please let us know by emailing us at", "weHaveSentAnEmailWithA_6DigitCodeLessThanBrGreaterT": "We have sent an email with a 6 digit code. \n Please now enter the code below:", "weHaveSentYouACodeTo": "We have sent a code to", "weSendtACodeTo": "We sent a code to", "weHaveSuccessfullyConnectedRentancyToYourChosenDocusign": "We have successfully connected <PERSON><PERSON><PERSON> to your chosen DocuSign account. <br /> Our automated processes are already busy in the background getting everything you need.", "weRecommendTheseAreSetToOffTurnOnIfYouWantToReceive": "We recommend these are set to Off. Turn On if you want to receive email alerts where property or contract data is being modified by yourself or other users.", "weWereUnableToSendTheVerificationCodePleaseTryAgain": "We were unable to send the verification code. Please try again.", "website": "Website", "weightIbs": "Weight (ibs)", "welcomeToLoftyWorks": "Welcome to LoftyWorks!", "whatRentCharges": "What are your rent charges?", "whatIsAWorkspace": "What is a Workspace?", "whatIsTheProblem": "What's the problem", "whatIsThePropertyAddress": "What is the property address?", "whatIsTheTermLease": "What is the term of this lease?", "whatsTheProblem": "What’s the problem?", "whatsYourDesiredMoveInDate": "What’s your desired move in date?", "whatsYourName": "What’s your name?", "whenReadyClickSendToDocusign": "When ready click Send to Docusign.", "whenDoYouWantToStartChargingRent": "When do you want to start charging rent?", "whenShouldChargeLateFee": "When should we charge this late fee?", "whereIsIt": "Where is it?", "whichKindOfPropertyDoYouManage": "Which kind of property do you manage?", "whichPropertyIsThisLeaseFor": "Which property is this lease for?", "who": "Who", "whoAreYouMovingWith": "Who are you moving in with", "whoIsTheTenant": "Who is the tenant?", "widowed": "Widowed", "willHoldingSecurityDepositLease": "Will you be holding a security deposit on this lease?", "work": "Work", "maxFileCount": "Max file count: {{maxFiles}}.", "maxFileSize": "Max file size: {{maxSize}}.", "unsupportedFileExt": "Unsupported file extension, only support: {{types}}", "workOrders": "Workorders", "workOrder": "+ Work Order", "workOrder1": "Work Order", "workspace": "Workspace", "workspaceEmail": "Workspace Email", "workspaceJournal": "Workspace Journal", "workspaceName": "Workspace Name", "workspaceId": "Workspace ID", "workspacePhone": "Workspace Phone", "workspaceType": "Workspace Type", "workspaceWebsite": "Workspace Website", "workspaceWithSuchNameAlreadyExists": "Workspace with such name already exists.", "writeAComment": "Write a comment...", "writeMessage": "Write message...", "xero": "Xero", "xeroConnectionFailed": "Xero connection failed", "xeroIsConnected": "Xero is connected", "propertyDetails": "Property Details", "photosAndVideos": "Photos And Videos", "singleFamily": "Single Family", "smoking": "Smoking", "sms": "SMS", "multiFamily": "Multi Family", "condo": "<PERSON><PERSON>", "apartment": "Apartment", "town": "Town", "townhouse": "TownHouse", "mobileHome": "Mobile Home", "propertyType": "Property Type", "residential": "Residential", "propertyAddress": "Property Address", "attachPhotosForProperty": "Attach photos for property. Minimum 1 photo required to advertise your property.", "attachFloorplanForProperty": "Attach floorplans for property.", "attachVideosForProperty": "Attach videos for property.", "addNewPropertyWhenBlank": "Add details of your property to start marketing your property on top listing site", "addDetailsWithPlus": "+ Add Details", "services": "Services", "software": "Software", "about": "About", "blog": "Blog", "LetsTalk": "Let's talk", "propertyManager": "Property Manager", "connectedSuppliers": "Connected Suppliers", "connectAsupplier": "Connect a supplier", "ConnectASupplier": "Connect a Supplier", "addPropertyManager": "Add Property Manager", "leaseDetailsFee": "Lease Details & Fee", "securityDeposit": "Security Deposit", "leaseTerms": "Lease Terms", "dateAvailable": "Date Available", "duration": "Duration", "floorplans": "Floorplans", "bedroom": "Bedroom", "bathroom": "Bathroom", "squareFeet": "Square Feet", "squareMeters": "Square Meters", "policies": "Policies", "insurance": "Insurance", "marketingInformation": "Marketing Information", "unitDetails": "Unit Details", "rentalTeams": "Rental Teams", "utilitiesAndAmenities": "Utilities and Amenities", "showingApplication": "Showing & Application", "previewPublish": "Preview & Publish", "utilitiesAmenities": "Utilities & Amenities", "utilities": "Utilities", "amenities": "Amenities", "validFrom": "<PERSON><PERSON>", "videos": "Videos", "year": "Year", "yearly": "Yearly", "generateXLS": "Generate XLS", "xlsGeneratedSuccessfully": "XLS generated and sent to your email successfully", "applicationSpecifyFees": "Specify fees for this application", "applicationAssignedTo": "Assigned To", "yes": "Yes", "youAreGoingRemoveAllActivityAndRecordsRelatedToThisCo": "You are going remove all activity and records related to this contract.", "youCanAdjustTheFeePerPropertyButThisSettingWillBeUse": "You can adjust the fee per property but this setting will be used when you create a new contract.", "youCanUseThisEmailAddressDirectlyToAutomaticallyBringE": "You can use this email address directly to automatically bring emails into LoftyWorks, however, we recommend setting up email forwarding to this address from a known address, such as &quot;<EMAIL>&quot;. If you&apos;re not familiar with setting up forwarding rules, please consult your email provider for instructions, or email us as &quot;<EMAIL>&quot; and we can help point you in the right direction.", "youCanSendTheLink": "You can send the link below or send a reminder to the applicant to fill it out.", "youCannotInviteAUserAsNewPleaseSelectOneOfTheUserTy": "You cannot invite a user as NEW. Please select one of the user types from the dropdown menu in their contact information", "youCannotSendYourselfInvites": "You cannot send yourself invites", "youCantDeleteRegisteredUser": "You can't delete registered user", "youCanAdvertiseYourProperties": "You can advertise your properties with our listing partners. Please turn on the switch for the partners you would like LoftyWorks to sync your listings with below:", "youDontHaveAccessToThisModulePleaseContactYourWorkspac": "You don’t have access to this module. Please contact your workspace admin.", "youHaveAPendingInvitationButHaveNotYetCreatedYourAcco": "You have a pending invitation, but have not yet created your account. Please", "youWillBeConnectedWithPropertiesShortly": "You will be connected with properties shortly", "yourCurrentWhatsappNumber": "Your current WhatsApp number", "yourListingIsUnderReview": "Your Listing is under review, <br /> it will be automatically published once it’s done.", "yourRequestIsCreated": "Your request is created successfully", "zipCode": "Zip Code", "title": "Title", "complete": "Complete", "fixedFee": "Fixed {{currency}}", "percentageOfFullAmount": "Percentage of Full Amount (%)", "rentChargeCreated": "Rent Charge Created", "rentChargeReceipted": "Rent Charge Receipted", "percentageOfAmount": "Percentage of Amount (%)", "fixed": "Fixed", "managementFeeIssuer": "Management Fee Issuer", "percentageOfAmountReceived": "Percentage of Amount Received (%)", "unitEditPageTitle": "Marketing Information", "submitting": "Submitting..", "saveAndPublish": "Save and Publish", "unitEditDiscardChangeTips": "Are you sure to discard all the changes?", "unitEditDiscardChange": "Yes, Ignore all changes", "generalInstructions": "General Instructions", "askTheApplicantAboutResidentialHistory": "Ask the applicant about residential history", "askTheApplicantAboutEmployment": "Ask the applicant about employment", "askTheApplicantAboutVehicles": "Ask the applicant about vehicles", "askTheApplicantAboutDependants": "Ask the applicant about dependants", "askTheApplicantAboutEmergencyContacts": "Ask the applicant about emergency contacts", "askTheApplicantAboutPets": "Ask the applicant about pets", "askTheApplicantAboutAdditionalIncome": "Ask the applicant about additional income", "askTheApplicantToUploadFiles": "Ask the applicant to upload files", "setThisSectionAsARequirementToSubmitTheApplication": "Set this section as a requirement to submit the application", "fillOutAllInformationOrContactUs": "i.e. Please fill out all the information, if you have any questions, contact us at ************", "markAsMandatory": "Mark as mandatory", "draggableAgentsReminder": "Manage Agents' order of this property unit by moving the agent's name. The first one is primary showing in applicant request tour page.", "yourQuestionLabel": "Your Question", "yourFirstAndLastName": "Your First & Last Name", "nameIsRequired": "Name is required", "enteraValidEmail": "Enter a valid email", "enteraValidPhoneNumber": "Enter a valid phone number", "pleaseAvailableDates": "Please select your available dates", "letUsKnowAndWellContactYou": "Let the property know when you're available, and the property will contact you to arrange a tour.", "selectAfewDatesForYourAvailability": "Select a few dates you're available", "sentTourRequest": "Send Tour Request", "invalidPhoneNumberLength": "Invalid phone number length", "firstDigitShouldNotBeZero": "First digit should not be 0", "thereAreAvailableFundsHeldOnTheLedger": "There are available funds of {{sum}} held on the ledger", "submittedAt": "Submitted At", "submissionSuccessful": "Submission Successful", "submissionFailed": "Submission Failed", "yourReferenceIsUnderReview": "Your reference is under review, the result will return once it’s complete.", "noApplications2": "This application hasn’t been filled out yet.", "noApplicationsSub": "You can send the link below or send a reminder to the applicant to fill it out.", "fileTitle": "File Title", "on": "On", "off": "Off", "forgotPasswordUserNotFoundErr": "We do not recognize this email address. Please try again.", "maxCharactersAreAllowed": "Maximum {{number}} characters are allowed", "referenceShouldNotContainSlash": "Reference shouldn't contain slash", "learnMore": "Learn More", "getStarted": "Get Started", "draft": "Draft", "sent": "<PERSON><PERSON>", "completed": "Completed", "retired": "Retired", "vouch": "Vouch", "day": "day", "inProgressFor": "In Progress For", "returnToEditing": "Return to editing", "documentFile": "Document File", "createdtime": "Created time", "guarantorInfo": "Guarantor Info", "postYourPropertiesTorightMoveFromRentancy": "Post your Properties to RightMove from LoftyWorks", "toAutomaticallySynchroniseWithRightMove": "To automatically synchronise with RightMove", "createARightMoveAccount": "Create a RightMove account", "branchID": "Branch ID", "pleaseContactRightMoveToRetrieveYourUniqueBranchIDAndProvideThisHere": "Please contact RightMove to retrieve your unique Branch ID and provide this here", "primary": "Primary", "propertyManagers": "Property Managers", "searchAddressCityZipcodeNeibourhood": "Search by Address, City, Zipcode, Neighborhood ...", "parentPropertyDeletedSuccessfully": "Parent Property deleted successfully", "accountingSideBar": "Accounting", "pleaseEnterValidNumber": "Please Enter Valid Number", "invalidNumber": "Invalid Number", "label": "label", "resultsEmailedtoPM": "Completed results have been emailed to the Property Manager.", "areYouSureToChangeTheApplicantStatus": "Are you sure to change the applicant status?", "previewQuestionnaire": "Preview Questionnaire", "connectVouchAccount": "Connect with your Vouch Account", "syncVouchAndRentancy": "To automatically synchronise your Tenant References between Vouch and LoftyWorks.", "vouchSignUp": "If you are not already a Vouch customer, Please contact Vouch to set-up your account and retrieve your unique Account Identifier to make use of our integration.", "vouchSomethingWentWrong": "Something is wrong with your Chime integration status", "connectSuccessful": "Connection Successful", "returnToIntegrations": "Return to Integrations", "retry": "Retry", "connectVouch": "Connect-Vouch", "contactAccountMngr": "Please contact your Account Manager to retrieve your unique Account Identifier and enter below.", "uniqueAccntIndentifier": "Unique Account Identifier", "vouchEnter": "Enter ...", "vouchDisconnect": "Vouch token has been removed successfully!", "provider": "Provider", "parentPropertiesImport": "Parent Properties", "percentageLessEqual": "percentage must be less than or equal to {{number}}", "percentageGreaterEqual": "percentage must be greater than or equal to {{number}}", "directMessage": "Direct Message", "basicInformation": "Basic Information", "financialInformation": "Financial Information", "assignTenant": "Assign Tenant", "assignGuarantor": "Assign <PERSON>", "doYouWantToSetupAutoInvoicing": "Do you want to set-up Auto Invoicing", "localCurrencySign": "£", "localDateFormat": "DD MMM YYYY", "shareToExt": "Share to External Calendar", "percentOfReceived": "{{amount}}% of Received", "percentOfFullAmount": "{{amount}}% of Full Amount", "optional": "Optional", "populateSummaryRightMove": "This will populate the Summary on RightMove", "extendedDescriptionRightMove": "This will be used in the extended Description for RightMove", "percentOfJournal": "{{amount}}% of Journaled Amount", "percentageOfJournaledAmount": "Percentage of Journaled Amount (%)", "certificateNo": "Certificate Number", "expiryDate": "Expiry Date", "expiryDates": "Expiry Dates", "enabledForThisContract": "Enabled For This Contract", "successfully": "successfully", "cardInformation": "Card Information", "cardNumber": "Card Number", "cvv": "CVV", "nameOnCard": "Name on Card", "cardNumberInvalid": "Card Number is Invalid", "cvvInvalid": "CVV is Invalid", "expiryDateInvalid": "Expiry Date is Invalid", "getStartedToday": "Get Started Today!", "weHelpAgentsAndLandlords": "We help agents and landlords with client accounting, rent collection and property management. Expert, professional and cost effective.", "newWorkspaceName": "New Workspace Name", "invitePeople": "Invite People", "inviteUpToFourPeople": "Invite up to four people", "startYourFreeTrial": "Start Your 14 Day Free Trial", "perPropertyPerMonthMinimum": "Per Property Per Month (Minimum {{ minimumAmount }} Per Month)", "previous": "Previous", "pleaseEnterYourDetails": "Please enter your details", "subscriptions": "Subscriptions", "updateCardInfo": "Update Card Info", "cancelMembership": "Cancel Membership", "extChargeDate": "Next Charge Date", "estimatedPaymentAmount": "Estimated Payment Amount", "history": "History", "basedOnNumberOfProperties": "This is based on the number of Properties in your system. We charge {{charge}} per Property in the system.", "completeStartNow": "Complete and Start Now", "cancelYourMembership": "Are you sure you want to cancel your membership", "addNewLandlord": "Add New Landlord", "pleaseEnterRecipient": "Please Enter Recipient!", "markedAlreadyExistsError": "Marked {{type}} already exists as a contact.", "contactNameExistsError": "This contact name already exists as a contact.", "cardDetailsAddedSuccessfully": "Card Details Added Successfully", "cardDetailsUpdatedSuccessfully": "Card Details Updated Successfully", "welcomeBack": "Welcome Back", "dontHaveAnAccount": "Don’t have an account?", "signUpNow": "Sign Up Now", "yourCardRequires3DS2Verification": "Your card requires 3DS2 Verification", "pleaseSelectClickHereBelowToCompleteVerification": "Please select “Click Here” below to complete verification", "paymentWillBeTakenAfter14Days": "Payment will be taken after the first 14 days. Cancel at any time. Minimum charge is £30.00 per month (£25.00 +VAT). ", "pleaseAddCardDetailsBelowPaymentWillBe": "Please add card details below. Payment will be taken after the first 14 days. Cancel at any time. Minimum charge is £30.00 per month (£25.00+VAT). ", "cardVerficiationFailed": "Card Verficiation failed. Please try again", "cardVerficiationSuccess": "Card Verficiation Success", "yourCancellationRequestHasBeenProcessed": "Your cancellation request has been processed. You will receive email confirmation shortly", "inOrderToBeChargedYourMonthlySubscription": "In order to be charged your monthly subscription, please complete 3DS2 verification on your Credit Card. ", "typeSearch": "Type a command or search", "noResultsFoundFor": "No results found for", "newNoResultsFound": "No Result Found", "problemReport": "Problem Report", "allSearchResult": "All search results", "seeAll": "See all", "loftyAPIProvidesEasySecureConnection": "LoftyWorks API provides easy and secure connection to your data. Use this API to synchronize Property, Tenancies and Contacts with 3rd party systems and applications.", "clickGenerateCreateKeyApi": "Click Generate to create a key for the api. Click Revoke to remove the key and a current API connection.", "youHaveNDaysLeftFreeTrial": "You have {{days}} days left of your free trial.", "YourMonthlySubscriptionChargeHasFailed": "Your monthly subscription charge has failed. We will attempt to charge your account tomorrow, however, by selecting \"Make Payment\" below you can make your monthly payment now", "makePayment": "Make Payment", "monthlySubscriptionUpdate": "Monthly Subscription: Update", "unknown": "Unknown", "invalid": "Invalid", "mobile": "Mobile", "landline": "Landline", "downloadInvoice": "Donwload Invoice", "sharedSuccessfully": "Shared Successfully!", "verified": "Verified", "unverified": "Unverified", "inviteYourTeam": "Invite your team", "workspaceDetail": "Workspace Detail", "studio": "Studio", "documentDates": "Document Dates", "marketingConsentMessage": "To serve you best, we would like to provide you with information about LoftyWorks by email, phone and other electronic means. We will always treat your personal details with the utmost care and will never sell them to other companies for marketing purposes.", "marketingConsentAgree": "Yes please, I am happy for you to contact me", "marketingConsentRefuse": "No thanks, I don't want to hear about services", "weProvidePropertyManagersWhatNeededToScale": "Simple to use property management software which helps you scale and excel.", "onboardingCommunicationDescriptionText": "One easy inbox for all your communications. Hold WhatsApp, emails, and conversations all in one place.", "marketyourProperties": "Market your Properties", "onboardingPropertiesDescriptionText": "Use our “one click” post to Rightmove and create online links. Share with clients and social media to maximise your leads.", "onboardingAccountingDescriptionText": "Our “one source of truth” to finance keeps your accountant happy. If you’re using Xero, don’t forget to link your account", "taskManagement": "Task Management", "onboardingTaskDescriptionText": "Our project style boards help you stay on top. Use our integrated checklists to automate your tasks", "salesCRM": "Sales CRM", "onboardingCRMDescriptionText": "Delight your customers with our Sales CRM. Schedulers and automated marketing make tenant acquisition a breeze", "logoTooltip1": "Upload a logo for your workspace. This will be used in reports such as Property Statements.", "logoTooltip2": "The logo is also displayed in the top left corner of this application.", "startNow": "Start Now!", "needHelpSettingYourAccountUpForSuccess": "Need help setting your account up for success? Access our Getting Started guide and reach out to our Customer Success team at", "accessOurGettingStartedGuide": "Access our Getting Started Guide", "viewGuide": "View Guide", "image": "Image", "video": "Video", "emoji": "<PERSON><PERSON><PERSON>", "downloadAuditHistory": "Download Audit History", "includeInPayout": "Include in Payout", "registered": "Registered", "notRegistered": "Not Registered", "transferred": "Transferred", "notTransferred": "Not Transferred", "notReceived": "Not Received", "depositToTransfer": "Deposits to Transfer", "depositToRegister": "Deposits to Register", "depositToProcess": "Deposits to Process", "inDispute": "In Dispute", "registeredSuccessfully": "Registered successfully!", "leads": "Leads", "addLead": "Add Lead", "goToLofty": "Go to Lofty", "communications": "Communications", "apply": "Apply", "lofty": "Lofty", "notStarted": "Not Started", "landlordsToPay": "Landlords To Pay", "amountOwed": "Amount Owed", "allLeads": "All Leads", "sharedInbox": "Shared Inbox", "personalInbox": "Personal Inbox", "processing": "Processing", "added": "Added", "asTheirPropertyManagerYouWillBeResponsible": "As their Property Manager, you will be responsible for keeping their account up-to-date", "invitationDescription": "By accepting this invitation, the {{type}} will gain access to log into the tenant portal. As their property manager, you will be responsible for managing their business.", "invitationSuccessMessage": "Your invitation has successfully sent to your contact by email.", "noNotesFoundWriteAFirstNote": "No notes found, write a first note", "template": "Template", "bankInfo": "Bank Info", "bankName": "Bank Name", "bankAddress": "Bank Address", "iban": "IBAN", "swift": "SWIFT", "additionalInfo": "Additional Info", "exemptionCer": "Exemption Cer", "basicInfo": "Basic Info", "postal": "Postal", "accountNumber": "Account Number", "addBasicInfo": "Add Basic Info", "addAddressInfo": "Add Address", "addBankInfo": "Add Bank Info", "addAdditionalInfo": "Add Additional Info", "editBasicInfo": "Edit Basic Info", "deleteTagWarning": "After deleting a tag, the conversations within the tag are not deleted.", "editAddress": "Edit Address", "sameHomeAddress": "Same as home address", "townCity": "Town/City", "enterAdress": "Enter address", "enterTownCity": "Enter City / Town", "enterZip": "<PERSON><PERSON>", "enterPostCode": "Enter Post Code", "enterReference": "Enter Reference", "selectService": "Select Service", "enterSize": "<PERSON><PERSON> Si<PERSON>", "enterAddress": "Enter Address", "action": "Action", "routingNumber": "Routing Number", "tags": "Tags", "enterIban": "Enter IBAN", "enterSwift": "Enter SWIFT", "addressLine1": "Address Line 1", "addressLine2": "Address Line 2", "exemptionCertificate": "Exemption Certificate", "confirmDeleteInternalNotes": "Are you sure you want to delete this Internal Notes?", "editInternalNotes": "Edit Internal Notes", "newContact": "New Contact", "financial": "Financial", "vacancyDate": "Vacancy Date", "invalidCredentials": "Invalid Credentials", "taskConfiguration": "Task Configuration", "checklists": "Checklists", "items": "Items", "createNewCheckList": "Create New Checklist", "taskTypes": "Task Types", "scheduledTask": "Scheduled Task", "automatedTask": "Automated Task", "noResultChecklists1": "Managing tasks can be a cumbersome process, requiring many steps. Our goal here is to never skip any steps or miss deadlines. ", "noResultChecklists2": " to create your first checklist template.", "addChecklistItem": "+ Add Checklist Item", "editCheckList": "Edit Checklist", "checkListRequired": "Checklist is required", "taskType": "Task Type", "myTasks": "My Tasks", "addStatus": "Add Status", "time": "Time", "taskStatusDescription": "This is the initial status when the task is created", "addWorkOrder": "Add Work Order", "taskEditCloseMessage": "If you leave this page, we won't be able to save your data.", "deleteWorkOrderMessage": "Are you sure you want to delete this Work Order? After deletion, your data will not be recoverable.", "createScheduledTask": "Create Scheduled Task", "editScheduledTask": "Edit Scheduled Task", "taskSuccessfullyUpdated": "Task successfully updated", "toCreateNewScheduledTask": "to create new scheduled task", "editDescription": "Edit Description", "event": "Event", "propertyStatusChangedTo": "Property Status Changed To", "propertyAdded": "Property Added", "contractStartsIn": "Contract Starts in", "contractStartsToday": "Contract Starts Today", "contractEndsIn": "Contract Ends in", "contractEndsToday": "Contract Ends Today", "contractCreated": "Contract Created", "documentExpiryDateIsToday": "Document expiry date is today", "documentExpiryDateIsIn": "Document expiry date is in", "editAutomatedTask": "Edit Automated Task", "workOrderUpdated": "Work Order Successfully Updated", "comments": "Comments", "scheduledTasks": "Scheduled Tasks", "automatedTasks": "Automated Tasks", "statusAlreadyExists": "Status already exists.", "propertyOrReference": "Property, Reference...", "searchByDocument": "Search by Property, File Name…", "searchEOT": "Search…", "searchApplication": "Search by Application", "templateLetters": "Template Letters", "templateLetterName": "Template Letter Name", "searchBy": "Search by...", "searchByTemplateName": "Search by template name", "chooseTemplateLetter": "Choose template letter", "subject": "Subject", "editTemplateLetter": "Edit Template Letter", "addTemplateLetter": "Add Template Letter", "letterName": "Letter Name", "insertVariable": "Insert Variable", "notAllVariablesCouldBeAddedDueToMissingData": "Some variables could not be added due to missing data", "dndOr": "Drag and drop file or", "browse": "Browse", "enterTask": "Enter Task", "taskSuccessfullyUnarchived": "Task Successfully Unarchived", "expiring": "Expiring", "valid": "<PERSON><PERSON>", "open": "Open", "scheduled": "Scheduled", "taskTitleHeader": "Task Title", "deleteTaskType": "Delete Task Type", "areYouSureYouWantToDeleteThisTaskType": "Are you sure, you want to delete this Task Type?", "taskTypeSuccessfullyDeleted": "Task Type Successfully Deleted", "failed": "Failed", "uncertain": "Uncertain", "orderNumber": "Order Number", "priority": "Priority", "contactForAccess": "Contact for Access", "generalLabel": "General", "worksManager": "Works Manager", "dateReported": "Date Reported", "problemReported": "Problem Reported", "workOrderPdfFooter": "This document gives permission to the stated above “Contractor” to carry out only the outlined above job. Any additional request from any other party apart from “Main Contact” are to be declined and placed forward for the approval by “Main Contact”.", "low": "Low", "high": "High", "medium": "Medium", "urgent": "<PERSON><PERSON>", "sharePDF": "Share PDF", "shareTo": "Share To", "preview": "Preview", "createdBy": "Created By", "chargeTo": "Charge to", "accessDetails": "Access details", "quote": "Quote", "maximumCost": "Maximum cost", "actualCost": "Actual cost", "zooplaRequired": "Please fill out required fields: ({{fields}}) ", "secondary": "Secondary", "theFloatsHaveBeenUpdated": "The floats have been updated!", "theFloatsHaventBeenUpdated": "The floats haven't been updated! Please try again!", "reportAnIssue": "Report An Issue", "templateLetter": "Template Letter", "composeNewLetter": "Compose New Letter", "portalMenu": "Portal", "templateLettersNoEmail": "Contact does not have an email address stored. Please update their email address to send Template Letters", "templateLetterNotIntegrate": "Please connect your email account with LoftyWorks via the Integrations menu in Settings", "templateLetterSuccessfullyDeleted": "Template Letter Successfully Deleted", "deleteTemplateLetter": "Delete Template Letter", "areYouSureYouWantToDeleteThisTemplateLetter": "Are you sure, you want to delete this Template Letter?", "addCategory": "Add Category", "categoryExists": "Category Exists", "logOut": "Log Out", "helpCenter": "Help Center", "switchWorkspace": "Switch Workspace", "uploadPhoto": "Upload Photo", "changePassword": "Change Password", "reportIssue": "Report Issue", "currentPassword": "Current Password", "confirmPassword": "Confirm Password", "confirmNewPassword": "Confirm New Password", "selectAnOption": "Select an Option", "issueDetails": "Issue Details", "welcomeToLandlordPortal": "Welcome to Landlord Portal", "workWithYourPropertyManager": "Work with your Property Manager to stay on top of your portfolio with the LoftyWorks Landlord Portal.", "tenantPortalTitle": "Welcome to Tenant Portal", "tenantPortalSubtitle": "Use your LoftyWorks Tenant Portal to communicate with your Property Manager, make rental payments, raise problem reports & view Documentation.", "reject": "Reject", "keyRevokedSuccessfully": "Key revoked successfully!", "billingName": "Billing Name", "general1": "General", "newWorkOrder": "New WorkOrder", "addNewSupplier": "Add New Supplier", "profileSuccessfullyUpdated": "Profile Successfully Updated", "passwordSuccessfullyChanged": "Password Successfully Changed", "portfolio": "Portfolio", "propertyStatus": "Property Status", "invalidValue": "Invalid Value", "passwordMustContainLetter": "Password must contain at least one letter", "passwordMustContainNumber": "Password must contain at least one number", "passwordMustContainSpecialCharacter": "Password must contain at least one special character", "passwordShouldBeOfMinimumLength": "Password should be of minimum 8 characters length", "newPasswordMustBeDifferent": "New password must be different from current password", "enterYourCurrentPassword": "Enter your current password", "newPasswordIsRequired": "New password is required", "passwordsDoNotMatch": "Passwords do not match", "confirmPasswordIsRequired": "Confirm password is required", "primaryTenantCantBeRemoved": "Primary Tenant Can't Be Unassigned from Contract", "inventoryDates": "Inventory Dates", "inventoryWarrantyNotificationDays": "Warranty Expiry Days", "templateLetterCreatedSuccessfully": "Template letter created successfully", "templateLetterUpdatedSuccessfully": "Template letter updated successfully", "documentUploadedSuccessfully": "Document uploaded successfully", "documentRemovedSuccessfully": "Document removed successfully", "clientEmailEmpty": "There is no e-mail assigned to the client", "certificateType": "Certificate Type", "certificateFile": "Certificate File", "contactNumber": "Contact number", "alreadyHaveAccount": "Already have a LoftyWorks account?", "createAccount": "Create Account", "yourWorkEmailAdress": "Your Work Email Address", "enterEmailToResetPassword": "Enter your email to reset password", "weHaveSendCodeTo": "We sent a code to ", "resetPassword": "Reset Password", "enterPassword": "Enter password", "privacyPolicy": "Privacy Policy", "rememberPassword": "Remember password", "freeTrialNote": "After the free trial period billing is monthly in advance. Cancel with 30 {dayNotice}. Billing is based on the number of Properties (£1.50+VAT per property per month). For full terms and conditions and pricing please click {condition} or contact our sales team {contact}.", "reports": "Reports", "login": "<PERSON><PERSON>", "startPage": "Property Management", "visitEndOfTenancy": "Visit End of Tenancy", "futureOfProperty": "The Future of Property Management", "everythingYouNeedForExceptionalProperty": "Everything you need for exceptional property management and performance", "currtentlyAvailableOnlyOnDesktop": "Currently available only on desktop. Visit our web version or book a demo today!", "emailSignature": "Email Signature", "propertyAssigned": "Property Assigned", "newTenancyCreated": "New Tenancy Created", "landlordReplied": "Landlord Replied", "newTaskAssigned": "New Task Assigned", "newIssueReported": "New Issue Reported", "complianceExpiring": "Compliance Expiring", "tenancyExpiring": "Tenancy Expiring", "applicationUpdate": "New Application Update", "applicationDesc": "New application update available. Check the latest listings now!", "tenantReplied": "Tenant Replied", "taskStatusUpdated": "Task Status Updated", "newIssueReportedDescription": "A new issue has been reported for {address}", "complianceExpiringDescription": "{complianceType} for {address} is Expiring", "tenancyExpiringDescription": "End of tenancy process is waiting to start for {address}", "tenantRepliedDescription": "Continue End of Tenancy for {address}", "newTaskAssignedDescription": "A new task, {taskTitle}, has been assigned to you", "taskStatusUpdatedDescription": "The status of task, {taskTitle} has been updated.", "newTenancyCreatedDescription": "A new tenancy has been created for {address}", "propertyAssignedDescription": "You have been assigned to {address}", "docusignIntegrationRequestDescription": "Team member just asked to use DocuSign and could use a hand getting it set up. Mind taking a look?", "somethingWentWrongUpdatingYourProfilePleaseTryAgain": "Something Went Wrong When Updating Your Profile Please Try Again", "applicant": "Applicant", "lead": "Lead", "system": "System", "tlpManager": "TLP Manager", "tlpStaff": "TLP Staff", "lettingAgent": "Letting Agent", "userRoleSuccessfullyUpdated": "User role successfully updated", "revokeSuccessfully": "Revoke successfully", "activities": "Activities", "youDon'tHaveAccessToLettings": "You do not have access to <PERSON><PERSON><PERSON>.", "pleaseEmailToIfYou'dLikeToAccessLettings": "Please <NAME_EMAIL> if you would like to access Lettings.", "syncToCRM": "Sync to CRM", "syncContactToCRM": "Sync Contact to CRM", "pleaseCompleteTheKeyInformationBelowToProceedWithTheSync": "Please complete the key information below to proceed with the sync.", "leadType": "Lead Type", "leadOwnership": "Lead Ownership", "assignmentMethod": "Assignment Method", "assignTo": "Assign To", "selectAnAgent": "Select an agent", "addNotForThisContact": "Add not for this contact", "syncContactToCRMSucceed": "Sync contact to CRM succeed!", "syncContactToCRMFailed": "Sync contact to CRM failed!", "viewInCRM": "View in CRM", "systemLog": "System Log", "agentMadeUpdatesInCRMSyncedToLettings": "Agent made updates in CRM and synced them to <PERSON><PERSON>s", "newDocumentFromCRM": "New Document From CRM", "tenancyUpdateFromCRM": "Tenancy Update From CRM", "tenancy": "Tenancy", "detail": "detail", "cancelled": "Cancelled", "ignore": "Ignore", "ignored": "Ignored", "updated": "Updated", "allDocuments": "All Documents", "noMatchedItems": "No Matched Items", "general": {"moduleTitles": {"dashboard": "Dashboard", "communication": "Communication", "communications": "Communications", "inbox": "Inbox", "property": "Property", "properties": "Properties", "contracts": "Tenancy", "calendar": "Calendar", "tasks": "Tasks", "contacts": "Contacts", "finance": "Finance", "accounting": "Accounting", "documents": "Documents", "reports": "Reports", "applications": "Applications", "settings": "Settings", "reportIssue": "Report Issue", "applicationsSubMenu": {"prospect": "Prospect", "rental": "Rental Applications"}, "parentProperty": "Parent Property", "workorders": "Workorders"}, "aidashboard": {"welcome": "👋 Welcome Back,", "message": "You have {{number}} new updates to check. Let’s get started!"}, "topBar": {"search": "Search"}, "mainMenu": {"switchToDesktop": "Use desktop version to manage your workspace"}, "sorters": {"address": "Address A-Z", "lastUpdate": "Last Update", "status": "Status"}, "actions": {"cancel": "Cancel", "create": "Create", "save": "Save"}, "address": {"address": "Address", "addressLine1": "Address 1", "addressLine2": "Address 2", "addressLine3": "Address 3", "city": "City / Town", "country": "Country", "postCode": "Post Code", "street1": "Street 1", "street2": "Street 2", "state": "State"}, "pagination": {"country": "Country", "goTo": "Go to", "postCode": "Post Code", "show": "Show"}, "property": {"client": "Client", "landlord": "Landlord", "manager": "Primary Manager", "ref": "Ref", "reference": "Reference", "service": "Service", "status": "Status", "type": "Property Type"}, "validation": {"valueMustBeNumber": "Value must be number"}}, "modules": {"endOfTenancy": {"tabs": {"all": "All", "active": "Active", "pending": "Pending", "archived": "Archived"}, "tablecolumn": {"property": "Property", "currentStage": "Current Stage", "endingIn": "Ending in", "endDate": "End Date", "reference": "Reference", "primaryTenant": "Primary Tenant"}}, "application": {"tablecolumn": {"status": "Status", "application": "Application", "movingDate": "Move-in Date", "deposit": "Holding Deposit", "rightToRent": "Right to Rent", "referencing": "Referencing", "compliances": "Compliances", "tenancyAgreement": "Tenancy Agreement", "inventory": "Inventory", "utilities": "Utilities", "moveIn": "Move In", "category": "Category", "task": "Task", "lastDate": "Last Change Date"}}, "properties": {"tabs": {"all": "All", "occupied": "Occupied", "underOffer": "Under Offer", "vacant": "Vacant"}, "columns": {"refOrAddress": "Ref or Address", "type": "Type"}, "addProperty": {"addProperty": "Add Property", "editProperty": "Edit Property", "successMessage": "Property successfully added, click to see the property", "seeProperty": "See property", "refError": "Reference already exists. Please use another", "addressError": "Address already exists. Please use another", "fields": {"addImage": "Upload image of your property", "uploadImages": "upload images"}}}, "parentProperties": {"addParentProperty": {"addParentProperty": "Add Parent Property"}, "name": "Name", "properties": "Properties", "search": "Search", "deleteProperty": "Are you sure you want to delete this Parent Property?", "deleteYes": "Yes, Delete It"}, "prospectFiles": {"dragFilesOrClickHereToUpload": "Drag files or click here to upload", "pdfDocumentsSpreadsheetsOrGraphics": ".PDF, Document (.DOC, .DOCX, .TXT, .ODT), Spreadsheet (.CSV, .XLS, .XLSX, .ODS), or Graphic (.JPG, .PNG, .GIF).", "onlyMaxFileSizeText": "Max file size"}, "workorders": {"tabs": {"active": "Active", "history": "History"}}}, "components": {"PostCode": {"searchByPostCode": "Search by postcode"}}, "parentProperty": "Parent Property", "addParentProperty": "Add Parent Property", "editParentProperty": "Edit Parent Property", "createParentProperty": "Create Parent Property", "addAssociatedProperties": "Add Associated Properties", "basic": "Basic", "country": "Country", "otherLandlord": "Other Landlord", "percentageSplit": "Percentage Split", "splitPayOutsToLandlords": "Split Pay-Outs to Landlords", "addAssociatedPropertiesWarning": "Once you link a Property to a Parent Property, the link is permanent and cannot be undone. If you are ready to proceed, click 'Submit'.", "createParentpropertySuccessfully": "Create parent property successfully", "shareDocumentSuccess": "Shared document with properties successfully", "modify": "Modify", "dataNotChange": "Data not changed", "successful": "Successful", "unsuccessful": "Unsuccessful", "addParentPropertySuccessfully": "Your Parent Property was successfully added.", "addParentPropertyUnsuccessfully": "You can only link Properties with the same Landlord. Please review the list and assign the correct properties.", "returnToMenu": "Return to Menu", "returnToEdit": "Return to Edit", "associatedProperties": "Associated Properties", "apartmentBlock": "Apartment Block", "businessPark": "Business Park", "block": "Block", "office": "Office", "sumEqual100": "Ensure each Landlord has a percentage split that adds up to 100", "archiveDocument": "Archive Document", "sharedDocumentDeleteWarning": "Deleting this will delete the document from all linked properties.", "areYouSureToDeleteIt": "Are you sure you want to delete it?", "sharedDocumentArchiveWarning": "Archiving this will move the Document to the archived section and remove it from the linked Properties.", "areYouSureToArchiveIt": "Are you sure you want to archive it?", "sharedDocumentRenameWarning": "Renaming this Document will update its name across all linked Properties.", "areYouSureToRenameIt": "Are you sure you want to rename it?", "sharedDocumentUnarchiveWarning": "Unarchiving this will move the Document to the active section and remove it from the linked Properties.", "areYouSureToUnarchiveIt": "Are you sure you want to unarchive it?", "propertyKeyDetail": "Property Key Detail", "yearBuilt": "Year Built", "lotSizeSqft": "Lot Size (Sqft)", "descriptionOptional": "Description (Optional)", "EnterYourContactDetailsWithNextSteps": "Enter your contact details, and we'll let the rental manager contact you with next steps.", "outstanding": "Outstanding", "areYouSureToRemoveThisUnit": "Are you sure to remove this unit?", "searchByAddressCityZipcode": "Search by Address, City, Zipcode", "underContracts": "Under Contracts", "propertyTourRequestCreatedSuccessfully": "Property Tour Request created successfully", "allContracts": "All Contracts", "newVersion": "New Version", "oldVersion": "Old Version", "propertyInformation": "Property Information", "taxRating": "Tax & Rating", "councilTaxBand": "Council Tax Band", "EPCRating": "EPC Rating", "furnished": "Furnished", "propertyUpdatedSuccessfully": "Property Updated Successfully", "squareMetres": "Square Metres", "availableNow": "Available Now", "postedToRightMoveSuccessfully": "Property posted to RightMove successfully!", "removedFromRightMoveSuccessfully": "Property removed from RightMove successfully!", "postedToZooplaSuccessfully": "Property posted to Zoopla successfully!", "removedFromZooplaSuccessfully": "Property removed from Zoopla successfully!", "updatingPropertyLandlordWarning": "Updating the property ownership structure will impact all linked properties.", "otherLandlords": "Other Landlords", "percentageSplitError": "Ensure each Landlord has a percentage split that adds up to 100", "linkProperties": "Link Properties", "aggregatedInformation": "Aggregated Information", "noProperties": "there is not any Properties", "addPropertyLogo": "Add Property Logo", "supplementary": "Supplementary", "addressLine": "Address Line {{ number }}", "furnishedStatus": "Furnished Status", "groupByParentProperty": "Group by Parent Property", "addAmenities": "Add Amenities", "addIncludes": "Add Includes", "squareFt": "Square Feet", "squareMtrs": "Square Meters", "conversionSqftToSqm": "Conversion: 1 sqft = 0.092903 sqm", "conversionSqmToSqft": "Conversion: 1 sqm = 10.7639 sqft", "FULLY_FURNISHED": "Furnished", "NOT_FURNISHED": "Unfurnished", "PART_FURNISHED": "Part Furnished", "FURNISHED_OPTIONAL": "Furnished Optional", "postYourPropertiesToZooplaFromRentancy": "Post your Properties to Zoopla from LoftyWorks", "toAutomaticallySynchroniseWithZoopla": "To automatically synchronise with Zoopla", "createAZooplaAccount": "Create a Zoopla account", "pleaseContactZooplaToRetrieveYourUniqueBranchIDAndProvideThisHere": "Please contact Zoopla to retrieve your unique Branch ID and provide this here", "allProperties": "All Properties", "arrearsBalance": "Arrears Balance", "nextRentDate": "Next Rent Date", "currentTenants": "Current Tenants", "pendingApproval": "Pending Approval", "propertyBalance": "Property Balance", "totalBalance": "Total Balance", "marketing": "Marketing", "inventory": "Inventory", "purchaseDate": "Purchase Date", "warrantyExpiryDate": "Warranty Expiry Date", "purchase": "Purchase", "warranty": "Warranty", "generateInventoryReport": "Generate Inventory Report", "inventoryReport": "Inventory Report", "noAssetsToGenerateReport": "No assets to generate report", "tooManyFiles": "Too many files", "invalidFileType": "Invalid file type", "fileTooLarge": "File is too large", "fileTooSmall": "File is too small", "propertyNotPublished": "Property Not Published", "propertyManagerAndLandlord": "Property Manager and Landlord", "rentalTerm": "Rental Term", "selectTerm": "Select Term", "selectSchedule": "Select Schedule", "rentGuarantee": "Rent Guarantee", "rentGuaranteeRequired": "Rent Guarantee Required", "reminderTip": "Choose how far in advance to remind before the due date", "policy": "Policy", "petPolicy": "Pet Policy", "smokingPolicy": "Smoking Policy", "utilitiesIncluded": "Utilities Included", "select_all_utilities_included_for_this_property": "Select all utilities included for this property", "addUtilities": "Add Utilities", "utility": "Utility", "enterUtilityName": "Enter Utility Name", "enterSupplierName": "Enter Supplier Name", "createProperty": "Create Property", "propertyFurnishingsTracking": "Add and track property furnishings, appliances, and fixtures.", "noInventoryItems": "No Inventory added", "inventoryItemName": "Item Name", "inventoryItemBrand": "Brand", "inventoryItemModel": "Model", "inventoryItemSerialNumber": "Serial Number", "inventoryItemPurchaseDate": "Purchase Date", "inventoryItemNote": "Note", "inventoryItemNameRequired": "Please enter the Item Name", "propertyService": "Property Service", "tenancyTerm": "Tenancy Term", "inventories": "Inventories", "serialNumber": "Serial Number", "purchaseTime": "Purchase time", "brand": "Brand", "meter": "<PERSON>er", "latestCheck": "Latest check", "otherDocs": "Other Docs", "viewTask": "View Task", "complianceName": "Compliance Name", "minimumTenancy": "Minimum Tenancy", "numberOfmonths": "{{number}} Months", "rolling": "Rolling", "letting": "Letting", "onTheMarket": "On the Market", "additionalCost": "Additional Cost", "cost": "Cost", "notRequired": "Not Required", "typePercentage": "Type & Percentage", "passed": "Passed", "addSupplier": "Add Supplier", "supplierName": "Supplier Name", "viewLedgers": "View Ledgers", "tenantLedgers": "Tenant Ledgers", "landlordLedgers": "Landlord Ledgers", "yourRentalApplicationHasBeenSentSuccessfully": "Your rental application has been sent successfully!", "confirmationNumber": "Confirmation number", "youCanLogIntoYourPortalToCheckYourApplicationReview": "You can log into your portal to check your application review status", "submitApplicationInformation": "Submit application information", "appliactionContact": "Contact", "appliactionMoveIn": "Expected Move-in", "appliactionSubmittedAt": "Submitted At", "appliactionStatus": "Status", "appliactionProperty": "Property", "reg": "Reg", "contactInfo": "Contact Info", "prospects": "Prospects", "addProspectTitle": "Prospect", "editProspectTitle": "Edit Prospect", "typePlaceholder": "Type Something…", "propertyPlaceholder": "Select Parent Property / Property", "taskChecklistPlaceholder": "Select Task Checklists", "City": "City", "labelMaxRent": "<PERSON>", "labelPropertyType": "Property Type", "labelMinBedrooms": "Min Bedrooms", "labelBathrooms": "Min Bathrooms", "detailInfo": "Detail Info", "areYouSureToChangeTheProspectStatus": "Are you sure to change the prospect status?", "yesChangeIt": "Yes，Change it", "regDate": "Reg Date", "expectedMoveInDate": "Expected Move-in Date", "addInterestedPropertyWhenBlank": "Add details of your property to start marketing your property on top listing site", "addInterestedPropertyWithPlus": "+ Add Interested Property", "confirmDeleteInterestProperty": "Are you sure to delete this Interest Property?", "confirmDeleteProspect": "Are you sure to remove the prospect?", "yesDelete": "Yes, Delete It", "interestProperty": "Interest Property", "keyDetails": "Key details", "maxPropertyTypesExceed": "Max count of property types: {{count}}", "creditScore": "Credit Score", "prospectCallButton": "Call", "filesTab": {"dragFilesOrClickHereToUpload": "Drag files or click here to upload", "pdfDocumentsSpreadsheetsOrGraphics": ".PDF, Document (.DOC, .DOCX, .TXT, .ODT), Spreadsheet (.CSV, .XLS, .XLSX, .ODS), or Graphic (.JPG, .PNG, .GIF).", "pdfDocuments": ".PDF, Document (.DOC, .DOCX, .TXT, .ODT)", "onlyMaxFileSizeText": "Max file size", "fileDeletedSuccessfully": "File deleted successfully.", "errorWhenDeletingFile": "An error was encountered while deleting the file. Please try again.", "downloadAll": "Download All", "addDescription": "Add Description", "enterDescription": "Enter description...", "areYouSureToDeleteTheFile": "Are you sure to delete the file?", "yesDeleteIt": "Yes, Delete it", "pleaseWaitAMoment": "Please wait a moment...", "fileExceededMaxSize": "{{fileName}} exceeded file max size"}, "interestPropertyRequired": "Interest property is required, please add at least one property", "invalidZipCode": "Invalid zip code format", "prospectEmailExists": "Email already exists. Please enter another.", "issue": "Issue", "selectPropertyToCreateReport": "Select property to create report", "problemPicture": "Problem Picture", "areYouSureYouWantToDeleteThisProblem": "Are you sure you want to delete this problem?", "problemResolvedSuccessfully": "Problem resolved successfully!", "areYouSureYouWantToCancelThisIssue": "Are you sure you want to cancel this issue?", "settings": "Settings", "connectEmail": "Connect Email", "personalMessageType": "Personal (Messages are only visible to you)", "sharedMessageType": "Shared (Messages are visible to everyone)", "screenYourTenantsWithTransUnion": "Screen your Tenants with TransUnion", "toAutomaticallySynchroniseYourTenantReferencesBetweenTransUnion": "To automatically synchronize your Tenant References between TransUnion and LoftyWorks.", "weUtilizeTransUnionAsOurPartnerForTenantScreening": "We utilize TransUnion as our partner for Tenant Screening. By using our integration with TransUnion, you can effectively screen your Tenants.", "transUnionSetUp": "TransUnion Set-Up", "businessName": "Business Name", "businessAddress": "Business Address", "connectWithYourStripeAccount": "Connect with your <PERSON>e Account", "connectWithWhatsApp": "Connect with WhatsApp", "integrateWithStripeConnectToStreamlineYourRentalCollectionProcess": "Integrate with Stripe Connect to streamline your Rental Collection Process", "weHaveIntegratedWithAMarketLeaderInStripe": "We have integrated with a market leader in Stripe to allow you to streamline your Rental Collection process with your Tenants.", "configureFees": "Configure Fees", "forEachIncomingRentalPaymentStripeCharge": "For each incoming rental payment, Stripe charge 0.8% (up to $5 maximum) for each transaction. LoftyWorks do not profit from this, and this fee solely goes to Stripe for using their payment infrastructure.", "weUnderstandThatYouMightNotWantToBearThisCost": "We understand that you might not want to bear this cost, so here you can configure it as follows:", "rehargeForACH": "Recharge for ACH", "rechargePercentage": "Recharge percentage", "maximumCharge": "Maximum Charge", "ifYourRentersMonthlyRentWas": "If your Renter's Monthly Rent was $1000, they would pay an additional $5 meaning that their total payment would be $1005.", "cannotBeZero": "Cannot be zero", "payYourLandlordsAndSuppliersWithCheckbook": "Pay your Landlords and Suppliers with Checkbook.io", "weWillUseCheckbookAsOurPartnerToFacilitateOutgoingPayments": "We utilize Checkbook as our partner to facilitate outgoing payments.", "configureProfile": "Configure Profile", "connectToABankAccount": "Connect to a bank account", "linkBankAccount": "Link Bank Account", "updateBankAccount": "Update Bank Account", "checkbook": "Checkbook.io", "business": "Business", "principleOwner": "Principle Owner", "officer": "Officer", "citizenShip": "Citizen Ship", "enterFirstName": "Enter First Name", "enterLastName": "Enter Last Name", "enterCity": "Enter City", "enterState": "Enter State", "enterZipCode": "Enter ZipCode", "successfullyLinked": "Successfully Linked", "taxID": "Tax ID", "enterTaxID": "Enter Tax ID", "enterBusinessName": "Enter Business Name", "occupation": "Occupation", "last4SSN": "Last 4 Digits of SSN", "birthday": "Birthday", "info": "Info", "returnToSettings": "Return to Settings", "linked": "Linked", "approvedConnectCheckbook": "Your company information has been approved by Checkbook, and the corresponding account has been generated in LoftyWorks. Please select which Banks you will be paying Suppliers from.", "connectWithPlaid": "Connect with Plaid", "pleaseSelectYourBank": "Please select the bank card you want to link with the Plaid account for LoftyWorks reconciliation. After connecting, your bank statements will be imported automatically. You have the option to disconnect this account from Plaid.", "rentDueAutomation": "Rent Due Automation", "lateRentAutomation": "Late Rent Automation", "triggerRentDueAutomation": "Trigger Rent Due Automation", "triggerLateRentAutomation": "Trigger Late Rent Automation", "timeMustBeInFormat": "Time must be in format HH:mm", "editEmail": "<PERSON> Email", "triggerProcessOverdueAfter": "Trigger Process Overdue After", "automatedInvoice": "Automated Invoice", "editTemplate": "Edit Template", "emailSentFrom": "<PERSON><PERSON>", "infiniceptHeader": "Process your Inbound and Outbound Payments", "infiniceptSubHeader": "Collect rent and make payments all in one platform", "feesHaveBeenConfiguredSuccessfully": "Fees have been configured successfully!", "infiniceptConfigureFeesHint": "Configure fees for ACH and credit card transactions", "infiniceptWarningForEachPayment": "For each incoming rental payment, Infinicept will charge $0.50 per ACH transaction.  and charge 0.35% of transaction amount and $0.10 fixed for Credit Card Transaction. Loftyworks do not profit from this, and this fee solely goes to Infinicept.", "integrationSuccessful": "Integration Successful", "integrationFailed": "Integration Failed", "run": "Run", "runAutoJournalNow": "Run Auto Journal Now", "facebookIntegrationSuccessfully": "Facebook account connected successfully", "successfullyRanAutoJournal": "Successfully Ran Auto Journal", "doYouWantToRunAutoJournalNow": "Do you want to auto journal now?", "thisWillRunAutoJournalForTheWholeOrganisation": "This will run Auto Journal for the whole organisation, are you sure you want to run it now?", "requestPhoneNumber": "Request Phone Number", "smsIntegrationSuccess": "Phone number purchased successfully", "smsIntegrationDisconnectSuccess": "Phone number disconnected successfully", "smsIntegrationFailed": "Phone number purchase failed, please try again later", "sharedNumber": "Shared Number", "personalNumber": "Personal Number", "twilioIntegrationTitle": "Connect with your <PERSON><PERSON><PERSON> Account", "twilioIntegrationSubtitle": "Send and receive SMS messages in Communication module.", "twilloIntegrationSuccessfully": "Twillo phone number was integrated successfully", "twilioIntegrationDescription": "Easily send and receive SMS messages within the communication module. Set up a virtual\n number, and start messaging instantly. Stay organized with automatic message sorting,\n real-time notifications, and usage tracking. Activate now to enhance your communication!", "twilioIntegration": "Twilio <PERSON>", "simpleSetup": "Simple Setup", "twilioProcess1": "Request Virtual Twilio Number", "twilioProcess2": "Return to LoftyWorks and see your virtual phone number", "twilioProcess3": "Start sending and receiving SMS messages in the Communication Module.", "loginWithFacebook": "Login with Facebook", "whatsAppIntegrationTitle": "Connect with your Facebook Account", "whatsAppIntegrationSuptitle": "Send and receive WhatsApp messages in Communication module.", "expiringPhoneNumberDescription": "Inactive. Will be removed from system in {{days}} days, please reach out to support to reactivate.", "invoiceGenerationTimeUpdatedSuccessfully": "Invoice Generation TimeUpdated Successfully", "vatSettings": "VAT Settings", "allTasks": "All Tasks", "addNewTenancy": "Tenancy", "endTenancy": "End Tenancy", "saveAndExit": "Save & Exit", "landlordReview": "Landlord Review", "tenantReview": "Tenant Review", "confirmation": "Confirmation", "tenantReviewSuccessMessage": "The End of Tenancy process is now complete. If the tenancy is not renewing, remember to visit <0>link</0> to update the deposit status.", "emailSent": "<PERSON><PERSON>", "landlordReviewSuccessMessage": "You will receive an update shortly with next steps.", "tenancyInformation": "Tenancy Information", "createTenancy": "Create Your First Tenancy", "createTenancyDesc": "Set up tenants, rent details, and agreements to manage your tenancy smoothly in one place.", "createTenancyDoc": "Add tenancy agreement or related documents.", "tenancyDetail": "Tenancy Details", "oneOffChargesDesc": "Add non-recurring costs such as move-in fees, maintenance, late payments, or admin charges.", "currentRent": "Current Rent", "newRent": "New Rent", "newRentDesc": "Only change amount if rent change recommended", "newRentError": "New Rent should greater than or equal to 0", "letterForLandlord": "Letter for Landlord", "landlordEmailNotExist": "Landlord email does not exist", "tenantEmailNotExist": "Tenant email does not exist", "addEmail": "Add <PERSON>", "messageLandlord": "Message Landlord", "messageTenant": "Message Tenant", "tenancyTerminated": "Tenancy Terminated", "tenancyCompleted": "End of Tenancy Completed", "waitingLandlordreply": "Waiting for <PERSON><PERSON> to reply", "waitingTenantreply": "Waiting for Tenant to reply", "endProcessMessage": "The End of Tenancy process is now complete.\nRemember to visit Deposit Management to update the deposit status.", "successProcessMessage": "The End of Tenancy process is now complete. ", "waitingLandlordreplyMessage": "You will be notified once the landlord replies. In the meantime,\nyou can use our communication tool to send them a message.", "waitingTenantreplyMessage": "You will be notified once the tenant replies. In the meantime,\nyou can use our communication tool to send them a message.", "backEOT": "Back to End of Tenancy", "formSubmit": "Form Submitted", "landlordConfirmationEmailSuccess": "Thank you. We will contact the tenant to notify them of your decision and update you accordingly.", "tenantConfirmationEmailSuccess1": "Thanks for completing the form.", "tenantConfirmationEmailSuccess2": "We will be in touch with next steps.", "commonConfirmationSuccess": "Thanks for completing the form. We will be in touch with next steps.", "landlordResponse": "Landlord Response", "tenantResponse": "Tenant Response", "letterForTenant": "Letter For Tenant", "decision": "Decision", "renewal": "Renewal", "addATask": "Add a task", "proposedRent": "Proposed <PERSON><PERSON>", "endOfTenancyCompletedMessage": "The End of Tenancy process is now complete.", "useDocusign": "Use Docusign", "sendOffline": "Send Offline", "signNewContract": "Sign a New Contract", "tenancyTerminatedMessage": "The End of Tenancy process is now complete. Remember to visit Deposit Management to update the deposit status.", "leadTenant": "Lead Tenant", "questionAcceptIncreasePrice": "Would you like to accept the recommended rent increase of {price} ?", "questionAcceptDecreasePrice": "Would you like to accept the recommended rent decrease of {price} ?", "questionIncreasePrice": "What is your proposed rent?", "questionAcceptRenewTenancy": "Would you like to renew with the current tenant ?", "questionAcceptRenewYourTenancy": "Would you like to renew your tenancy ?", "questionPeriodRenew": "How long would you like to renew the tenancy for ?", "questionRenewTenancyFurther": "Would you like to renew for a further {period} months ?", "questionRenewRollingPeriod": "Would you like to renew on a month-to-month basis?", "questionAcceptRecommendedIncreasePrice": "Do you accept the recommended rent increase of {price} ?", "questionTenantProvideProposeIncreasePrice": "Please propose a rent increase amount below. We will get back to you with a confirmation.", "proposalPeriod": "{period} months", "confirmationEmailSent": "Confirmation Em<PERSON>", "EOTCompleted": "End Of Tenacy Completed", "pleaseInputAmount": "Please input a valid amount", "tenancyConfirmation": "Tenancy Confirmation", "addATenancy": "Add A Tenancy", "addATenancyMessage": "Manage your property by adding tenant details, lease terms, and rent information in one place.", "currentTenancy": "Current Tenancy", "tenancyStatus": "Tenancy Status", "tenancyType": "Tenancy Type", "tenancyStart": "Tenancy Start", "depositHeld": "<PERSON><PERSON><PERSON><PERSON>", "nextPayment": "Next Payment", "forLet": "For Let", "composeLetter": "Compose Letter", "renewTenancy": "Renew Tenancy", "manage": "Manage", "tenantName": "Tenant Name", "addPermittedOccupiers": "Add Permitted Occupiers", "erv": "ERV", "rightToRent": "Right to Rent", "guarantorName": "Guarantor Name", "permittedOccupierName": "Occupier Name", "members": "Members", "editTenancy": "Edit Tenancy", "enterTitle": "Enter Title", "changeDateWarning": "Change Date Warning", "changeDateWarningMsg": "Changing the date will result in the tenancy status update and may impact associated operations. Do you wish to proceed?", "unsavedChanges": "Unsaved Changes", "unsavedChangesMsg1": "Are you sure you want to leave this page?", "unsavedChangesMsg2": "Changes you made will not be saved.", "enabledForTenancy": "Enabled for tenancy", "nextSendDate": "Next Send Date", "nextDueDate": "Next Due Date", "lastPaymentTo": "Last Payment to", "nextRentInvoice": "Next Rent Invoice", "confirmAndEnd": "Confirm & End", "endTenancyTips": "Are you sure you want to cancel your tenancy before the end date? This action cannot be undone.", "reminder": "Reminder", "reminderTips": "This tenancy is set to end on {{date}}. Move-out details will be available after the tenancy ends.", "moveOut": "Move-Out", "moveOutDateIsRequired": "Move-out date is required", "moveOutTips": "Complete move-out to start a new tenancy", "moveOutCompleted": "Move Out Completed", "moveOutDate": "Move-Out Date", "moveOutDateTips": "Confirm your move-out date for a smooth transition", "utilitiesUpdate": "Utilities Update", "utilitiesUpdateTips": "Update the latest utility meters", "checkoutReport": "Checkout Report", "checkoutReportTips": "Upload checkout report for tenancy to keep record", "addTaskForTenancy": "Add task for this tenancy", "outstandingPayments": "Outstanding Payments", "outstandingPaymentsTips": "Reminder for outstanding payments", "notificationEmails": "Notification Emails", "notificationEmailsTips": "Notify landlord and tenant for ending tenancy", "tenantNotificationLetter": "Tenant Notification Letter", "landlordNotificationLetter": "Landlord Notification Letter", "existingActiveTenancyFound": "Existing Active Tenancy Found", "existingActiveTenancyFoundTips": "This property already has an active tenancy. Please ensure the new tenancy details do not conflict with the existing active tenancy before proceeding. Would you like to continue?", "checkApplication": "Check Application", "inviteToClientPortal": "Invite to Client Portal", "iHaveAlreadySigned": "I’ve already signed – Just want to update the record.", "iWantToRenew": "Let’s do it here – I’d like to renew through here.", "allTenancies": "All Tenancies", "duplicate": "Duplicate", "addressReferenceOrPrimaryTenant": "Address, Reference, Primary Tenant...", "addGuarantor": "Add Guarantor", "addTenant": "Add Tenant", "permittedOccupier": "Permitted Occupier", "permittedOccupiers": "Permitted Occupiers", "addPermittedOccupier": "Add Permitted Occupier", "rightToRentStatus": "Right to Rent Status", "past": "Past", "current": "Current", "future": "Future", "inviteToPortal": "Invite to {{portal}} portal", "invited": "Invited", "tenantPortal": "Tenant Portal", "emergencyContact": "Emergency Contact", "sentAt": "<PERSON><PERSON>", "loginEmail": "<PERSON><PERSON>", "weightLbs": "Weight(lbs)", "myPetIsAServiceAnimal": "My pet is a service animal", "editTenantTitle": "Edit Tenant", "addTenantTitle": "Add New Tenant", "addLeaseTitle": "Add <PERSON>se", "addTenantTips": "These details are required for rent reminders, tenant communications and portal access.", "confirmDeleteTenant": "Are you sure to delete this Tenant?", "error": "Error", "tenantCannotBeDeleted": "This tenant is associated with a lease. Please delete the lease first and try again.", "invitationSentSuccessfully": "Invitation sent successfully", "tenantScreening": "Tenant Screening", "createReference": "Create Reference", "editReference": "Edit Reference", "whatsapp": "WhatsApp", "createReferenceDescription": "Conduct your referencing through LoftyWorks and take advantage of our workflows allowing you to share completed references with your Landlord, and create a Contract following a successful reference.", "recommendPlatform": "Recommend Platform", "vouchFeature1": "Use Vouch with LoftyWorks to streamline referencing for your Tenant's by eliminating double entry and saving you time whilst utilising a market leading provider.", "vouchFeature2": "Each reference with Vouch costs £X, and includes Equifax Credit Checks, Employer & Landlord referencing alongside affordability calculation and right to rent checks.", "alternativeProvider": "Alternative Provider", "alternativeProviderFeature": "If you are already utilising another Referencing provider, you can still utilise LoftyWorks to upload your reference details and share information with your Landlord", "viewReference": "View Reference", "shareDocument": "Share Document", "createContract": "Create Contract", "tenantReferenceDeletedSuccessfully": "Tenant reference deleted successfully", "confirmDeleteTenantReference": "Are you sure to delete this Reference?", "yesDeleteIt": "Yes, Delete It", "tenantReferenceSharedSuccessfully": "Tenant reference shared with landlord successfully", "viewContactDetails": "View Contact Details", "decline": "Decline", "denial": "Denial", "workorder": "Workorder", "reasonForRejection": "Reason for rejection", "pleseEnterTheReasonForRejection": "Please enter the reason for rejection", "rejectedSuccessfully": "Rejected successfully", "reportedBy": "Reported by", "supplierReported": "Supplier reported", "contactManger": "Contact manager"}