import { fetchHelper } from "@rentancy/common";
import type { Result } from "Api/type";
import {
  ApplicationData,
  GetAllApplicationParams,
  CreateApplicationParams,
  UpdateApplicationParams,
  AssignApplicationParams,
  UpdateApplicationTaskParams,
  ListDataResponse,
} from "./types";

export const createApplication = (data: CreateApplicationParams): Promise<Result<void>> => {
  return fetchHelper({
    url: `${process.env.MAIN_ENDPOINT}/api/v1/application`,
    method: "POST",
    body: data,
  });
};

export const updateApplication = (data: UpdateApplicationParams): Promise<Result<void>> => {
  const { applicationId, status, remark } = data;
  return fetchHelper({
    url: `${process.env.MAIN_ENDPOINT}/api/v1/application/${applicationId}`,
    method: "PUT",
    body: {
      status,
      remark,
    },
  });
};

export const assignApplication = (data: AssignApplicationParams): Promise<Result<void>> => {
  const { applicationId, managers } = data;
  return fetchHelper({
    url: `${process.env.MAIN_ENDPOINT}/api/v1/application/${applicationId}`,
    method: "PUT",
    body: {
      managers,
    },
  });
};

export const deleteApplication = (applicationId: string): Promise<Result<void>> => {
  return fetchHelper({
    url: `${process.env.MAIN_ENDPOINT}/api/v1/application/${applicationId}`,
    method: "DELETE",
  });
};

export const getAllApplication = ({
  page = 1,
  limit = 30,
  keyword,
  type,
  orderBy,
  orderDirection,
}: GetAllApplicationParams): Promise<Result<ApplicationData>> => {
  const searchParams = new URLSearchParams();

  // Helper to append to searchParams if value is present
  const appendParam = (key: string, value: string | number | null | undefined) => {
    if (value !== null && value !== undefined) {
      searchParams.set(key, String(value));
    }
  };

  appendParam("page", page);
  appendParam("size", limit); // API expects 'size'
  appendParam("keyword", keyword);
  appendParam("status", type); // API expects 'status'
  appendParam("orderBy", orderBy);
  appendParam("orderDirection", orderDirection);
  appendParam("t", String(Date.now())); // Cache busting

  return fetchHelper({
    url: `${process.env.MAIN_ENDPOINT}/api/v1/application?${searchParams.toString()}`,
    method: "GET",
  });
};

export const updateApplicationTask = (data: UpdateApplicationTaskParams) => {
  const { applicationId, taskId, status, detail, documentIds } = data;
  return fetchHelper({
    url: `${process.env.MAIN_ENDPOINT}/api/v1/application/${applicationId}/task/${taskId}`,
    method: "PUT",
    body: {
      status,
      detail,
      documentIds,
    },
  });
};

export const getApplicationDetail = (propertyId: string): Promise<Result<ListDataResponse>> => {
  return fetchHelper({
    url: `${process.env.MAIN_ENDPOINT}/api/v1/property/applications/${propertyId}`,
    method: "GET",
  });
};

export const getApplicationStatics = (): Promise<Result<ListDataResponse>> => {
  return fetchHelper({
    url: `${process.env.MAIN_ENDPOINT}/api/v1/application/statistics`,
    method: "GET",
  });
};
