import { Header<PERSON><PERSON><PERSON>, Pagination, TableSortLabel } from "@rentancy/ui";
import {
  Box,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";
import { ReactComponent as NoRecord } from "@rentancy/icon/svgs/no-records.svg";
import { makeStyles } from "@mui/styles";
import { useEffect, useMemo, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import { clsx } from "clsx";
import { useTranslation } from "react-i18next";
import { DEFAULT_PAGINATION_LIMIT, useQueryString } from "@rentancy/common";
import { useTableStyles, OrderTable } from "@rentancy/ui";
import { TableLoading } from "./TableLoading";
import { ApplicationFilters } from "./Filter";
import { StatusLabel } from "./StatusLabel";
import { TaskLabel } from "./TaskLabel";
import moment from "moment/moment";
import { TaskCategory, ApplicationHeaderCellId } from "../types";

type Props = {
  data?: any;
  loading: boolean;
  refetch: () => void;
};

export const ApplicationDesktop = ({ data, loading }: Props) => {
  const classes = useStyles();
  const tableClasses = useTableStyles();

  const { query, setQuery } = useQueryString<{
    limit: string;
    page: string;
    type: string;
    orderBy: string;
    orderDirection: string;
  }>();
  const { limit } = query;
  const { t } = useTranslation();
  const navigate = useNavigate();
  const container = useRef<HTMLDivElement>(null);

  const [orderDirection, setOrderDirection] = useState<OrderTable>("desc");
  const [orderBy, setOrderBy] = useState<string>("movingDate");

  // Define widths for the sticky columns (Status, Application, Moving Date)
  const stickyColumnWidths = useMemo(() => [150, 250, 150], []); // [Status width, Application width, Moving Date width]
  const stickyColumnLeftValues = useMemo(() => {
    const leftValues = [0];
    for (let i = 0; i < stickyColumnWidths.length - 1; i++) {
      leftValues.push(leftValues[i] + stickyColumnWidths[i]);
    }
    return leftValues;
  }, [stickyColumnWidths]);

  const resetScroll = () => {
    container?.current?.scrollTo(0, 0);
  };

  useEffect(resetScroll, [data]);

  const createSortHandler = (sort: string) => {
    setOrderBy(sort);
    const sortValue: { orderBy: string; orderDirection?: OrderTable } = { orderBy: sort };
    if (orderDirection === "asc") {
      sortValue.orderDirection = "desc";
    } else {
      sortValue.orderDirection = "asc";
    }
    setQuery(sortValue);
    setOrderDirection(sortValue.orderDirection);
  };

  const headerCells: HeaderCells<ApplicationHeaderCellId> = useMemo(
    () => [
      {
        id: "status" as ApplicationHeaderCellId,
        label: t("modules.application.tablecolumn.status"),
        sortable: false,
      },
      {
        id: "application" as ApplicationHeaderCellId,
        label: t("modules.application.tablecolumn.application"),
        sortable: false,
        style: {},
      },
      {
        id: "moveInDate" as ApplicationHeaderCellId,
        label: t("modules.application.tablecolumn.movingDate"),
        sortable: true,
        sort: "moveInDate" as ApplicationHeaderCellId,
        style: {},
      },
      {
        id: "deposit" as ApplicationHeaderCellId,
        label: t("modules.application.tablecolumn.deposit"),
        sortable: false,
        style: {},
      },
      {
        id: "rightToRent" as ApplicationHeaderCellId,
        label: t("modules.application.tablecolumn.rightToRent"),
        sortable: false,
        style: {},
      },
      {
        id: "referencing" as ApplicationHeaderCellId,
        label: t("modules.application.tablecolumn.referencing"),
        sortable: false,
        style: {},
      },
      {
        id: "compliances" as ApplicationHeaderCellId,
        label: t("modules.application.tablecolumn.compliances"),
        sortable: false,
        style: {},
      },
      {
        id: "tenancyAgreement" as ApplicationHeaderCellId,
        label: t("modules.application.tablecolumn.tenancyAgreement"),
        sortable: false,
        style: {},
      },
      {
        id: "inventory" as ApplicationHeaderCellId,
        label: t("modules.application.tablecolumn.inventory"),
        sortable: false,
        style: {},
      },
      {
        id: "utilities" as ApplicationHeaderCellId,
        label: t("modules.application.tablecolumn.utilities"),
        sortable: false,
        style: {},
      },
      {
        id: "moveIn" as ApplicationHeaderCellId,
        label: t("modules.application.tablecolumn.moveIn"),
        sortable: false,
        style: {},
      },
    ],
    [t],
  );

  const handleEnterDetail = item => {
    navigate(`/property/${item.propertyId}/application`);
  };

  return (
    <Grid container className={classes.root}>
      <Grid className={tableClasses.tableWrapper}>
        <Box className={tableClasses.tableFilter}>
          <ApplicationFilters loading={loading} />
        </Box>
        <TableContainer
          ref={container}
          className={classes.tableContainer}
          sx={theme => ({
            "& tbody > tr:last-child > td": {
              borderBottom: `1px solid ${theme.palette.specialColors.grey[200]}`,
            },
          })}
        >
          <Table aria-label="simple table" stickyHeader>
            <TableHead>
              <TableRow>
                {headerCells.map((item, index) => {
                  const isSticky = index < 3; // First three columns are sticky
                  const stickyStyle = isSticky
                    ? {
                        position: "sticky" as const,
                        left: stickyColumnLeftValues[index],
                        zIndex: 10, // Higher zIndex for header
                        minWidth: stickyColumnWidths[index], // Ensure min width for sticky columns
                        width: stickyColumnWidths[index], // Set width for sticky columns
                      }
                    : {
                        minWidth: 125,
                        width: 125,
                      };

                  if (item.sortable) {
                    return (
                      <TableCell
                        key={item.id}
                        sortDirection={item.id === orderBy ? orderDirection : false}
                        className={classes.head}
                        style={{ ...item.style, ...stickyStyle }}
                      >
                        <TableSortLabel
                          active={item.sort === orderBy}
                          direction={item.sort === orderBy ? orderDirection : "asc"}
                          onClick={() => createSortHandler(item.sort!)}
                        >
                          <span>{item.label}</span>
                        </TableSortLabel>
                      </TableCell>
                    );
                  }
                  return (
                    <TableCell
                      key={item.id}
                      sortDirection={false}
                      className={classes.head}
                      style={{ ...item.style, ...stickyStyle }}
                    >
                      <span style={item.style}>{item.label}</span>
                    </TableCell>
                  );
                })}
              </TableRow>
            </TableHead>
            <TableBody>
              {loading && <TableLoading loading={loading} />}
              {!loading &&
                (data?.data.length === 0 ? (
                  <div className={classes.noDataWrapper}>
                    <NoRecord />
                    {t("noRecords")}
                  </div>
                ) : (
                  data?.data.map((item: any) => (
                    <TableRow
                      key={item.id}
                      className={clsx(classes.wrapperRow)}
                      onClick={() => handleEnterDetail(item)}
                      data-testid="property-table-row"
                    >
                      <TableCell
                        style={{
                          position: "sticky",
                          left: stickyColumnLeftValues[0],
                          zIndex: 1,
                          backgroundColor: "#fff",
                          minWidth: stickyColumnWidths[0],
                          width: stickyColumnWidths[0],
                        }}
                      >
                        <StatusLabel status={item?.status} item={item} />
                      </TableCell>
                      <TableCell
                        style={{
                          position: "sticky",
                          left: stickyColumnLeftValues[1],
                          zIndex: 1,
                          backgroundColor: "#fff",
                          minWidth: stickyColumnWidths[1],
                          width: stickyColumnWidths[1],
                        }}
                      >
                        <Typography variant="body1" color="#202437">
                          {item?.propertyName ?? "-"}
                        </Typography>
                        <Typography variant="body1" color="#797E8B">
                          {item?.primaryTenantName ?? "-"}
                        </Typography>
                      </TableCell>

                      <TableCell
                        style={{
                          position: "sticky",
                          left: stickyColumnLeftValues[2],
                          zIndex: 1,
                          backgroundColor: "#fff",
                          minWidth: stickyColumnWidths[2],
                          width: stickyColumnWidths[2],
                        }}
                      >
                        <Typography variant="caption">
                          {moment(item.moveInDate).format("DD MMM yyyy")}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <TaskLabel tasks={item?.tasks} category={TaskCategory.Deposit} />
                      </TableCell>
                      <TableCell>
                        <TaskLabel tasks={item?.tasks} category={TaskCategory.RightToRent} />
                      </TableCell>
                      <TableCell>
                        <TaskLabel tasks={item?.tasks} category={TaskCategory.Referencing} />
                      </TableCell>
                      <TableCell>
                        <TaskLabel tasks={item?.tasks} category={TaskCategory.Compliance} />
                      </TableCell>
                      <TableCell>
                        <TaskLabel tasks={item?.tasks} category={TaskCategory.TenancyAgreement} />
                      </TableCell>
                      <TableCell>
                        <TaskLabel tasks={item?.tasks} category={TaskCategory.Inventory} />
                      </TableCell>
                      <TableCell>
                        <TaskLabel tasks={item?.tasks} category={TaskCategory.Utilities} />
                      </TableCell>
                      <TableCell>
                        <TaskLabel tasks={item?.tasks} category={TaskCategory.MoveIn} />
                      </TableCell>
                    </TableRow>
                  ))
                ))}
            </TableBody>
          </Table>
        </TableContainer>
        <Box width="100%" className={clsx(tableClasses.paginationWrapper, classes.borderTop)}>
          <Pagination
            count={Math.ceil(data?.total / Number(limit))}
            disabled={!data?.data?.length}
            paginationLimit={DEFAULT_PAGINATION_LIMIT}
          />
        </Box>
      </Grid>
    </Grid>
  );
};

export const useStyles = makeStyles(theme => ({
  root: {
    display: "flex",
    flexDirection: "column",
    overflow: "hidden",
    flex: 1,
    padding: theme.spacing(2.5),
    flexWrap: "nowrap",
  },
  tableContainer: {
    border: "none",
    flex: 1,
    overflowX: "auto",
    "& tbody ": {
      position: "relative",
      "& > tr > td > *": {
        width: "max-content",
      },
    },
  },
  statusTooltip: {
    backgroundColor: theme.palette.specialColors.grey[800],
    fontSize: 12,
    color: theme.palette.specialColors.grey[400],
    padding: theme.spacing(1, 1, 1, 2),
  },
  head: {
    fontWeight: 600,
    backgroundColor: "#fff",
  },
  propertyAvatar: {
    width: 24,
    height: 18,
  },
  avatar: {
    width: theme.spacing(3.75),
    height: theme.spacing(3.75),
    marginRight: 12,
  },
  wrapperRow: {
    cursor: "pointer",
    textDecoration: "unset",
    overflow: "hidden",
    "& .MuiAvatar-root": {
      width: "30px",
      height: "30px",
    },
  },
  landlord: {
    "& p": {
      overflow: "hidden",
      maxWidth: "100px",
      whiteSpace: "nowrap",
      textOverflow: "ellipsis",
    },
  },
  action: {
    cursor: "pointer",
    color: "rgba(93, 81, 226, 1)",
  },
  status: {
    padding: theme.spacing(0.5, 0.5),
  },
  borderTop: {
    borderTop: `1px solid ${theme.palette.specialColors.grey[200]}`,
  },
  noDataWrapper: {
    position: "absolute",
    display: "grid",
    placeItems: "center",
    inset: "150px 0 0 0",
    color: theme.palette.specialColors.grey[400],
    rowGap: theme.spacing(1.25),
  },
}));
