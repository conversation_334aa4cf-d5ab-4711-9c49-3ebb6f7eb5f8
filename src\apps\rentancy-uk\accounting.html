<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <link crossorigin="use-credentials" rel="icon" href="/favicon.ico" />
  <link crossorigin="use-credentials" rel="manifest" href="/manifest.json" />
  <meta name="theme-color" content="#000000" />
  <meta name="description" content="LoftyWorks Accounting Management" />
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-touch-fullscreen" content="yes" />
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="application-name" content="LoftyWorks Accounting">
  <meta name="apple-mobile-web-app-title" content="LoftyWorks Accounting">
  <meta name="theme-color" content="#202437">
  <meta name="msapplication-navbutton-color" content="#202437">
  <meta name="apple-mobile-web-app-status-bar-style" content="default">
  <meta name="msapplication-starturl" content="/">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
  <title>LoftyWorks Accounting</title>
  <script crossorigin="anonymous" src="https://static.chimeroi.com/servicetool-temp/********/23/374bc506-600a-437b-a7b6-e3967b5510ac_fingerprint-prod-********.js"></script>
  <script crossorigin="anonymous" src="https://static.chimeroi.com/servicetool-temp/2025627/2/lw-production-menus-umd.js"></script>
</head>
<body>
  <noscript>You need to enable JavaScript to run this app.</noscript>
  <div id="root"></div>
  <div id="fb-root"></div>

  <script src="/src/accounting.tsx" type="module"></script>
  <script>
    window.fbAsyncInit = function () {
      window.FB.init({
        appId: '***************', // Replace with your Meta app ID
        autoLogAppEvents: true,
        xfbml: true,
        version: 'v21.0' // Latest version when this doc was published
      })
    };
  </script>
  <script async defer crossorigin="anonymous" src="https://connect.facebook.net/en_US/sdk.js"></script>
</body>
</html>
