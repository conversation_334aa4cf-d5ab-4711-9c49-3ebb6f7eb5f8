import { FC, useRef, lazy, useEffect, useState } from "react";
import { <PERSON><PERSON>, Card, CardHeader, CardContent, CardActions, Typography } from "@mui/material";
import { Button } from "@rentancy/ui";
import { styled } from "@mui/material/styles";
import { ChildForwardRef, LeftFormType, RightFormType } from "../types/ChildTypes";
import { LeftForm } from "./LeftPanel/LeftForm";
import { RightPanel } from "./RightPanel/RightPanel";
import { endTenancyMoveOut, MoveOutPayload } from "Api/EndOfTenancy/MoveOut";
import { Route, Routes, useLocation, useNavigate, useParams } from "react-router-dom";
import { getContract, useApi } from "@rentancy/common";
import { MoveOutContext } from "../hooks/MoveOutContext";
import { useSnackbar } from "notistack";
import { useTranslation } from "react-i18next";
import { getEotDetailByTenancyId } from "Api/EndOfTenancy";
import { getStatusFromMoveOutDate } from "./utils";

const MoveOutTaskPopup = lazy(() =>
  import("./components/Task/TaskPopup").then(({ MoveOutTaskPopup }) => ({
    default: MoveOutTaskPopup,
  })),
);

const StyledCard = styled(Card)(({ theme }) => ({
  "&.MuiCard-root": {
    display: "flex",
    flexDirection: "column",
    height: `calc(100% - ${theme.spacing(6)})`,
    margin: theme.spacing(2),
  },
  "& > .MuiCardContent-root": {
    flex: 1,
    overflow: "hidden",
  },
}));

export const MoveOutForm: FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { enqueueSnackbar } = useSnackbar();
  const { t } = useTranslation();
  const LeftFormRef = useRef<ChildForwardRef<LeftFormType>>(null);
  const RightFormRef =
    useRef<ChildForwardRef<RightFormType & { outstandingPayments: number }>>(null);
  const { tenancyId } = useParams<{ id: string; tenancyId: string }>();
  const { apiCall: getContractAPI, data } = useApi(getContract);
  const { apiCall: endTenancyMoveOutAPI } = useApi(endTenancyMoveOut);
  const { apiCall: getEotDetail, data: eotDetail } = useApi(getEotDetailByTenancyId);
  const [isValid, setIsValid] = useState<{ leftValid: boolean; rightValid: boolean }>({
    leftValid: true,
    rightValid: true,
  });
  const tempTask = useRef<{ id: string; name?: string; action: "add" | "remove" | "edit" }>();
  const submitForm: () => Promise<void> = async () => {
    const results = await Promise.all([
      LeftFormRef.current?.validate(),
      RightFormRef.current?.validate(),
    ]);
    if (results.some(result => result === false)) {
      return;
    }
    // TODO: all validation passed, then going on to submit all information to BE
    const [leftForm, rightForm] = [
      LeftFormRef.current?.getValues(),
      RightFormRef.current?.getValues(),
    ];
    const tenancy = data?.data?.getTenancy;
    // assembly the payload data for move-out
    const payload: MoveOutPayload = {
      id: tenancy?.id!,
      moveOutDate: leftForm?.moveOutDate!,
      organisationId: tenancy?.tenancyOrganisationId ?? "",
      status: getStatusFromMoveOutDate(leftForm?.moveOutDate ?? ""),
      primaryTenantId: tenancy?.primaryTenant?.id ?? "",
      tenantLetter: JSON.stringify({
        letterSubject: rightForm?.toTenant?.letterSubject ?? "",
        letterContent: rightForm?.toTenant?.letterContent ?? "",
        to: tenancy?.primaryTenant?.emails?.[0].email ?? "",
      }),
      landlordLetter: JSON.stringify({
        letterSubject: rightForm?.toLandlord?.letterSubject ?? "",
        letterContent: rightForm?.toLandlord?.letterContent ?? "",
        to: tenancy?.landlords?.[0].emails?.[0].email ?? "",
      }),
      outstandingPayments: rightForm?.outstandingPayments,
      primaryLandlordId: tenancy?.landlords?.[0].id ?? "",
      utilities: ["waterBill", "gasBill", "electricityBill"]
        .filter(item => {
          if (leftForm !== undefined) {
            // @ts-ignore
            return !isNaN(leftForm[item]);
          }
          return false;
        })
        .map(item => {
          // @ts-ignore
          return { name: item, metric: leftForm?.[item] };
        }),
      checkoutReports:
        leftForm?.checkoutReport?.map(item => {
          return {
            key: item.doc?.key ?? "",
            ...(item.doc?.name === undefined ? {} : { name: item.doc?.name }),
            ...(item.doc?.type === undefined ? {} : { type: item.doc?.type }),
            ...(item.doc?.status === undefined ? {} : { status: item.doc?.status }),
            ...(item.doc?.description === undefined ? {} : { description: item.doc?.description }),
            ...(item.doc?.mimeType === undefined ? {} : { mimeType: item.doc?.mimeType }),
            ...(item.doc?.archived === undefined ? {} : { archived: item.doc?.archived }),
            ...(item.doc?.expiry === undefined || item.doc?.expiry === null
              ? {}
              : { expiry: item.doc?.expiry }),
            ...(item.doc?.validFrom === undefined || item.doc?.validFrom === null
              ? {}
              : { validFrom: item.doc?.validFrom }),
            ...(item.doc?.done === undefined ? {} : { done: item.doc?.done }),
            ...(item.doc?.documentTenancyId === undefined
              ? {}
              : { documentTenancyId: item.doc?.documentTenancyId }),
            ...(tenancy?.property?.id === undefined
              ? {}
              : { documentPropertyId: tenancy?.property.id }),
            ...(item.doc?.documentUserId === undefined
              ? {}
              : { documentUserId: item.doc?.documentUserId }),
            ...(item.doc?.isParentPropertyDocumentShared === undefined
              ? {}
              : { isParentPropertyDocumentShared: item.doc?.isParentPropertyDocumentShared }),
          };
        }) ?? [],
    };
    endTenancyMoveOutAPI(payload).then(result => {
      if (result.data?.data?.moveOutTenancy === true) {
        enqueueSnackbar(t("moveOutCompleted"), { variant: "success" });
        navigate(
          {
            pathname: `/property/${tenancy?.property?.id}/contracts`,
            search: location.search,
          },
          {
            replace: true,
          },
        );
      }
    });
  };

  useEffect(() => {
    if (tenancyId) {
      getContractAPI(tenancyId);
      getEotDetail(tenancyId);
    }
  }, [tenancyId, getContractAPI, getEotDetail]);

  return (
    <MoveOutContext.Provider
      value={{
        eotDetail: eotDetail?.data ?? null,
        updateEOTDetail: () => void 0,
        tenancy: data?.data?.getTenancy ?? null,
        newTask: tempTask,
        ...isValid,
        setValidStatus: setIsValid,
      }}
    >
      <StyledCard
        sx={{
          width: "100%",
          height: "100%",
        }}
      >
        <CardHeader
          title={
            <Typography variant="subtitle1">
              {t("moveOut")}
              <Typography
                variant="caption"
                sx={theme => ({
                  marginLeft: theme.spacing(1),
                  color: theme.palette.text.secondary,
                })}
              >
                {t("moveOutTips")}
              </Typography>
            </Typography>
          }
          sx={theme => ({
            borderBottom: `1px solid ${theme.palette.divider}`,
            overflow: "hidden",
          })}
        />
        <CardContent sx={{ padding: 0 }}>
          <Grid xs={12} md={12} container sx={() => ({ height: "100%" })}>
            <Grid
              item
              md={9}
              sx={theme => ({
                padding: theme.spacing(2),
                borderRight: `1px solid ${theme.palette.divider}`,
                overflow: "auto",
                height: "100%",
              })}
            >
              <LeftForm ref={LeftFormRef} />
            </Grid>
            <Grid
              item
              md={3}
              sx={theme => ({
                padding: theme.spacing(2),
                overflow: "auto",
                height: "100%",
              })}
            >
              <RightPanel ref={RightFormRef} />
            </Grid>
          </Grid>
        </CardContent>
        <CardActions
          sx={theme => ({
            borderTop: `1px solid ${theme.palette.divider}`,
          })}
        >
          <Button
            disabled={!isValid.rightValid || !isValid.leftValid}
            variant="contained"
            onClick={submitForm}
            data-testid="move-out-complated-btn"
          >
            {`${t("confirm")}`}
          </Button>
        </CardActions>
        <Routes>
          <Route path="/task/:boardId/:taskId/?/*" element={<MoveOutTaskPopup />} />
        </Routes>
      </StyledCard>
    </MoveOutContext.Provider>
  );
};
