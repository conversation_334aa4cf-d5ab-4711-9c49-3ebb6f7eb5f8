import { Box, Tab, Tabs, Typography } from "@mui/material";
import { makeStyles } from "@mui/styles";
import { ReactNode, useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useFlag } from "@unleash/proxy-client-react";

interface AccountingTabs {
  labelKey: string;
  value: string;
}

const initAccountingTabs = (approvalsFlag: boolean): AccountingTabs[] => [
  {
    labelKey: "invoices",
    value: "/invoices",
  },
  {
    labelKey: "bills",
    value: "/bills",
  },
  {
    labelKey: "bank",
    value: "/bank",
  },
  {
    labelKey: "payout",
    value: "/payouts",
  },
  {
    labelKey: "arrears",
    value: "/arrears",
  },
  {
    labelKey: "chartofAccounts",
    value: "/chart?orderBy=code&orderDirection=desc",
  },
  ...(approvalsFlag
    ? [
        {
          labelKey: "approvals",
          value: "/approvals",
        },
        {
          labelKey: "paymentsHistory",
          value: "/payments-history",
        },
      ]
    : []),
];

interface Props {
  addNewBtn: () => ReactNode;
}

export const AccountingHeader = ({ addNewBtn }: Props) => {
  const approvalsFlag = useFlag("approvals");
  const [accountingTabs, setAccountingTabs] = useState<AccountingTabs[]>(
    initAccountingTabs(approvalsFlag),
  );
  const classes = useStyles();
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation("loftyPay");

  const currentTab = location.pathname;

  useEffect(() => {
    setAccountingTabs(initAccountingTabs(approvalsFlag));
  }, [approvalsFlag]);

  const handleTabChange = (_: React.SyntheticEvent, newValue: string) => {
    navigate(newValue);
  };

  return (
    <Box className={classes.root}>
      <Box className={classes.header}>
        <Typography variant="h4" className={classes.title}>
          {t("accounting")}
        </Typography>
        {addNewBtn()}
      </Box>
      <Tabs
        value={currentTab}
        onChange={handleTabChange}
        className={classes.tabs}
        variant="scrollable"
        scrollButtons="auto"
      >
        {accountingTabs.map((tab) => (
          <Tab
            key={tab.value}
            label={t(tab.labelKey)}
            value={tab.value}
            className={classes.tab}
          />
        ))}
      </Tabs>
    </Box>
  );
};

const useStyles = makeStyles((theme) => ({
  root: {
    backgroundColor: theme.palette.common.white,
    borderBottom: `1px solid ${theme.palette.divider}`,
  },
  header: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    padding: theme.spacing(2, 3),
  },
  title: {
    fontWeight: 600,
  },
  tabs: {
    paddingLeft: theme.spacing(3),
    "& .MuiTabs-indicator": {
      backgroundColor: theme.palette.primary.main,
    },
  },
  tab: {
    textTransform: "none",
    fontWeight: 500,
    fontSize: "0.875rem",
  },
}));
