import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  FormLabel,
  Grid,
  MenuItem,
  TextField,
  Typography,
  Tooltip,
  IconButton,
} from "@mui/material";
import { KeyboardDatePicker } from "@rentancy/ui";
import { Icon } from "@rentancy/icon";
import { typeOptions } from "Components/ProblemReport/functions/TenancyDetailsEdit";
import moment from "moment";
import { FC } from "react";
import { useTranslation } from "react-i18next";
import { ContractBreakClauseEdit } from "./BreakClause";
import { ContractSummaryEdit } from "./Summary";
import { TenancyFormik } from "../hooks/useEditContractForm";
import { YES_NO_INPUT_OPTIONS } from "@root/apps/rentancy-uk/src/Utils/financeBalanceUtils";
import { TENANCY_STATUS_LIST, TenancyUtils } from "@rentancy/common";
import { TenancyStatus } from "../../consts";

const REMINDER_SELECT_OPTINONS = [
  { value: 1, label: "1 day" },
  { value: 2, label: "2 days" },
  { value: 3, label: "3 days" },
  { value: 4, label: "4 days" },
  { value: 5, label: "5 days" },
  { value: 6, label: "6 days" },
  { value: 7, label: "1 week" },
  { value: 14, label: "2 week" },
];

export const DetailsPanel: FC<TenancyFormik> = ({ formik }) => {
  const { values, handleBlur, handleChange, errors, touched, setFieldValue } = formik;

  const isPeriodic = values.status === TenancyStatus.PERIODIC;

  const { t } = useTranslation();
  return (
    <>
      <Typography fontWeight="bold">{t("details")}</Typography>
      <Grid container rowSpacing={1} columnSpacing={2.5}>
        <Grid item xs={6}>
          <FormLabel component="p">{t("title")}</FormLabel>
          <TextField
            fullWidth
            placeholder={t("enterTitle")}
            name="title"
            size="small"
            value={values.title || ""}
            onChange={handleChange}
            variant="outlined"
            inputProps={{ "data-testid": "contract-title-input" }}
          />
        </Grid>
        <Grid item xs={6}>
          <FormLabel component="p">{t("contractReference")}</FormLabel>
          <TextField
            variant="outlined"
            size="small"
            name="reference"
            placeholder={t("enterReference")}
            error={Boolean(errors.reference && touched.reference)}
            helperText={errors.reference && touched.reference ? errors.reference : null}
            fullWidth
            onBlur={handleBlur}
            onChange={handleChange}
            value={values.reference || ""}
            inputProps={{ "data-testid": "contract-reference-input" }}
          />
        </Grid>
        <Grid item xs={6}>
          <FormLabel required component="p">
            {t("type")}
          </FormLabel>
          <TextField
            select
            size="small"
            name="type"
            variant="outlined"
            fullWidth
            disabled
            value={values.type || ""}
            onChange={handleChange}
            inputProps={{ "aria-label": "type", "data-testid": "contract-type-input" }}
          >
            {typeOptions.map((item, key) => (
              <MenuItem value={item.value} key={key}>
                {item.label}
              </MenuItem>
            ))}
          </TextField>
        </Grid>
        <Grid item xs={6}>
          <FormLabel required component="p">
            {t("status")}
          </FormLabel>
          <TextField
            name="status"
            id="status"
            select
            placeholder={t("select")}
            size="small"
            variant="outlined"
            onBlur={handleBlur}
            value={values.status || ""}
            onChange={value => setFieldValue("status", value.target.value)}
            error={Boolean(errors.status && touched.status)}
            helperText={errors.status && touched.status ? t("required") : ""}
            fullWidth
            data-testid="contract-edit-status-input"
          >
            {TENANCY_STATUS_LIST.map((item, key) => {
              return (
                <MenuItem
                  key={item.value}
                  onClick={() => {}}
                  value={item.value}
                  data-testid={`property-contract-status-item-${key}`}
                >
                  <Chip
                    size="small"
                    variant="outlined"
                    label={item.text}
                    style={TenancyUtils.getChip(item.value)}
                  />
                </MenuItem>
              );
            })}
          </TextField>
        </Grid>
        <Grid item xs={6}>
          <FormLabel required component="p">
            {t("startDate")}
          </FormLabel>
          <KeyboardDatePicker
            name="startDate"
            id="startDate"
            inputFormat="DD MMM YYYY"
            placeholder={t("select")}
            size="small"
            inputVariant="outlined"
            disableMaskedInput
            onBlur={handleBlur}
            value={values.startDate?.toString() || ""}
            onChange={value => setFieldValue("startDate", value ?? null)}
            error={Boolean(
              (errors.startDate && touched.startDate) ||
                (values.startDate && !moment(values.startDate).isValid()),
            )}
            helperText={errors.startDate && touched.startDate ? t("required") : ""}
            fullWidth
            data-testid="contract-edit-start-date-input"
          />
        </Grid>
        <Grid item xs={6}>
          <FormLabel required component="p">
            {t("endDate")}
          </FormLabel>
          <KeyboardDatePicker
            name="endDate"
            id="endDate"
            inputFormat="DD MMM YYYY"
            placeholder={t("select")}
            size="small"
            inputVariant="outlined"
            disableMaskedInput
            value={values.endDate?.toString()}
            onBlur={handleBlur}
            onChange={value => setFieldValue("endDate", value ?? null)}
            error={Boolean(
              (errors.endDate && touched.endDate) ||
                (values.endDate && !moment(values.endDate).isValid()),
            )}
            helperText={errors.endDate && touched.endDate ? t("invalidDate") : ""}
            fullWidth
            data-testid="contract-edit-end-date-input"
          />
        </Grid>
        <Grid item xs={6}>
          <FormLabel component="p">{t("rentGuaranteeRequired")}</FormLabel>
          <TextField
            id="outlined-select-currency-native"
            select
            name="isRentGuaranteeRequired"
            size="small"
            value={values.isRentGuaranteeRequired?.toString()}
            fullWidth
            onChange={e => {
              setFieldValue("isRentGuaranteeRequired", e.target.value === "true");
            }}
          >
            {YES_NO_INPUT_OPTIONS.map(item => (
              <MenuItem key={item.label} value={item.value.toString()}>
                {item.label}
              </MenuItem>
            ))}
          </TextField>
        </Grid>
        {isPeriodic && (
          <Grid item xs={6}>
            <FormLabel component="p" style={{ margin: 0 }}>
              {t("reminder")}
              <Tooltip title={t("reminderTip")} arrow>
                <IconButton>
                  <Icon name="denial-reason" size={14} />
                </IconButton>
              </Tooltip>
            </FormLabel>
            <TextField
              id="outlined-select-currency-native"
              select
              name="reminderDays"
              size="small"
              value={values.reminderDays}
              fullWidth
              onChange={e => {
                setFieldValue("reminderDays", e.target.value);
              }}
            >
              {REMINDER_SELECT_OPTINONS.map(item => (
                <MenuItem key={item.label} value={item.value}>
                  {item.label}
                </MenuItem>
              ))}
            </TextField>
          </Grid>
        )}
      </Grid>

      <Divider sx={theme => ({ margin: theme.spacing(3.75, 0) })} />
      <ContractBreakClauseEdit formik={formik} />
      <Divider sx={{ marginBottom: 3.75, marginTop: 2 }} />
      <ContractSummaryEdit formik={formik} />
      <Grid item xs={6}>
        <FormLabel component="p">{t("")}</FormLabel>
      </Grid>
    </>
  );
};
