import { Theme, Tooltip } from "@mui/material";
import { makeStyles } from "@mui/styles";
import { useTranslation } from "react-i18next";
import { TaskCategory } from "../types";
import { ApplicationTask } from "Api/Application/types";

type TaskLabelProps = {
  tasks: ApplicationTask[];
  category: TaskCategory;
};

const useStyles = makeStyles((theme: Theme) => ({
  tooltip: {
    backgroundColor: theme.palette.specialColors.grey[800],
    fontSize: 12,
    color: theme.palette.specialColors.grey[400],
    padding: theme.spacing(1, 4),
  },
  arrow: {
    "&::before": {
      backgroundColor: theme.palette.specialColors.grey[800],
    },
  },
  label: {
    padding: theme.spacing(0.5, 1.25),
    lineHeight: theme.spacing(2.5),
    borderRadius: theme.spacing(3.75),
  },
  tooltipList: {
    padding: 0,
    margin: 0,
    listStyle: "none",
  },
  tooltipListItem: {
    lineHeight: theme.spacing(2.5),
  },
}));

export const TaskLabel = ({ tasks, category }: TaskLabelProps) => {
  const classes = useStyles();
  const { t } = useTranslation();
  const currentTask = tasks.filter(task => task?.category === category);
  const completed = currentTask?.every(task => task?.status === "Completed");

  const pendingTask = currentTask?.filter(task => task?.status === "Todo");
  const failed = currentTask?.every(task => task?.status === "Failed");
  const tasksName = pendingTask?.map(task => task?.name);

  if (completed) {
    return <span className={classes.label}>{t("Completed")}</span>;
  } else if (pendingTask.length > 0) {
    return (
      <Tooltip
        arrow
        classes={{ tooltip: classes.tooltip, arrow: classes.arrow }}
        title={
          <ul className={classes.tooltipList}>
            {tasksName.map((taskName, index) => (
              <li key={index} className={classes.tooltipListItem}>
                {taskName}
              </li>
            ))}
          </ul>
        }
        placement="bottom"
      >
        <span className={classes.label}>
          {pendingTask.length} {t("To-Dos")}
        </span>
      </Tooltip>
    );
  } else if (failed) {
    return "--";
  }
  return <span className={classes.label}>0 {t("To-Dos")}</span>;
};
