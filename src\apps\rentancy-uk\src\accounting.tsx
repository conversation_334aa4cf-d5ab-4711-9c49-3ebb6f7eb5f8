import "./i18n";
import "@rentancy/common/src/config/sentryConfig";
import { AccountingApp } from "Containers/Accounting/AccountingApp";
import { MuiProvider } from "Containers/App/MuiProvider";
import { createRoot } from "react-dom/client";
import { FlagProvider } from "@unleash/proxy-client-react";
import * as Sentry from "@sentry/react";

import "@rentancy/common/src/assets/styles/index.css";
import { NotFound } from "@rentancy/common";

/**
 * This is a workaround of how amplify works.
 *
 * In short, it tries to parse "code" query parameter and treat it as its own authentication code.
 *
 * In more details, see: https://github.com/aws-amplify/amplify-js/issues/9208
 */
// todo: remove this part
const qs = window.location.search;
if (window.location.pathname.indexOf("settings/integrations") > -1 && qs.indexOf("code=") > -1) {
  window.location.search = qs.replace("code", "integrationCode");
}

const config = {
  url: process.env.UNLEASH_URL || "",
  clientKey: process.env.UNLEASH_CLIENT_KEY || "",
  refreshInterval: 600,
  appName: process.env.UNLEASH_APP_NAME || "",
};

const container = document.getElementById("root");
const root = createRoot(container!);
root.render(
  <MuiProvider>
    <FlagProvider config={config}>
      <Sentry.ErrorBoundary fallback={<NotFound />} showDialog>
        <AccountingApp />
      </Sentry.ErrorBoundary>
    </FlagProvider>
  </MuiProvider>,
);
