import { Grid } from "@mui/material";
import { makeStyles } from "@mui/styles";
import { rentancyColors } from "@rentancy/common";

type StatusConfig = {
  label: string;
  color: string;
};

const STATUS_ENUM: Record<string, StatusConfig> = {
  Completed: {
    label: "Completed",
    color: rentancyColors.completeColor,
  },
  Pending: {
    label: "Pending",
    color: rentancyColors.pendingColor,
  },
  Cancelled: {
    label: "Cancelled",
    color: rentancyColors.cancelledColor,
  },
};

type LabelProps = {
  status: keyof typeof STATUS_ENUM;
};

const useStyles = makeStyles(theme => ({
  statusDot: {
    width: theme.spacing(1),
    height: theme.spacing(1),
    borderRadius: "50%",
    backgroundColor: (props: { color: string }) => props.color,
  },
  statusLabel: {
    padding: theme.spacing(0.5, 1.25),
    lineHeight: "20px",
    borderRadius: theme.shape.borderRadius * 4,
    color: rentancyColors.statusLabelColor,
  },
}));

export const StatusLabel = ({ status }: LabelProps) => {
  const statusConfig = STATUS_ENUM[status];
  const classes = useStyles({ color: statusConfig?.color });

  if (!statusConfig) {
    console.warn(`Unknown status: ${status}`);
    return null;
  }

  return (
    <Grid container justifyContent="center" alignItems="center">
      <span className={classes.statusDot} />
      <span className={classes.statusLabel}>{statusConfig.label}</span>
    </Grid>
  );
};
