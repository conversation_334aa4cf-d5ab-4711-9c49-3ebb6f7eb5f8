import { Box, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";

export const NoAccess = () => {
  const { t } = useTranslation("loftyPay");

  return (
    <Box
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      height="100vh"
      textAlign="center"
    >
      <Typography variant="h4" gutterBottom>
        {t("noAccess")}
      </Typography>
      <Typography variant="body1" color="textSecondary">
        {t("noAccessDescription")}
      </Typography>
    </Box>
  );
};
