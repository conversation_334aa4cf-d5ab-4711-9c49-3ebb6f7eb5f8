import AddIcon from "@mui/icons-material/Add";
import { Box, Theme, Tooltip, Typography, alpha, useTheme } from "@mui/material";
import { Button } from "@rentancy/ui";
import {
  HtmlHeadTitles,
  IntegrationStatusEnum,
  IntegrationType,
  OrganisationDataProvider,
  getIntegrationStatus,
  isNewCheckbookPromptsAvailable,
  useApi,
  useUser,
} from "@rentancy/common";
import { Icon } from "@rentancy/icon";
import { Route, Routes, useLocation, useNavigate } from "react-router-dom";
import { makeStyles } from "@mui/styles";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useSnackbar } from "notistack";
import { useTranslation } from "react-i18next";
import { useFlag } from "@unleash/proxy-client-react";

// Import accounting components (these will be created later)
import { AccountingHeader } from "../components/AccountingHeader";
import { Arrears } from "../Arrears/Arrears";
import { Bank } from "../Reconciliation/Bank";
import { Bill } from "../Bill/Bill";
import { ConnectionReceive } from "../Reconciliation/Bank/ConnectionReceive";
import { DepositManagement } from "../DepositManagement";
import { HtmlHead } from "../../../Components/HtmlHead";
import { Invoice } from "../Invoice/Invoice";
import { Ledger } from "../Ledger/LedgerWrapper";
import { NoAccess } from "../components/NoAccess";
import { Payouts } from "../Payouts";
import { SuspendedTransactions } from "../SuspendedTransactions/SuspendedTransactions";
import { isNoBackgroundLocation } from "../Utils/util";
import { useChargePlatformDebtItem } from "../Dialogs/ProcessPaymentModal/hooks/useChargePlatformDebtItem";
import { listBankAccounts, createCashAccount } from "../../../api/accounting";
import { Approvals } from "../Approvals";
import { PaymentsHistory } from "../PaymentsHistory/PaymentsHistory";

const tabsWithoutButton = ["chart", "suspended", "ledger"];

export const AccountingLayout = () => {
  const [hasAccess] = useState(true);
  const navigate = useNavigate();
  const { t } = useTranslation("loftyPay");
  const theme = useTheme();
  const location = useLocation();
  const locationName = useLocation().pathname;
  const { enqueueSnackbar } = useSnackbar();
  const approvalsFlag = useFlag("approvals");

  const noBackground = useMemo(() => isNoBackgroundLocation(locationName), [locationName]);
  const classes = useStyles({ noBackground });

  const {
    apiCall: getBankData,
    data: bankData,
    loading: isBankDataLoading,
    refetch,
  } = useApi(listBankAccounts);

  const { apiCall: createCashAccountApi } = useApi(createCashAccount);

  const onCreateAccount = useCallback(async () => {
    const { data } = await createCashAccountApi();

    if (data?.ok) {
      enqueueSnackbar(t("newCashAccountCreatedSuccessfully"), { variant: "success" });
      refetch?.();
    }
  }, [createCashAccountApi, enqueueSnackbar, t, refetch]);

  const tab = useMemo(() => locationName.split("/")[1] || "invoices", [locationName]);

  const navigatePathBackground = useCallback(
    (pathname: string) => navigate({ pathname }, { state: { background: location } }),
    [location, navigate],
  );

  const buttonsTexts = useMemo(
    () => ({
      invoices: t("newCharge"),
      bills: t("createInvoice"),
      reconciliation: t("newBankReconciliation"),
    }),
    [t],
  );

  const navigateTo = useMemo(
    () => ({
      charts: () => navigatePathBackground("/newAccount"),
      invoices: () => navigatePathBackground("/createInvoice"),
      bills: () => navigatePathBackground("/newBill"),
      arrears: () =>
        navigate(
          {
            pathname: "/arrears",
            search: "?type=global",
          },
          { state: { background: location } },
        ),
      bank: () => onCreateAccount(),
    }),
    [navigate, location, onCreateAccount, navigatePathBackground],
  );

  const btnText = useMemo(() => {
    return buttonsTexts[tab as keyof typeof buttonsTexts] || "";
  }, [tab, buttonsTexts]);

  const {
    user: { organisationId },
  } = useUser();

  const { checkCardAvailability, isCheckingCardAvailability } = useChargePlatformDebtItem();
  const [checkbookConnected, setCheckbookConnected] = useState(false);

  const handleOpenNew = useCallback(
    () => navigateTo[tab as keyof typeof navigateTo]?.(),
    [tab, navigateTo],
  );

  const skipAddNewButton = useMemo(
    () => ["/arrears", "/payouts"].some(p => locationName.includes(p)),
    [locationName],
  );

  const onCheckAvailability = useCallback(async () => {
    if (!isNewCheckbookPromptsAvailable) {
      return;
    }
    await checkCardAvailability(() => navigatePathBackground("/processPayment"));
  }, [isNewCheckbookPromptsAvailable, checkCardAvailability, navigatePathBackground]);

  const isButtonVisible = useMemo(() => {
    if (tabsWithoutButton.includes(tab)) {
      return false;
    }

    if (tab === "bank") {
      return bankData?.every(({ type }) => type !== "CASH");
    }
    return true;
  }, [bankData, tab]);

  const showProcessPayment = false;

  useEffect(() => {
    const tabGetMap = new Map<string, () => void>([
      ["bank", getBankData],
      ["invoices", getBankData],
    ]);

    tabGetMap.get(tab)?.();
  }, [tab, getBankData]);

  useEffect(() => {
    getIntegrationStatus(organisationId, IntegrationType.CHECKBOOK).then(res => {
      const status = res?.data?.getIntegrationStatus?.status;
      setCheckbookConnected(status === IntegrationStatusEnum.CONNECTED);
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (!hasAccess) {
    return <NoAccess />;
  }

  return (
    <Box className={classes.root}>
      <OrganisationDataProvider>
        <HtmlHead title={HtmlHeadTitles.ACCOUNTING} />
        <AccountingHeader
          addNewBtn={() => {
            if (skipAddNewButton) {
              return null;
            }

            return (
              <Box>
                {showProcessPayment && (
                  <Tooltip
                    title={checkbookConnected ? "" : t("pleaseIntegrateWithCheckbookInSettings")}
                  >
                    <Box component="span">
                      <Button
                        disabled={!checkbookConnected}
                        loading={isCheckingCardAvailability}
                        variant="contained"
                        sx={{ marginRight: 1.25, height: theme.spacing(5) }}
                        onClick={onCheckAvailability}
                      >
                        <Box>
                          <Typography sx={{ lineHeight: theme.spacing(2) }}>
                            {t("processPayment")}
                          </Typography>
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              height: theme.spacing(1.5),
                              fontSize: theme.spacing(1),
                              lineHeight: theme.spacing(1.5),
                            }}
                          >
                            <div>{t("poweredBy")}</div>
                            <Icon
                              name="checkbook-logo-icon"
                              color={
                                checkbookConnected
                                  ? theme.palette.common.white
                                  : alpha(theme.palette.common.white, 0.5)
                              }
                              size={8}
                              style={{ marginLeft: theme.spacing(0.375) }}
                            />
                            <div>{t("checkbook")}</div>
                          </Box>
                        </Box>
                      </Button>
                    </Box>
                  </Tooltip>
                )}
                {isButtonVisible && btnText && (
                  <Button
                    onClick={handleOpenNew}
                    startIcon={<AddIcon />}
                    variant="contained"
                    color="secondary"
                    sx={{ height: theme.spacing(5) }}
                    data-testid="accounting_new_item_btn"
                  >
                    {btnText}
                  </Button>
                )}
              </Box>
            );
          }}
        />
      </OrganisationDataProvider>
      <div className={classes.content}>
        <Routes>
          <Route path="/" element={<Invoice bankData={bankData} />} />
          <Route path="/invoices" element={<Invoice bankData={bankData} />} />
          <Route path="/bills" element={<Bill />} />
          <Route
            path="/bank"
            element={<Bank data={bankData} loading={isBankDataLoading} refetch={refetch} />}
          />
          <Route path="/ledger" element={<Ledger />} />
          <Route path="/payouts" element={<Payouts />} />
          <Route path="/arrears" element={<Arrears />} />
          <Route path="/bank/success" element={<ConnectionReceive />} />
          <Route path="/suspended" element={<SuspendedTransactions />} />
          <Route path="/deposit-management" element={<DepositManagement />} />
          {approvalsFlag && (
            <>
              <Route path="/approvals" element={<Approvals />} />
              <Route path="/payments-history" element={<PaymentsHistory />} />
            </>
          )}
        </Routes>
      </div>
    </Box>
  );
};

interface StyleProps {
  noBackground: boolean;
}

const useStyles = makeStyles((theme: Theme) => ({
  root: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    background: theme.palette.specialColors.grey[100],
  },
  content: {
    flex: 1,
    overflow: "hidden",
    display: "flex",
    flexDirection: "column",
    margin: ({ noBackground }: StyleProps) =>
      noBackground ? theme.spacing(0) : theme.spacing(2.5),
    background: ({ noBackground }: StyleProps) =>
      noBackground ? "none" : theme.palette.common.white,
    border: ({ noBackground }: StyleProps) =>
      noBackground ? "none" : `1px solid ${theme.palette.specialColors.themeBorder}`,
    borderRadius: theme.shape.borderRadius,
  },
}));
