import { Box, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";

interface Props {
  bankData?: any[];
}

export const Invoice = ({ bankData }: Props) => {
  const { t } = useTranslation("loftyPay");

  return (
    <Box p={3}>
      <Typography variant="h5" gutterBottom>
        {t("invoices")}
      </Typography>
      <Typography variant="body1" color="textSecondary">
        Invoice management functionality will be implemented here.
      </Typography>
      {bankData && (
        <Typography variant="body2" sx={{ mt: 2 }}>
          Bank accounts connected: {bankData.length}
        </Typography>
      )}
    </Box>
  );
};
