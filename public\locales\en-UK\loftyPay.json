{"account": "Account", "accountName": "Account name", "accounting": "Accounting", "action": "Action", "addTransaction": "Add Transaction", "address": "Address", "allocateFunds": "Allocate funds", "allocateToLedger": "Allocate to <PERSON><PERSON>", "amount": "Amount", "netAmount": "Net Amount", "amountDue": "Amount Due", "amountMustMatch": "Amount must match", "amountPaid": "Amount <PERSON>", "arrears": "Arrears", "arrearsAmount": "Arrears Amount", "balance": "Balance", "balanceInRentancy": "Balance in LoftyPay", "bank": "Bank", "bankAccountError": "An error occurred while retrieving bank account.", "bankAccountSummary": "Bank Account Summary", "bankReconcileError": "An error occurred while retrieving bank reconcile statements.", "bankStatement": "Bank Statement", "bankStatementError": "An error occurred while retrieving bank statements.", "invoices": "Invoices", "invoiceDate": "Invoice Date", "cancel": "Cancel", "chartofAccounts": "Chart Of Accounts", "depositManagement": "Deposit Management", "code": "Code", "complete": "Complete", "connectYourBank": "Connect your bank", "contractReference": "Tenancy Reference", "contracts": "Tenancy", "confirmationAllocate": "Allocate funds confirmation", "createCharge": "Create Charge", "updateCharge": "Update Charge", "createInvoice": "Create Invoice", "creditorName": "Creditor name", "currency": "<PERSON><PERSON><PERSON><PERSON>", "currencyRule": "Currency: USD or GBP", "clientBalance": "Client Balance", "date": "Date", "daysInArrears": "Days In Arrears", "debtorName": "Debtor name", "description": "Description", "delete": "Delete", "deleteBankStatementConfirmMsg": "Do you want to delete the transaction? You only delete the transaction when it is a repetitive bank transaction that has been imported to system.", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "dismiss": "<PERSON><PERSON><PERSON>", "disconnectYourBank": "Disconnect your bank", "downloadTemplate": "Download Template", "downloadYourBankTransactionsAndUploadFiles": "Download your bank transactions and upload the .CSV, .XLS, or .XLSX here to manually reconcile your finances. A CSV template is available for you to download below.", "DragFilesOrClickHereToUpload": " Drag file or click here to upload", "dueDate": "Due Date", "endDate": "End Date", "enterAmount": "Enter Amount", "enterDescription": "Enter Description", "findMore": "Find & Match", "float": "Float", "frequency": "Frequency", "grossAmount": "Gross Amount", "importTransactions": "Import Bank Statement", "importTransactionsError": "An error occurred while importing bank transaction.", "chargeDate": "Charge Date", "charges": "Charges", "landlord": "Landlord", "lastUpdate": "Last update", "lineItems": "Line Items", "ledger": "Ledger", "matchPageActionTipsLine1": "The bank statement line you had reconciled is kept. But you'll need to reconcile it again.", "matchPageActionTipsLine2": "The bank statement line is deleted from system, you have to import the bank statement line again.", "matchPageActionTipsLine3": "Both undo and delete actions make linked LoftyWorks transaction unreconciled.", "matchPageActionTipsLine4": "When you undo reconciliation for an unreconciled bank statement line, nothing will happen.", "MaxFileSize100Mb": "Max file size: 100Mb.", "missingBankDetailsAddSortCodeAndAccountNumber": "Missing bank details. Add sort code and account number", "moreDetail": "More details", "newBankReconciliation": "New Bank Reconciliation", "newCharge": "New Charge", "next": "Next", "noRecords": "No records…", "numberOnDaysUntilDue": "Number on days until due", "outstanding": "Outstanding", "paid": "Paid", "payee": "Payee", "payer": "Payer", "paymentMethod": "Payment method", "pending": "Pending", "payout": "Payout", "prepareFileForUpload": "Prepare file for upload", "property": "Property", "propertyName": "Property name", "noPropertiesToChose": "No properties to chose", "received": "Received", "reference": "Reference", "remaining": "Remaining", "rentancyBalance": "LoftyPay Balance", "rentancyTransactions": "LoftyPay Transactions", "reconciliationReport": "Reconciliation Report", "reconcile": "Reconcile", "saveForLater": "Save For Later", "selectAContactToCreateInvoice": "Select a Contact to Create Invoice", "selectATenantToCreateCharge": "Select a Tenant to Create Charge", "selectDifferentAccountOrCreateNewOne": "Please select a different account or create a new one.", "selected": "Selected", "selectMethod": "Select Method", "selectLedger": "Select Ledger", "selectTransaction": "Select Transaction", "searchByContactName": "Search by contact name", "statementBalance": "Statement Balance", "statementDateRule": "Date: only DD/MM/YYYY format.", "statementAmountRule": "Amount: 100.00 format.", "statementDescriptionRule": "Description: 200 characters Max.", "statementDetails": "Statement Details", "statementImportRuleHoverLine1": "Please follow the template’s rule (on the header), There are limitation and some rules in characters inputting in each cell.", "statementPayeeRule": "Payee: 40 characters Max.", "statementReferenceRule": "Reference: 30 characters Max.", "startDate": "Start date", "startReconciliation": "Start Reconciliation", "subType": "Sub type", "successfullyConnectedBank": "Successfully Connected Bank", "successfullyCreatedBill": "Successfully Created Bill", "successfullyCreatedInvoice": "Successfully Created Invoice", "successfullyDeletedInvoice": "Successfully deleted Invoice", "summaryOfTransactions": "Summary of transactions", "supportedFormatsSpreadsheet": "Supported formats: Spreadsheet (.CSV, .XLS, .XLSX)", "systemAccountsCannotBeChanged": "System accounts cannot be changed", "thereIsNo": "There is no {{type}}", "total": "Total", "totalInvoice": "Total Invoice", "totalItemsSelected": "Total items selected", "transaction": "Transaction", "transactionAmount": "Transaction Amount", "transactionAreReadyToImport": "transaction(s) are ready to import", "transactionAreUnableToRecognized": "transaction(s) are unable to recognized", "transactionDate": "Transaction date", "transactionType": "Transaction Type", "type": "Type", "undoReconcile": "Undo reconcile", "undoReconciliation": "Undo reconciliation", "unfundedItems": "Unfunded Items", "updateInvoice": "Update Invoice", "uploadBankStatement": "Upload Bank Statement", "viewBankStatement": "View Bank Statement", "viewRentancyTransaction": "View LoftyPay Transaction", "viewReport": "View Report", "toChangeInfoClickEditOrClickSaveToContinue": "To change information click edit, or click “Save” to continue. ", "deleteInvoiceConfirmText": "Are you sure you want to delete this Invoice?", "deleteApplicationYes": "Yes, Delete it", "updateBill": "Update Bill", "successfullyUpdatedBill": "Successfully Updated Bill", "bankFeed": "Bank Feed", "conenctToGoCardless": "Connect to GoCardless", "connectToGoCardlessInfo": "Link to your bank account to easily reconcile. <br /> Please setup your GoCardless connection to begin.", "edit": "Edit", "moveToSuspense": "Move to suspense", "whatsThis": "What's this", "whatIsThisHover": "Once you think your bank statement on the left side totally match LoftyPay's transaction, click OK. This transaction will be gone, and marked as reconciled.", "allocateToTenantLedger": "Allocate To Tenant Ledger:", "receivePayment": "Receive Payment", "amountReceived": "Amount Received", "outstandingTransaction": "Outstanding Transaction", "selectATenantToRecordPayment": "Select a Tenant to record payment", "thisFieldIsRequired": "This field is required", "createCashAccount": "Create Cash Account", "successfullyUpdatedCharge": "Successfully Updated Charge", "matchTransaction": "Match Transaction", "fromAmount": "From Amount", "toAmount": "To Amount", "clear": "Clear", "search": "Search", "allocate": "Allocate", "amountToReconcile": "Amount to <PERSON><PERSON><PERSON><PERSON>", "suspendedTransactions": "Suspended Transactions", "dateReceived": "Data Received", "searchDescription": "Search description", "success": "Success", "ledgerBalances": "Ledger Balances", "from": "From", "to": "To", "amountToAllocateToTenantBalance": "Amount to Allocate to Tenant Balance", "connected": "Connected", "notConnected": "Not Connected", "allocatedSuccessfully": "Allocated Successfully", "expenses": "Expenses", "lastPayout": "Last Payout", "active": "Active", "searchPropertyAddress": "Search Property Address", "create": "Create", "newCashAccountCreatedSuccessfully": "New cash account created successfully", "balanceCarryForward": "Balance Carry Forward", "incomes": "Incomes", "expenditure": "Expenditure", "proceed": "Proceed", "connectionPending": "Connection Pending", "paymentRunConfirmation": "Payment Run Confirmation", "recipientName": "Recipient Name", "landlordPayoutStatement": "Landlord Payout Statement", "paymentRun": "Payment Run", "landlordIncome": "Landlord Income", "totalPayout": "Total Payout", "preview": "Preview", "totalBalancePayable": "Total Balance Payable", "balanceCarriedForward": "Balance Carried Forward", "createdAt": "Created At", "executedBy": "Executed By", "paymentId": "Payment ID", "recipientType": "Recipient Type", "sortCode": "Sort Code", "bankAccount": "Bank Account", "amountPaidPounds": "Amount Paid (£)", "newPayoutCreatedSuccessfully": "New payout created successfully", "tenantBalance": "Tenant balance", "payImmediatelyFromTenantBalance": "Pay Immediately from Tenant Balance", "vatRate": "VAT Rate", "vatAmount": "VAT Amount", "addLineItems": "+ Add Line Item", "save": "Save", "successfullyPaidCharge": "Successfully Paid Charge", "transactionDetails": "Transaction Details", "newBill": "New Charge", "undoTheMatch": "Undo the match", "successfullyUndoTheMatch": "The match has been successfully undone", "somePaymentsHaveFailed": "Some payments have failed", "errorCode": "Error code", "paymentsPassedSuccessfully": "Payments Passed Successfully", "approvals": "Approvals", "approve": "Approve", "reject": "Reject", "payoutApproved": "Payout approved", "payoutRejected": "Payout rejected", "paymentDateTime": "Payment Date Time", "accountNumber": "Account Number", "paymentType": "Payment Type", "net": "Net", "vat": "VAT", "tax": "Tax", "gross": "Gross", "accepted": "Accepted", "sendToLandlord": "Send to Landlord", "landlordPayoutStatementSent": "Landlord Payout Statement sent", "yourReportWillBeSentViaEmail": "Your report will be sent via email. Please check your spam folder.", "paymentsHistory": "Payments History", "paymentRunDateTime": "Payment Run Date Time", "paymentRunStatus": "Payment Run Status", "paymentStatus": "Payment Status", "initiator": "Initiator", "carryForward": "Carry Forward", "completed": "Completed", "processing": "Processing", "failed": "Failed", "chargeDeletedSuccessfully": "Charge Deleted Successfully", "unpaid": "Unpaid", "partiallyPaid": "Partially Paid", "reconciled": "Reconciled", "partiallyReconciled": "Partially Reconciled", "overdue": "Overdue", "approved": "Approved", "autoRejected": "Auto Rejected", "userCantApproveOwnPayout": "You can't approve your own payout", "userDoesNotHavePermission": "You don't have permission to approve this payout", "all": "All", "approvedBy": "Approved By", "addApplicant": "Add Applicant", "editApplicant": "Edit Applicant", "propertyAddress": "Property Address", "holdingFee": "Holding Fee", "addedOn": "Added On", "guarantors": "Guarantors", "createTenancy": "Create Tenancy", "successfullyUpdatedApplicant": "Successfully updated Applicant", "passed": "Passed", "rejected": "Rejected", "passedWithGuarantor": "Passed with <PERSON><PERSON><PERSON><PERSON>", "passedWithJointTenancy": "Passed with Joint Tenancy", "deleteApplicant": "Delete Applicant", "youAreGoingRemoveAllActivityAndRecordsRelatedToThisApplicant": "You are going to remove all activity and records related to this applicant.", "successfullyDeletedApplicant": "Successfully deleted applicant", "agencyName": "Agency name", "alreadyHaveaLoftyWorksAccount": "Already have a LoftyWorks account?", "branch": "Branch (if applicable)", "checkYourEmail": "Check Your Email", "confirmPassword": "Confirm Password", "contactNumber": "Contact Number", "createAccount": "Create Account", "dontHaveAnAccount": "Don't have an account?", "email": "Email", "emailIsInvalid": "<PERSON><PERSON> is invalid", "enterEmail": "<PERSON><PERSON>", "enterName": "Enter Name", "enterNumber": "Enter Number", "enterPassword": "Enter Password", "enterWorkEmail": "Your Work Email Address", "exampleTranslation": "Example translation", "exampleTranslation2": "Example translation 2", "firstName": "First Name", "forgotPassword": "Forgot Password", "iAgreeToThe": "I agree to the", "lastName": "Last name", "login": "<PERSON><PERSON>", "newsletter": "Subscribe to our newsletter, unsubscribe any moment", "password": "Password", "passwordMustBeAtLeast_8Characters": "Password needs at least 8 chars", "passwordsDoNotMatch": "Passwords do not match", "phoneNumber": "Phone number", "pleaseCheckYourEmail": "Please check your email", "rememberPassword": "Remember Password", "resendCode": "Resend Code", "signUp": "Sign Up", "signUpNow": "Sign Up Now!", "weSentACodeTo": "We sent a code to", "weWereUnableToSendTheVerificationCodePleaseTryAgain": "We were unable to send the verification code. Please try again", "welcomeBack": "Welcome Back", "sendEmail": "Send Email", "passwordIsRequired": "Password is required", "confirmPasswordIsRequired": "Confirm password is required", "codeIsRequired": "Code is required", "forgotPasswordUserNotFoundErr": "User not found.", "somethingWentWrongPleaseTryAgainLater": "Something went wrong. Please try again later", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "resetPassword": "Reset Password", "back": "Back", "workspaceName": "Workspace Name", "newWorkspace": "New Workspace", "workspaceWithSuchNameAlreadyExists": "Workspace with such name already exists", "thisCouldBeYourBusinessNameLessThanBrGreaterThanOrTe": "This could be your business name or team name", "startLoftyPay": "Start LoftyPay", "byContinuingYoureAgreeingToOurIAccept": "By signing up, you agree to the", "termsAndConditions": "Terms & Conditions", "submit": "Submit", "aUserWithThatEmailAddressAlreadyExists": "A user with that email address already exists.", "disconnectedSuccessfully": "Disconnected successfully", "contactsLofty": "Contacts - LoftyWorks", "contacts": "Contacts", "addContact": "Add Contact", "updateSuccessful": "Update successful", "pleaseAddWhatsappDetailsToContact": "Please add Whatsapp details to contact", "documents": "Documents", "chat": "Cha<PERSON>", "joined": "Joined", "emailIsMissing": "<PERSON><PERSON> is missing", "contactName": "Contact Name", "phone": "Phone", "contact": "Contact", "selectYourProperty": "Select your property", "rentPCM": "Rent PCM", "proposedTenancyTerm": "Proposed Tenancy Term", "anIndividual": "An Individual", "aCompany": "A Company", "enter": "Enter", "numberOfTenantsToBeReferenced": "Number of Tenants to be referenced", "numberOfGuarantorsToBeReferenced": "Number of Guarantors to be referenced", "proposedTenancyStartDate": "Proposed Tenancy Start Date", "guarantor": "Guarant<PERSON>", "companyName": "Company Name", "companyEmail": "Company's Email", "prefix": "Prefix", "addGuarantor": "Add Guarantor", "propertyIsRequired": "Property is required", "atLeastOneApplicantRequired": "At least one applicant is required", "atLeastOneGuarantorRequired": "At least one guarantor is required", "applicant": "Applicant", "applicants": "Applicants", "successfullyCreatedApplicant": "Successfully created Applicant", "dashboard": "Dashboard", "newApplicants": "New Applicants", "actions": "Actions", "required": "Required", "noResults": "No Results", "leads": "Applicant", "properties": "Properties", "tenancy": "Tenancy", "typeSomething": "Type something", "note": "Note", "addNote": "Add note...", "files": "Files", "close": "Close", "confirmation": "Confirmation", "update": "Update", "getStartedToday": "Get started today!", "simplifyingClientAccountingandRentProcessingforSmarterManagement": "Simplifying Client Accounting and Rent Processing for Smarter Management.", "ok": "OK", "select": "Select", "operationSuccessful": "Operation successful", "settings": "Settings", "team": "Team", "pageNotFound": "Page not found", "profile": "Profile", "supplier": "Supplier", "show": "Show", "localDateFormat": "DD MMM YYYY", "addNew": "Add New", "no": "No", "yes": "Yes", "min": "Min", "max": "Max", "addVat": "Add VAT", "method": "Method", "vatNumber": "VAT Number", "exclusiveVat": "Exclusive VAT", "inclusiveVat": "Inclusive VAT", "enterVatNumber": "Enter VAT Number", "welcomeToLoftyPay": "Welcome to LoftyPay", "weProvidePropertyManagersWhatNeededToScale": "Simple to use software which helps you scale and excel.", "onboardingAccountingDescriptionText": "Our “one source of truth” to finance keeps your accountant happy.", "details": "Details", "tenancyHistory": "Tenancy History", "applications": "Applications", "instructions": "Instructions", "notes": "Notes", "editProperty": "Edit Property", "bedroom": "Bedroom", "fullBathrooms": "Full Bathrooms", "halfBathrooms": "Half Bathrooms", "squareMt": "Square Meters", "squareFt": "Square Feet", "yearBuilt": "Year Built", "photoAlbum": "Photo Album", "keyFeatures": "Key Features", "utilities": "Utilities", "amenities": "Amenities", "status": "Status", "landlords": "Landlords", "tenant": "Tenant", "propertyStatus": "Property Status", "securityDeposit": "Security Deposit", "selectProperty": "Select property", "rent": "Rent", "rentGuarantee": "Rent Guarantee", "manageType": "Manage Type", "epcOnFile": "EPC on File", "outsideSpace": "Outside Space", "number": "Number", "name": "Name", "share": "Share", "tenancyDate": "Tenancy Date", "payment": "Payment", "tenancyTerm": "Tenancy Term", "paymentDay": "Payment Day", "parking": "Parking", "laundryFacilities": "Laundry facilities", "gym": "Gym", "pool": "Pool", "playground": "Playground", "securitySystem": "Security system", "petFriendly": "Pet-friendly", "smokeFree": "Smoke-free", "wheelChairAccessible": "Wheelchair accessible", "elevator": "Elevator", "rooftopDeck": "Rooftop deck", "privateBalcony": "Private balcony", "fireplace": "Fireplace", "centralAirConditioning": "Central air conditioning", "centralHeating": "Central heating", "dishWasher": "Dishwasher", "microwave": "Microwave", "refrigerator": "Refrigerator", "stove": "<PERSON><PERSON>", "oven": "Oven", "washer": "<PERSON>her", "dryer": "Dryer", "kingSizeBed": "King-size bed", "queenSizeBed": "Queen-size bed", "twinSizebed": "Twin-size bed", "sofaBed": "<PERSON><PERSON> bed", "coffeeMaker": "Coffee maker", "toaster": "Toaster", "iron": "Iron", "ironingBoard": "Ironing board", "hairDryer": "Hair dryer", "vacuumCleaner": "Vacuum cleaner", "airPurifier": "Air purifier", "smokeDetector": "Smoke detector", "carbonMonoxideDetector": "Carbon monoxide detector", "fireExtinguisher": "Fire extinguisher", "firstAidKit": "First-aid kit", "luggageRack": "Luggage rack", "closetSpace": "Closet space", "storageSpace": "Storage space", "view": "View", "quietNeighborhood": "Quiet neighborhood", "waterBill": "Water Bill", "gasBill": "Gas Bill", "electricityBill": "Electricity Bill", "oilBill": "Oil Bill", "satelliteCableBill": "Satellite Cable Bill", "internetBill": "Internet Bill", "allBills": "All Bills", "councilTax": "Council Tax ", "TVLicense": "TV License ", "reports": "Reports", "generateReport": "Generate Report", "trialBalanceReport": "Trial Balance Report", "trialBalanceReportDescription": "Real-time snapshot of all balances in client money accounts, ensuring they are in balance at the moment of generation.", "addTenancy": "Add Tenancy", "allContracts": "All Contracts", "current": "Current", "archived": "Archived", "draft": "Draft"}