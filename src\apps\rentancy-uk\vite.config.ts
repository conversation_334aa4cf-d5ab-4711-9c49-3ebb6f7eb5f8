import { defineConfig, PluginOption } from "vite";
import react from "@vitejs/plugin-react";
import tsConfigPaths from "vite-tsconfig-paths";
import svgr from "vite-plugin-svgr";
import commonjs from "vite-plugin-commonjs";
import nodePolyfills from "rollup-plugin-node-polyfills";
import { injectEnv, viteUrlLoader, removePropTypes } from "../../../vite-plugins";
import eslint from "vite-plugin-eslint";
import ckeditor5 from "@ckeditor/vite-plugin-ckeditor5";
import { getMergedEnvs } from "../../../scripts/init-aws-envs";
import replace from "@rollup/plugin-replace";

const isDevMode = (process.env.NODE_ENV || process.env.MODE) === "development";

const plugins: PluginOption[] = isDevMode ? [commonjs(), nodePolyfills() as PluginOption] : [];

const replaceEnv = {};
const processEnv = getMergedEnvs();

for (const item in processEnv) {
  // @ts-ignore
  replaceEnv[`process.env.${item}`] = JSON.stringify(processEnv[item]);
}

// https://vitejs.dev/config/
const defConfig = defineConfig({
  server: {
    host: "127.0.0.1",
    port: 4000,
    open: true,
    hmr: {
      overlay: false,
    },
  },
  publicDir: "../../../public",
  plugins: [
    // inject window.global to project root index.html
    injectEnv(),
    /**
     * todo:: I haven't find a better plugin to remove propTypes yet
     * there are not many libs use React.Component.propTypes, so handle like this temporarily
     * If remove `react-calendar-mobile` in the feature, please remove this plugin
     */
    replace(replaceEnv),
    removePropTypes({
      include: [/node_modules(.+?)react-calendar-mobile/],
    }),
    viteUrlLoader(),
    ...plugins,
    react(),
    tsConfigPaths(),
    svgr(),
    ckeditor5({ theme: require.resolve("@ckeditor/ckeditor5-theme-lark") }),
    isDevMode &&
    eslint({
      lintOnStart: true,
      failOnError: false,
      include: ["src/**/*.tsx", "src/**/*.ts"],
      rulePaths: ["./"],
    }),
  ].filter(Boolean),
  build: {
    target: "es2015",
    outDir: "../../../build",
    rollupOptions: {
      input: {
        main: "./index.html",
        "lettings-accounting": "./lettings-accounting.html",
      },
      treeshake: true,
      plugins: [
        // nodeResolve()
      ],
      onwarn(warning, warn) {
        if (warning.code === "MODULE_LEVEL_DIRECTIVE" && warning.message.includes('"use client"')) {
          return;
        }
        warn(warning);
      },
    },
    sourcemap: isDevMode, // used for bundle analyzer
    commonjsOptions: {
      sourceMap: isDevMode,
    },
  },
  resolve: {
    alias: {
      // aws-sdk 'request is not exported by __vite-browser-external' bugfix
      // todo:: add this alias is not a good solution, but there is no better solution at present
      // the reference path named './runtimeConfig' cannot exist in src...
      "./runtimeConfig": "./runtimeConfig.browser",
    },
  },
});

// eslint-disable-next-line import/no-default-export
export default defConfig;
