import { FC, useState, useEffect, useMemo, useCallback } from "react";
import { Theme, Grid, CircularProgress, Box, Button } from "@mui/material";
import { useTranslation } from "react-i18next";
import { makeStyles, useTheme } from "@mui/styles";
import { useApi, useGlobal } from "@rentancy/common";
import { useParams } from "react-router-dom";
import { getApplicationDetail } from "Api/Application/index";
import { Add } from "@mui/icons-material";
import { TaskCard } from "./components/TaskCard";
import { CancelModal } from "./components/CancelModal";
import { AssignModal } from "./components/AssignModal";
import { CompleteModal } from "./components/CompleteModal";
import { DeleteModal } from "./components/DeleteModal";
import { DepositModal } from "./components/TaskModal/DepositModal";
import { CreateApplicationModal } from "Containers/Contracts/Application/components/CreateApplicationModal";
import { EmptyPage } from "./EmptyPage";
import { TaskName } from "./types";
import { ReferencingModal } from "./components/TaskModal/ReferencingModal";
import { RightToRentModal } from "./components/TaskModal/RightToRentModal";
import { UtilitiesModal } from "./components/TaskModal/UtilitiesModal";
import { SafetyCertificatesModal } from "./components/TaskModal/SafetyCertificatesModal";
import { InventoryModal } from "./components/TaskModal/InventoryModal";
import { filterApplicationsByStatus, ApplicationItem } from "./utils"; // Assuming ApplicationItem is exported from utils
import { Tabs, TabOption } from "@rentancy/ui";

type ModalType =
  | "cancel"
  | "assign"
  | "complete"
  | "delete"
  | "createApplication"
  | "deposit"
  | "referencing"
  | "rightToRent"
  | "utilities"
  | "safetyCertificates"
  | "inventory"
  | null;

export const WrapperPropertyApplications: FC = () => {
  // State
  const [selectedApplicationId, setSelectedApplicationId] = useState<string>("");
  const [selectedManagerId, setSelectedManagerId] = useState<string[]>([]);
  const [activeModal, setActiveModal] = useState<ModalType>(null);
  const [activeTab, setActiveTab] = useState<string>("inProgress");
  const [taskDetail, setTaskDetail] = useState<any>();

  const theme = useTheme();

  // Hooks
  const { t } = useTranslation();
  const classes = useStyles();
  const { id: propertyId } = useParams<{ id: string }>();
  const { apiCall: getApplicationDetailApi, loading, data, refetch } = useApi(getApplicationDetail);
  const { emitter } = useGlobal();

  // Variables
  const pendings = useMemo(() => {
    return filterApplicationsByStatus(data, "Pending");
  }, [data]);

  const cancelleds = useMemo(() => {
    return filterApplicationsByStatus(data, "Cancelled");
  }, [data]);

  const completedApplications = useMemo(() => {
    // Renamed to avoid conflict with component name
    return filterApplicationsByStatus(data, "Completed");
  }, [data]);

  const tabOptions: TabOption[] = [
    { label: "In Progress", count: pendings?.length ?? 0, value: "inProgress" },
    { label: "Cancelled", count: cancelleds?.length ?? 0, value: "cancelled" },
    { label: "Completed", count: completedApplications?.length ?? 0, value: "completed" },
  ];

  // Functions
  const handleCreateApplication = () => {
    setActiveModal("createApplication");
  };

  const showModalByType = (type: "cancel" | "assign" | "complete" | "delete") => {
    setActiveModal(type);
  };

  const showDetailTask = useCallback(
    (payload: {
      applicationId: string;
      taskId: string;
      type: string;
      category: string;
      [key: string]: any;
    }) => {
      setTaskDetail(payload);
    },
    [],
  );

  const renderApplicationList = (applications: ApplicationItem[] | undefined, type: string) => {
    return applications?.map((item, index) => (
      <>
        <TaskCard
          item={item}
          key={`${type}-${item.id}-${index}`}
          type={type}
          setSelectedApplication={setSelectedApplicationId}
          setSelectedManagerId={setSelectedManagerId}
          showModalByType={showModalByType}
          refetch={refetch}
        />
        <div className={classes.divider} />
      </>
    ));
  };

  // useEffects
  useEffect(() => {
    if (propertyId) {
      getApplicationDetailApi(propertyId);
    }
  }, [propertyId, getApplicationDetailApi]);

  useEffect(() => {
    emitter?.on("openTaskDetails", showDetailTask);
    return () => {
      emitter?.off("openTaskDetails", showDetailTask);
    };
  }, [emitter, showDetailTask]);

  let content;
  if (loading) {
    content = (
      <Box className={classes.loadingContainer}>
        <CircularProgress size={60} />
      </Box>
    );
  } else if (!data?.data || data?.data?.length === 0) {
    content = <EmptyPage handleCreate={handleCreateApplication} />;
  } else {
    let dataToRender = pendings;
    if (activeTab === "completed") {
      dataToRender = completedApplications;
    } else if (activeTab === "cancelled") {
      dataToRender = cancelleds;
    }
    content = (
      <>
        <Grid item container className={classes.tabs}>
          <Grid item container justifyContent="space-between">
            <Tabs tabs={tabOptions} onTabChange={setActiveTab} />
            <Button
              variant="outlined"
              sx={{
                height: theme.spacing(5),
                borderColor: theme.palette.text.secondary2,
                backgroundColor: "transparent",
                "&:hover": {
                  backgroundColor: "transparent",
                  borderColor: theme.palette.text.secondary2,
                },
                color: theme.palette.text.secondary2,
              }}
              startIcon={<Add />}
              data-testid="add-application-btn"
              onClick={handleCreateApplication}
            >
              {t("addApplication")}
            </Button>
          </Grid>

          {renderApplicationList(dataToRender, activeTab || "")}
        </Grid>
      </>
    );
  }

  return (
    <Grid className={classes.container} container padding={2.5}>
      <div className={classes.innerWrapper}>{content}</div>

      {activeModal === "cancel" && (
        <CancelModal
          applicationId={selectedApplicationId}
          onClose={shouldRefresh => {
            setSelectedApplicationId("");
            if (shouldRefresh) {
              refetch();
            }
            setActiveModal(null);
          }}
        />
      )}

      {activeModal === "assign" && (
        <AssignModal
          applicationId={selectedApplicationId}
          selectedManagerId={selectedManagerId}
          onClose={shouldRefresh => {
            setSelectedApplicationId("");
            if (shouldRefresh) {
              refetch();
            }
            setActiveModal(null);
          }}
        />
      )}

      {activeModal === "complete" && (
        <CompleteModal
          applicationId={selectedApplicationId}
          onClose={shouldRefresh => {
            setSelectedApplicationId("");
            if (shouldRefresh) {
              refetch();
            }
            setActiveModal(null);
          }}
        />
      )}

      {activeModal === "delete" && (
        <DeleteModal
          applicationId={selectedApplicationId}
          onClose={shouldRefresh => {
            setSelectedApplicationId("");
            if (shouldRefresh) {
              refetch();
            }
            setActiveModal(null);
          }}
        />
      )}

      {activeModal === "createApplication" && (
        <CreateApplicationModal
          onClose={shouldRefetch => {
            if (shouldRefetch) {
              refetch();
            }
            setActiveModal(null);
          }}
          onSave={() => {
            // Assuming onSave might also trigger a refetch or modal close
            refetch();
            setActiveModal(null);
          }}
        />
      )}

      {(taskDetail?.type === TaskName.ReceivedHoldingDeposit ||
        taskDetail?.type === TaskName.DepositConfirmPayment) && (
        <DepositModal
          {...taskDetail}
          type={taskDetail?.type} // Pass necessary props from taskDetail
          taskId={taskDetail?.taskId}
          applicationId={taskDetail?.applicationId}
          onClose={shouldRefresh => {
            setTaskDetail(null);
            if (shouldRefresh) {
              refetch();
            }
          }}
        />
      )}

      {taskDetail?.type === TaskName.RecordReferencingDecision && (
        <ReferencingModal
          {...taskDetail}
          taskId={taskDetail?.taskId}
          applicationId={taskDetail?.applicationId}
          onClose={shouldRefresh => {
            setTaskDetail(null);
            if (shouldRefresh) {
              refetch();
            }
          }}
        />
      )}

      {taskDetail?.category === TaskName.RightToRent && (
        <RightToRentModal
          {...taskDetail}
          taskId={taskDetail?.taskId}
          applicationId={taskDetail?.applicationId}
          onClose={shouldRefresh => {
            setTaskDetail(null);
            if (shouldRefresh) {
              refetch();
            }
          }}
        />
      )}

      {taskDetail?.type === TaskName.AddUtilitiesInformation && (
        <UtilitiesModal
          {...taskDetail}
          taskId={taskDetail?.taskId}
          applicationId={taskDetail?.applicationId}
          onClose={shouldRefresh => {
            setTaskDetail(null);
            if (shouldRefresh) {
              refetch();
            }
          }}
        />
      )}

      {taskDetail?.type === TaskName.UploadCertificates && (
        <SafetyCertificatesModal
          taskId={taskDetail?.taskId}
          applicationId={taskDetail?.applicationId}
          onClose={shouldRefresh => {
            setTaskDetail(null);
            if (shouldRefresh) {
              refetch();
            }
          }}
        />
      )}

      {taskDetail?.type === TaskName.AddInventoryInformation && (
        <InventoryModal
          {...taskDetail}
          type={taskDetail?.type}
          taskId={taskDetail?.taskId}
          applicationId={taskDetail?.applicationId}
          onClose={shouldRefresh => {
            setTaskDetail(null);
            if (shouldRefresh) {
              refetch();
            }
          }}
        />
      )}
    </Grid>
  );
};

const useStyles = makeStyles((theme: Theme) => ({
  loadingContainer: {
    background: theme.palette.specialColors.pageBackground,
    width: "100%",
    height: "calc(100vh - 250px)", // Consider if this height is still appropriate
    flex: 1,
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center",
    [theme.breakpoints.down("lg")]: {
      // overflow: "scroll", // This might not be needed if the main container scrolls
      // position: "absolute", // This might cause layout issues
    },
  },
  container: {
    backgroundColor: theme.palette.specialColors.grey[100],
    flexDirection: "column",
    boxSizing: "border-box",
    overflow: "auto",
    width: "100%",
    height: "100%",
    minHeight: "100%", // Ensures it takes at least full viewport height
    // minWidth: "1475px", // Consider if this minWidth is necessary or can be made responsive
  },
  tabs: {
    padding: theme.spacing(1.25),
  },
  innerWrapper: {
    background: "white", // Or theme.palette.background.paper for a card-like feel
    // Potentially add padding here if sections need spacing from edges
  },
  title: {
    color: theme.palette.text.secondary2,
    marginLeft: theme.spacing(1), // Add some space between icon and title
  },
  divider: {
    width: "100%",
    height: "1px",
    backgroundColor: "#E5E5E5",
    margin: theme.spacing(2, 0),
  },
}));
