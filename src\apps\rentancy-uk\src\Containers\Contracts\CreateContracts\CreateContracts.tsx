import { ArrowBackIos } from "@mui/icons-material";
import {
  Box,
  Dialog,
  DialogActions,
  DialogContent,
  lighten,
  Grid,
  IconButton,
  Typography,
} from "@mui/material";
import {
  ContactDetailsForSelect,
  ContractPeriod,
  CreateTenancyInput,
  FeeType,
  FinanceBill,
  HtmlHead,
  HtmlHeadTitles,
  SubscriptionType,
  TaxType,
  UserTypes,
  createBills,
  createContractInvoice,
  createTenancy,
  hasSubscription,
  parseFalsyString,
  updateContactType,
  useApi,
  useDeviceScreen,
  useQueryString,
  useUser,
  getPropertyContracts,
} from "@rentancy/common";
import { DesktopProfileBar } from "Containers/UserProfile/DesktopProfileBar";
import { Documents } from "Containers/Contracts/CreateContracts/Documents";
import { FC, useCallback, useMemo, useState } from "react";
import { Layout } from "Components/MultiPageEdit/Layout";
import { OneOffCharges } from "Containers/Contracts/CreateContracts/OneOffCharges";
import { PageEditStep } from "Components/MultiPageEdit/components/PageEditStep";
import { RecurringCharges } from "Containers/Contracts/CreateContracts/RecurringCharges";
import { makeStyles } from "@mui/styles";
import { useLocation, useNavigate } from "react-router-dom";
import { useSnackbar } from "notistack";
import { useTranslation } from "react-i18next";

import { BasicInformation } from "./BasicInformation";
import { ContractState, OneOffCharge } from "./types";
import { Footer } from "./Footer";
import { DialogHeader, Button } from "@rentancy/ui";

export type QueryType = {
  type: string;
  startDate: string;
  endDate: string;
  propertyId: string;
  applicantId: string;
  rent: string;
  deposit: string;
  term: ContractPeriod;
};

export const CreateContracts: FC = () => {
  const classes = useStyles();
  const { t } = useTranslation();
  const { query } = useQueryString<QueryType>();
  const { enqueueSnackbar } = useSnackbar();
  const navigate = useNavigate();
  const { user } = useUser();
  const { isDesktop } = useDeviceScreen();
  const location = useLocation();
  const { applicant } = location.state || {};

  const [isMultipleTenancy, setIsMultipleTenancy] = useState<boolean>(false);

  const hasLoftyPayOrFullSubscription = hasSubscription(user, [
    SubscriptionType.LOFTYPAY,
    SubscriptionType.FULL,
  ]);

  const [tenancyInput, setTenancyInput] = useState<CreateTenancyInput>();

  const [step, setStep] = useState<number>(0);
  // @ts-ignore
  const [contract, setContract] = useState<ContractState>({
    type: "",
    status: "DRAFT",
    currency: user.organisation?.currency ?? "",
    customReference: "",
    propertyId: applicant?.propertyId || query.propertyId,
    endDate: parseFalsyString(query.endDate) || null,
    startDate: parseFalsyString(query.startDate) || null,
    rent: query.rent ? Number(parseFalsyString(query.rent)) : undefined,
    deposit: query.deposit ? Number(parseFalsyString(query.deposit)) : undefined,
    period: (parseFalsyString(query.term) as ContractPeriod) || undefined,
    guarantorsUser: applicant?.guarantorsData || [],
    guarantors: applicant?.guarantors || [],
    tenants: applicant?.applicants,
    tenantsUser: applicant?.applicantsData.map((tenant: ContactDetailsForSelect) => ({
      status: "PENDING",
      tenant,
    })),
  });
  const [isFirstStepReady, setIsFirstStepReady] = useState<boolean>(false);

  const { apiCall: updateContactTypeApi, loading: updateUsersTypeLoading } =
    useApi(updateContactType);
  const { apiCall: createContract, loading } = useApi(createTenancy);
  const { apiCall: createBillsApi, loading: createBillsLoading } = useApi(createBills, {
    disableErrorMessage: true,
  });
  const { apiCall: createInvoiceApi, loading: createInvoiceLoading } = useApi(
    createContractInvoice,
    {
      disableErrorMessage: true,
    },
  );

  const { apiCall: getPropertyContractsApi } = useApi(getPropertyContracts);

  const checkMultipleTenancy = async (propertyId: string) => {
    const data = await getPropertyContractsApi(propertyId);
    return (
      data?.data?.data?.listTenanciesForProperty?.filter(item => item?.status === "ACTIVE")
        ?.length >= 1
    );
  };

  const handleSetIsFirstStepReady = (status: boolean) => {
    setIsFirstStepReady(status);
  };

  const createOneOffCharges = useCallback(
    async ({
      charges,
      contractId,
      propertyId,
      tenant,
      vendorId,
    }: {
      charges: OneOffCharge[];
      propertyId: string;
      contractId: string;
      tenant: ContactDetailsForSelect;
      vendorId: string;
    }) => {
      const chargePromises = charges
        .filter(item => !!item.type)
        .map(item => {
          const bill: FinanceBill = {
            tenancyId: contractId,
            date: item.date,
            dueDate: item.date,
            propertyId: propertyId,
            taxType: TaxType.EXCLUSIVE,
            lineItems: [
              {
                description: item.description,
                unitPrice: Number(item.amount),
                ledgerCode: item.account,
                taxType: item.tax,
                quantity: 1,
              },
            ],
          };

          if (item.type === "TENANT") {
            return createInvoiceApi({
              bill: { ...bill, fromUserId: tenant.id },
              organisationId: user.organisationId,
              prefix: process.env.INTEGRATION_REST_ENDPOINT || "",
            });
          }

          if (item.type === "LANDLORD" && vendorId) {
            return createBillsApi({
              bill: { ...bill, fromUserId: vendorId },
              organisationId: user.organisationId,
              urlPrefix: process.env.INTEGRATION_REST_ENDPOINT || "",
            });
          }

          return null;
        });

      return Promise.all(chargePromises);
    },
    [createBillsApi, createInvoiceApi, user.organisationId],
  );

  const update = useCallback(
    async (data: ContractState, step: number) => {
      let applicantsList: { tenant: ContactDetailsForSelect }[] = [];
      if (data.tenantsUser) {
        // @ts-ignore
        applicantsList = data.tenantsUser.filter(
          elem => elem?.tenant?.type === UserTypes.APPLICANT,
        );
      }

      if (step < 3) {
        setStep(step => step + 1);
      }

      if (step === 3) {
        try {
          // update Applicant to Tenant
          const { propertyId } = data;
          const isMultiple = await checkMultipleTenancy(propertyId);

          for (const { tenant } of Object.values(applicantsList)) {
            const { isError } = await updateContactTypeApi({
              id: tenant.id,
              type: UserTypes.TENANT,
            });
            if (isError) {
              enqueueSnackbar(t("somethingWentWrongPleaseTryAgainLater"), { variant: "error" });
              return;
            }
          }

          const {
            tenantsUser,
            guarantorsUser: _guarantorsUser,
            applicants: _applicants,
            vendorId,
            oneOffCharges,
            permittedOccupiers,
            managementFeeIssuer,
            automaticAllocationOfTenantFundsToRent,
            autoInvoiceInput,
            feeType,
            fixedFee,
            rentCommission,
            ...contractData
          } = data;

          const output: CreateTenancyInput = hasLoftyPayOrFullSubscription
            ? {
                ...contractData,
                autoInvoiceInput,
                permittedOccupiers: permittedOccupiers?.map(item => item?.id),
                automaticAllocationOfTenantFundsToRent,
                automaticRentInvoice: autoInvoiceInput,
                managementFeeEnabled: data.feeType !== FeeType.NONE,
                managementFeeFixed: data.fixedFee,
                managementFeePercentage: data.rentCommission || undefined,
                managementFeeStrategy: data.rentCommission ? "PERCENTAGE" : "FIXED",
                managementFeeTrigger:
                  data.feeType !== FeeType.NONE
                    ? (data.feeType as FeeType.RENT_INVOICE_CREATED)
                    : undefined,
                propertyManagerId: managementFeeIssuer?.[0]?.id,
              }
            : {
                ...contractData,
                autoInvoiceInput,
                permittedOccupiers: permittedOccupiers?.map(item => item?.id),
                feeType,
                fixedFee,
                rentCommission,
              };

          if (isMultiple) {
            setIsMultipleTenancy(isMultiple);
            setTenancyInput(output);
            return;
          }

          const { isSuccess, data: contractResult } = await createContract({ ...output });
          const tenancyId = contractResult?.data.createTenancy.id;

          if (!isSuccess || !tenancyId) {
            throw new Error("create contract error!");
          }

          if (oneOffCharges?.length) {
            await createOneOffCharges({
              charges: oneOffCharges,
              contractId: tenancyId,
              propertyId: data.propertyId,
              tenant: tenantsUser![0],
              vendorId: vendorId || "",
            });
          }
          navigate(`/property/${contractData.propertyId}/contracts/${tenancyId}/details`);
        } catch (error) {
          enqueueSnackbar(t("somethingWentWrongPleaseTryAgainLater"), { variant: "error" });
        }
      }
      setContract(data);
    },
    [
      hasLoftyPayOrFullSubscription,
      createContract,
      navigate,
      user.currentOrganisation,
      enqueueSnackbar,
      t,
      createOneOffCharges,
    ],
  );

  const handleConfirmCreateContract = async () => {
    try {
      const { data: contractResult } = await createContract(tenancyInput);
      setIsMultipleTenancy(false);
      const tenancyId = contractResult?.data.createTenancy.id;
      navigate(`/property/${tenancyInput?.propertyId}/contracts/${tenancyId}/details`);
    } catch (error) {
      enqueueSnackbar(t("somethingWentWrongPleaseTryAgainLater"), { variant: "error" });
    }
  };

  const backward = useCallback(() => {
    setStep(preState => preState - 1);
  }, [setStep]);

  const skipStep = useCallback(() => {
    setStep(3);
  }, [setStep]);

  const titles = useMemo(
    () => [t("basicInformation"), t("recurringCharges"), t("oneOffCharges"), t("documents")],
    [t],
  );

  return (
    <>
      <Layout
        style={{
          height: "100vh",
        }}
        header={
          <>
            <HtmlHead title={HtmlHeadTitles.CONTRACTS} />
            <DesktopProfileBar
              showNotifications
              leftNode={
                <Grid container alignItems="center">
                  <IconButton
                    className={classes.navbarLeftNode}
                    size="large"
                    onClick={() => navigate(-1)}
                  >
                    <ArrowBackIos fontSize="inherit" />
                  </IconButton>
                  <Typography variant="h5" color="text.primary">
                    {t("addTenancy")}
                  </Typography>
                </Grid>
              }
            />
          </>
        }
        main={
          <Box
            boxSizing="border-box"
            flex={1}
            display="flex"
            overflow="auto"
            px={isDesktop ? 2.5 : 2}
            py={2.5}
            sx={{ background: "#F6F7FB" }}
          >
            <Grid
              container
              spacing={2.5}
              flex={1}
              sx={{ flexDirection: { xs: "column", md: "row" } }}
              wrap="nowrap"
            >
              <Grid item xs="auto">
                <PageEditStep
                  activeIndex={step}
                  titles={titles}
                  submitStatus={titles.map((_, idx) => idx <= step)}
                  onChangeActiveIndex={() => {}}
                />
              </Grid>
              <Grid item xs>
                {step === 0 && (
                  <BasicInformation
                    applicant={applicant}
                    contract={contract}
                    update={update}
                    handleSetIsFirstStepReady={handleSetIsFirstStepReady}
                  />
                )}
                {<RecurringCharges visible={step === 1} contract={contract} update={update} />}
                {
                  <OneOffCharges
                    visible={step === 2}
                    skipStep={skipStep}
                    contract={contract}
                    update={update}
                  />
                }
                {<Documents visible={step === 3} contract={contract} update={update} />}
              </Grid>
            </Grid>
          </Box>
        }
        footer={
          <Footer
            step={step}
            backward={backward}
            loading={
              loading || updateUsersTypeLoading || createBillsLoading || createInvoiceLoading
            }
            isFirstStepReady={isFirstStepReady}
          />
        }
      />
      {isMultipleTenancy && (
        <Dialog fullScreen={false} open={true} onClose={() => setIsMultipleTenancy(false)}>
          <DialogHeader onClose={() => setIsMultipleTenancy(false)}>
            {t("existingActiveTenancyFound")}
          </DialogHeader>
          <DialogContent className={classes.root}>
            <Typography variant="h6" color="text.secondary2">
              {t("existingActiveTenancyFoundTips")}
            </Typography>
          </DialogContent>
          <DialogActions className={classes.buttonsWrapper}>
            <Button onClick={() => setIsMultipleTenancy(false)} color="primary" fullWidth>
              {t("cancel")}
            </Button>
            <Button
              form="addContactFormId"
              type="submit"
              variant="contained"
              fullWidth
              loading={
                loading || updateUsersTypeLoading || createBillsLoading || createInvoiceLoading
              }
              onClick={handleConfirmCreateContract}
              data-testid="contact-form-submit-btn"
            >
              {t("confirm")}
            </Button>
          </DialogActions>
        </Dialog>
      )}
    </>
  );
};

const useStyles = makeStyles(({ typography, palette, shape, spacing, breakpoints }) => ({
  navbarLeftNode: {
    border: `1px solid ${palette.specialColors.grey[200]}`,
    borderRadius: shape.borderRadius,
    fontSize: typography.fontSize,
    marginLeft: spacing(2.5),
    marginRight: spacing(2.5),
    "& svg": {
      position: "relative",
      left: 3,
      color: palette.text.primary,
    },
  },
  root: {
    width: "100vw",
    "&::-webkit-scrollbar": {
      width: 8,
    },
    "&::-webkit-scrollbar-track": {
      boxShadow: "transparent",
    },
    "&::-webkit-scrollbar-thumb": {
      backgroundColor: lighten(palette.specialColors.grey[800], 0.7),
      borderRadius: 50,
      border: "3px solid white",
    },
    [breakpoints.up("lg")]: {
      width: 500,
    },
  },
  buttonsWrapper: {
    borderTop: `1px solid ${palette.specialColors.grey[200]}`,
    backgroundColor: palette.background.paper,
    display: "flex",
    alignItems: "center",
    paddingInline: spacing(3.75),
    justifyContent: "flex-end",
    columnGap: spacing(1.875),
    "& > button": {
      height: "40px",
      width: "100px",
      margin: "0 !important",
      boxShadow: "none",
      "&:first-child": {
        color: palette.text.secondary3,
      },
    },
  },
}));
