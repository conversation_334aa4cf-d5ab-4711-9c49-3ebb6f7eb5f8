import { Box, Grid } from "@mui/material";
import { DEFAULT_DEBOUNCE_TIME, useQueryString } from "@rentancy/common";
import { makeStyles } from "@mui/styles";
import { useCallback } from "react";
import { useTranslation } from "react-i18next";
import { SearchTextField } from "@rentancy/ui";

interface Props {
  loading: boolean;
}

export const ApplicationFilters = ({ loading }: Props) => {
  const { query, setQuery } = useQueryString<{
    q: string;
    type?: string;
  }>();

  const { t } = useTranslation();
  const classes = useStyles();

  const handleChange = useCallback(
    (name: string, value?: string | number) => {
      setQuery({ [name]: value, page: 1 });
    },
    [setQuery],
  );

  return (
    <Box>
      <Grid container spacing={1.25} className={classes.filterRow}>
        <Grid item>
          <SearchTextField
            debounceTime={DEFAULT_DEBOUNCE_TIME}
            fullWidth
            type="text"
            variant="outlined"
            value={query?.q || ""}
            placeholder={t("searchApplication")}
            onChange={e => {
              if (loading) {
                return;
              }
              handleChange("q", e.target.value);
            }}
            inputProps={{
              "data-testid": "application-search-input",
            }}
          />
        </Grid>
      </Grid>
    </Box>
  );
};

const useStyles = makeStyles({
  subTitle: {
    background: "#FFF",
    pointerEvents: "none",
  },
  filterRow: {
    justifyContent: "space-between",
    "& > div": {
      width: "80px",
      "&:first-child": {
        width: "300px",
      },
    },
  },
});
