import * as Yup from "yup";
import {
  ContactDetailsForSelect,
  ContractSettings,
  FeeType,
  Tenancy,
  TenancySettingsResponse,
  UpdateTenancyInput,
  User,
  divideMoneyBy100,
  multipleMoneyBy100,
} from "@rentancy/common";
import { FormikProps, useFormik } from "formik";
import { ManagementFeeConfigDetail } from "@rentancy/common/src/rest/Tenancy/types";
import { useTranslation } from "react-i18next";

type UseEditContractFormProps = {
  initialContract: TenancyFormikValues;
};
type FormikTenant = User & {
  tenant: User;
};
export type TenancyFormikValues = Partial<Omit<Tenancy, "tenants">> & {
  tenants: FormikTenant[];
  tenancySettings: TenancySettingsResponse;
};
export type TenancyFormik = {
  formik: FormikProps<TenancyFormikValues>;
  initialContract?: TenancyFormikValues;
  showReminder?: boolean;
};

const getNumber = (value: any) => {
  if (!value || isNaN(Number(value))) {
    return 0;
  }
  return Number(value) / 100;
};
export const getInitialValues = (
  contract: Tenancy,
  tenancySettings: TenancySettingsResponse,
  managerFeeConfig?: ManagementFeeConfigDetail,
  contacts?: User[],
  hasLoftyPayOrFullSubscription?: boolean,
) => {
  const tenants = (contract?.tenants || []).map((tenant: User) => ({
    ...tenant,
    tenant,
    status: tenant.rightToRent,
  }));

  const settings = contract?.settings || {};

  return {
    // Details - Details
    id: contract?.id,
    status: contract?.status,
    startDate: contract?.startDate,
    endDate: contract?.endDate,
    title: contract?.title,
    reference: contract?.reference,
    type: contract?.type,
    // Details - Break Clauses
    breakClauseItems: contract?.breakClauseItems || [],
    // Details - Summary
    area: getNumber(contract?.area),
    openingBalance: getNumber(contract?.openingBalance),
    // Financial
    currency: contract?.currency,
    rent: getNumber(contract?.rent),
    period: contract?.period,
    paymentDay: contract?.paymentDay,
    reminderDays: contract?.reminderDays,
    rentReviewDate: contract?.rentReviewDate,
    reviewNotice: contract?.reviewNotice,
    renewalDate: contract?.renewalDate,
    reservation: getNumber(contract.reservation),
    dontCollectRent: contract?.dontCollectRent,
    landlordVat: contract?.landlordVat,
    invoiceStartDate: contract?.invoiceStartDate,
    isRentGuaranteeRequired: contract.isRentGuaranteeRequired,
    // Settings
    settings: {
      id: settings.id,
      eRV: getNumber(settings.eRV),
      contractValue: getNumber(settings.contractValue),
      feeType: hasLoftyPayOrFullSubscription ? contract.managementFeeTrigger : settings.feeType,
      fixedFee: hasLoftyPayOrFullSubscription
        ? divideMoneyBy100(contract?.managementFeeFixed)
        : getNumber(settings.fixedFee),
      rentCommission: hasLoftyPayOrFullSubscription
        ? contract?.managementFeePercentage
        : settings.rentCommission,
      feeLedgerCode: settings.feeLedgerCode,
      autoInvoiceRent: settings.autoInvoiceRent,
      sendInvoiceToTenant: settings.sendInvoiceToTenant,
      invoiceRentInAdvanceDays: settings.invoiceRentInAdvanceDays,
      managementFeeIssuer:
        hasLoftyPayOrFullSubscription && contract.propertyManagerId
          ? ([{ id: contract.propertyManagerId }] as ContactDetailsForSelect[])
          : [],
    },
    // Deposit
    deposit: getNumber(contract.deposit),
    depositReference: contract?.depositReference,
    dateDepositRegistered: contract?.dateDepositRegistered,
    depositProtectionScheme: contract?.depositProtectionScheme,
    certificateNumber: contract?.certificateNumber,
    transferredDate: contract?.transferredDate,
    refundedDate: contract?.refundedDate,
    depositTransferred: contract?.depositTransferred,
    depositStatus: contract?.depositStatus,
    // Members
    primaryTenant: contract?.primaryTenant,
    tenants,
    guarantors: contract?.guarantors || [],
    permittedOccupiers: contract?.permittedOccupiers || [],
    tenancySettings: {
      automaticAllocationOfTenantFundsToRent: contract.automaticAllocationOfTenantFundsToRent,
      automaticRentInvoice: contract.automaticRentInvoice,
    },
  } as TenancyFormikValues;
};

export const transformSubmitTenancy = (
  values: TenancyFormikValues,
  hasLoftyPayOrFullSubscription?: boolean,
): UpdateTenancyInput => {
  const tenants = values?.tenants?.map(item => item.tenant?.id).filter(Boolean);
  const guarantors = values?.guarantors?.map(item => item.id);
  const permittedOccupiers = values?.permittedOccupiers?.map(item => item.id);

  return {
    // Detail
    id: values.id!,
    type: values.type!,
    title: values.title,
    status: values.status,
    startDate: values.startDate,
    endDate: values.endDate,
    reference: values.reference,
    // @ts-ignore
    breakClauseItems: values.breakClauseItems,
    area: values.area ? multipleMoneyBy100(values.area) : undefined,
    openingBalance: values.openingBalance ? multipleMoneyBy100(values.openingBalance) : undefined,
    //Financial
    rent: values.rent ? multipleMoneyBy100(values.rent) : undefined,
    isRentGuaranteeRequired: values.isRentGuaranteeRequired,
    period: values.period || undefined,
    paymentDay: values.paymentDay || undefined,
    renewalDate: values.renewalDate,
    reminderDays: values.reminderDays,
    rentReview: values.rentReview,
    rentReviewDate: values.rentReviewDate,
    breakClauseDate: values.breakClauseDate,
    reviewNotice: values.reviewNotice,
    reservation: values.reservation ? multipleMoneyBy100(values.reservation) : undefined,
    dontCollectRent: values.dontCollectRent,
    landlordVat: values.landlordVat,
    invoiceStartDate: values.invoiceStartDate,
    //Deposit
    deposit: values.deposit ? multipleMoneyBy100(values.deposit) : undefined,
    depositReference: values.depositReference,
    dateDepositRegistered: values.dateDepositRegistered,
    depositProtectionScheme: values?.depositProtectionScheme,
    certificateNumber: values.certificateNumber,
    transferredDate: values.transferredDate,
    refundedDate: values.refundedDate,
    depositTransferred: values.depositTransferred,
    depositStatus: values.depositStatus,
    //Members
    tenants,
    guarantors,
    permittedOccupiers,
    ...(hasLoftyPayOrFullSubscription
      ? {
          automaticRentInvoice: values.tenancySettings?.automaticRentInvoice,
          automaticAllocationOfTenantFundsToRent:
            values.tenancySettings.automaticAllocationOfTenantFundsToRent,
          paymentDay: values.tenancySettings.paymentDay,
          propertyManagerId: values.settings?.managementFeeIssuer?.[0]?.id,
          managementFeeEnabled: values.settings?.feeType !== FeeType.NONE,
          managementFeeTrigger:
            values.settings?.feeType !== FeeType.NONE ? values.settings?.feeType : undefined,
          managementFeeStrategy: values.settings?.managementFeeIssuer?.[0]?.id
            ? values.settings?.rentCommission
              ? "PERCENTAGE"
              : "FIXED"
            : undefined,
          managementFeePercentage: values.settings?.rentCommission || 0,
          managementFeeFixed: values.settings?.fixedFee
            ? multipleMoneyBy100(values.settings?.fixedFee)
            : undefined,
        }
      : {}),
  };
};

export const transformSubmitSettings = (
  values: ContractSettings,
  hasLoftyPayOrFullSubscription?: boolean,
) => {
  const output: Partial<ContractSettings> = {
    id: values.id,
    eRV: multipleMoneyBy100(values.eRV!),
    contractValue: multipleMoneyBy100(values.contractValue),
    feeLedgerCode: values.feeLedgerCode,
    autoInvoiceRent: values.autoInvoiceRent,
    sendInvoiceToTenant: values.sendInvoiceToTenant,
    invoiceRentInAdvanceDays: values.invoiceRentInAdvanceDays,
  };

  if (hasLoftyPayOrFullSubscription) {
    return output;
  }

  return {
    ...output,
    feeType: values.feeType,
    fixedFee: multipleMoneyBy100(values.fixedFee),
    rentCommission: +parseFloat(values.rentCommission.toString()) || 0,
  };
};

export const useEditContractForm = ({ initialContract }: UseEditContractFormProps) => {
  const { t } = useTranslation();

  const formik = useFormik<TenancyFormikValues>({
    validateOnBlur: true,
    onSubmit: () => {},
    validationSchema: Yup.object().shape({
      startDate: Yup.date().required(),
      endDate: Yup.date()
        .nullable()
        .when(["startDate"], startDate => {
          const isValid = Date.parse(startDate);
          if (!isNaN(isValid)) {
            return Yup.date().nullable().min(startDate, t("endDateMustBeAfterStartDate"));
          }
        }),
      tenants: Yup.array().of(Yup.object().shape({})).min(1, t("required")),
      breakClauseItems: Yup.array().of(
        Yup.object().shape({
          date: Yup.date().required("Date is required"),
        }),
      ),
      isRentGuaranteeRequired: Yup.boolean().nullable(),
    }),
    // @ts-ignore
    initialValues: {
      ...initialContract,
      tenants: [...initialContract.tenants],
      guarantors: [...initialContract.guarantors!],
    },
  });

  return formik;
};
