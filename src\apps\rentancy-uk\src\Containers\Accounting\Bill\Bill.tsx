import { Box, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";

export const Bill = () => {
  const { t } = useTranslation("loftyPay");

  return (
    <Box p={3}>
      <Typography variant="h5" gutterBottom>
        {t("bills")}
      </Typography>
      <Typography variant="body1" color="textSecondary">
        Bill management functionality will be implemented here.
      </Typography>
    </Box>
  );
};
