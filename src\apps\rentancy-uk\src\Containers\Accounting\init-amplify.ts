import { Amplify } from "aws-amplify";

Amplify.configure({
  Auth: {
    region: process.env.COGNITO_REGION,
    userPoolId: process.env.COGNITO_USER_POOL_ID,
    userPoolWebClientId: process.env.COGNITO_APP_CLIENT_ID,
    oauth: {
      domain: process.env.COGNITO_DOMAIN,
      scope: ["email", "profile", "openid", "aws.cognito.signin.user.admin"],
      redirectSignIn: process.env.COGNITO_REDIRECT_SIGN_IN,
      redirectSignOut: process.env.COGNITO_REDIRECT_SIGN_OUT,
      responseType: "code",
    },
  },
  API: {
    endpoints: [
      {
        name: "rentancy",
        endpoint: process.env.RENTANCY_BE_ENDPOINT,
        region: process.env.COGNITO_REGION,
        custom_header: async () => {
          return { "Content-Type": "application/json" };
        },
      },
    ],
  },
  Storage: {
    AWSS3: {
      bucket: process.env.S3_BUCKET,
      region: process.env.S3_REGION,
    },
  },
});
