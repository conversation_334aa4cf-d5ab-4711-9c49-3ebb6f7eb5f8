export enum TenancyStatus {
  DRAFT = "DRAFT",
  APPLICATION = "APPLICATION",
  REFERENCING = "REFERENCING",
  RTR_CHECK = "RTR_CHECK",
  CONTRACT = "CONTRACT",
  ON_NOTICE = "ON_NOTICE",
  VACANT = "VACANT",
  TRANSFER_MONEY = "TRANSFER_MONEY",
  CHECK_IN = "CHECK_IN",
  ACTIVE = "ACTIVE",
  NOTICE_GIVEN = "NOTICE_GIVEN",
  RENEWED = "RENEWED",
  PERIODIC = "PERIODIC",
  VACATING = "VACATING",
  MTM = "MTM",
  VACATED = "VACATED",
  CANCELLED = "CANCELLED",
  ARCHIVED = "ARCHIVED",
  HEAD_OF_TERMS = "HEAD_OF_TERMS",
  UNDER_OFFER = "UNDER_OFFER",
  OFFER_ACCEPTED = "OFFER_ACCEPTED",
  CONVEYANCING = "CONVEYANCING",
  EXCHANGE = "EXCHANGE",
  SOLD_STC = "SOLD_STC",
  HOLDING_OVER = "HOLDING_OVER",
  LET_EXTERNALLY = "LET_EXTERNALLY",
  UPCOMING = "UPCOMING",
  RENEWING = "RENEWING",
  EXPIRING = "EXPIRING",
  EXPIRED = "EXPIRED",
}

export type TenancyActionType = {
  type: TenancyStatus | null;
  from: TenancyStatus;
  label: string;
  title?: string;
  body?: string;
};

export const TenancyActionConfig: Record<
  string,
  {
    name: string;
    actions?: Array<TenancyActionType>;
  }
> = {
  DRAFT: {
    name: "Draft",
    actions: [
      {
        type: TenancyStatus.CANCELLED,
        from: TenancyStatus.DRAFT,
        label: "Cancel Tenancy",
        title: "Cancel Tenancy",
        body: "Would you like to cancel this tenancy? This can’t be undone.",
      },
      {
        type: TenancyStatus.LET_EXTERNALLY,
        from: TenancyStatus.DRAFT,
        label: "Mark as Let Externally",
        title: "Mark as Let Externally",
        body: "You are updating its status to Let Externally, want to go ahead?",
      },
      {
        type: TenancyStatus.ACTIVE,
        from: TenancyStatus.DRAFT,
        label: "Active",
        title: "Activate Tenancy",
        body: "You're about to mark this tenancy as Active.<br />This confirms that the agreement has been signed and the tenancy is ready to begin. Want to go ahead?",
      },
      {
        type: TenancyStatus.APPLICATION,
        from: TenancyStatus.DRAFT,
        label: "Proceed to Application",
        title: "Application",
        body: "You're moving this tenancy to Application.<br />please make sure that the application is created or in the process. Want to go ahead?",
      },
    ],
  },
  APPLICATION: {
    name: "Application",
    actions: [
      {
        type: null,
        from: TenancyStatus.APPLICATION,
        label: "Check Application",
        title: "Add Application",
      },
      {
        type: TenancyStatus.ACTIVE,
        from: TenancyStatus.APPLICATION,
        label: "Active",
        title: "Activate Tenancy",
        body: "You're about to mark this tenancy as Active.<br />This confirms that the agreement has been signed and the tenancy is ready to begin. Want to go ahead?",
      },
    ],
  },
  ACTIVE: {
    name: "Active",
    actions: [
      {
        type: TenancyStatus.VACATING,
        from: TenancyStatus.ACTIVE,
        label: "Terminate Tenancy",
        title: "Terminate Tenancy",
        body: "Terminating this tenancy will close the agreement. This marks the beginning of the move-out process. Just a heads-up — this can’t be undone. <br /> Please check the end date before continuing:",
      },
      {
        type: TenancyStatus.PERIODIC,
        from: TenancyStatus.ACTIVE,
        label: "Switch to Periodic Tenancy",
        title: "Switch to Periodic Tenancy",
        body: "Are you sure you want to switch this tenancy to periodic?",
      },
      {
        type: TenancyStatus.RENEWING,
        from: TenancyStatus.ACTIVE,
        label: "Renew Tenancy",
        title: "Renew Tenancy",
        body: "Looks like you're trying to renew your tenancy. Just to check:<br />Have you already signed a new agreement elsewhere, or would you like to go through the renewal process here?",
      },
    ],
  },
  RENEWING: {
    name: "Renewing",
    actions: [
      {
        type: TenancyStatus.RENEWED,
        from: TenancyStatus.RENEWING,
        label: "Renewed",
        title: "Confirm Tenancy Renewal",
        body: "You're about to mark this tenancy as Renewed. If you haven’t created a new tenancy record, the existing tenancy will be extended.<br />Please make sure all details are up to date before confirming. This action is final and cannot be undone.",
      },
    ],
  },
  RENEWED: {
    name: "Renewed",
    actions: [
      {
        type: TenancyStatus.ARCHIVED,
        from: TenancyStatus.RENEWED,
        label: "Archived",
        title: "Archive Tenancy",
        body: "Want to go ahead and archive it? This can’t be undone.",
      },
    ],
  },
  PERIODIC: {
    name: "Periodic",
    actions: [
      {
        type: TenancyStatus.VACATING,
        from: TenancyStatus.PERIODIC,
        label: "End Tenancy",
        title: "End Tenancy",
        body: "Ending this tenancy will close the agreement. This marks the beginning of the move-out process. If everything’s wrapped up, you can go ahead.<br /> Just a heads-up — this can’t be undone.",
      },
      {
        type: TenancyStatus.RENEWING,
        from: TenancyStatus.PERIODIC,
        label: "Renew Tenancy",
        title: "Renew Tenancy",
        body: "Looks like you're trying to renew your tenancy. Just to check: <br />Have you already signed a new agreement elsewhere, or would you like to go through the renewal process here?",
      },
    ],
  },
  EXPIRING: {
    name: "Expiring",
    actions: [
      {
        type: TenancyStatus.VACATING,
        from: TenancyStatus.EXPIRING,
        label: "End Tenancy",
        title: "End Tenancy",
        body: "Ending this tenancy will close the agreement. This marks the beginning of the move-out process. If everything’s wrapped up, you can go ahead.<br /> Just a heads-up — this can’t be undone.",
      },
      {
        type: TenancyStatus.RENEWING,
        from: TenancyStatus.EXPIRING,
        label: "Renew Tenancy",
        title: "Renew Tenancy",
        body: "Looks like you're trying to renew your tenancy. Just to check: <br />Have you already signed a new agreement elsewhere, or would you like to go through the renewal process here?",
      },
    ],
  },
  VACATING: {
    name: "Vacating",
    actions: [
      {
        type: null,
        from: TenancyStatus.VACATING,
        label: "Progress to move out",
      },
      {
        type: TenancyStatus.VACATED,
        from: TenancyStatus.VACATING,
        label: "Vacated",
        title: "Confirm Vacated Status",
        body: "You're about to mark this tenancy as vacated.<br />This means the tenant has moved out and the tenancy has officially ended. Please note this can’t be undone.",
      },
    ],
  },
  VACATED: {
    name: "Vacated",
    actions: [
      {
        type: TenancyStatus.ARCHIVED,
        from: TenancyStatus.VACATED,
        label: "Archive",
        title: "Archive Tenancy",
        body: "Want to go ahead and archive it? This can’t be undone.",
      },
    ],
  },
  CANCELLED: {
    name: "Cancelled",
    actions: [
      {
        type: TenancyStatus.ARCHIVED,
        from: TenancyStatus.CANCELLED,
        label: "Archive",
        title: "Archive Tenancy",
        body: "Want to go ahead and archive it? This can’t be undone.",
      },
    ],
  },
  EXPIRED: {
    name: "Expired",
    actions: [
      {
        type: TenancyStatus.RENEWING,
        from: TenancyStatus.EXPIRED,
        label: "Renew Tenancy",
        title: "Renew Tenancy",
        body: "Looks like you're trying to renew your tenancy. Just to check:<br /> Have you already signed a new agreement elsewhere, or would you like to go through the renewal process here?",
      },
      {
        type: TenancyStatus.ARCHIVED,
        from: TenancyStatus.VACATED,
        label: "Archive",
        title: "Archive Tenancy",
        body: "Want to go ahead and archive it? This can’t be undone.",
      },
    ],
  },
  LET_EXTERNALLY: {
    name: "Let Externally",
    actions: [
      {
        type: TenancyStatus.DRAFT,
        from: TenancyStatus.LET_EXTERNALLY,
        label: "Revert to Draft",
        title: "Revert to Draft",
        body: "Reverting it to Draft will allow you to restart the internal tenancy setup process. Want to go ahead?",
      },
    ],
  },
};
