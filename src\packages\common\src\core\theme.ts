import { createTheme } from "@mui/material/styles";
import { alpha, colors } from "@mui/material";

export const rentancyColors = {
  primary: {
    main: "#5D51E2",
    light: "#EFEEFD",
  },
  secondary: {
    main: "#1BCA96",
  },
  text: {
    primary: "#202437",
    secondary: "#a0a3af",
    secondary2: "#515666",
    secondary3: "#797e8b",
  },
  error: { main: "#FF285F" },
  specialColors: {
    themeBackground: "#e9e9ff",
    pageBackground: "#f8f9fc",
    themeBorder: "#EBECF1",
    background: {
      main: "#f4f6f8",
    },
    purple: "#7145DF",
    orange: "#FFAB00",
    red2: "#E9137F",
    green: "#31CFA0",
    lightBlue1: "#2492FC",
    lightBlue2: "#4478FC",
    cyan: "#5D51E2",
    greenStage: {
      100: "#D4F6EC",
      200: "#18B687",
    },
    storm: {
      600: "#424a70",
      700: "#394065",
      800: "#2D3452",
      900: "#26283f",
    },
    grey: {
      50: "#FAFBFD",
      100: "#F6F7FB",
      200: "#EBECF1",
      300: "#E1E2E6",
      400: "#C6C8D1",
      500: "#A0A3AF",
      600: "#797E8B",
      700: "#515666",
      800: "#202437",
    },
    primaryA: {
      10: "#E9E9FF",
      100: "#C7C8FE",
      200: "#9FA5FE",
      300: "#7380FF",
      400: "#4B61FE",
      500: "#2F44EF",
      600: "#2A3AE3",
      700: "#1E30D6",
      800: "#1623C8",
      900: "#0D00AF",
    },
    primaryB: {
      10: "#EFE9FC",
      100: "#D7C8F5",
      200: "#BBA5F0",
      300: "#9F7FEA",
      400: "#8761E5",
      500: "#7145DF",
      600: "#6640D8",
      700: "#5636CF",
      800: "#4730C8",
      900: "#2B24BC",
    },
  },
  labelColors: ["#EB2B8C", "#2A3AE3", "#18B687", "#2492FC"],
  completeColor: "#20C472",
  pendingColor: "#FFA600",
  cancelledColor: "#C6C8D1",
  statusLabelColor: "#81858D",
};

export const theme = createTheme({
  palette: {
    specialColors: {
      themeBackground: rentancyColors.specialColors.themeBackground,
      pageBackground: rentancyColors.specialColors.pageBackground,
      themeBorder: rentancyColors.specialColors.themeBorder,
      background: rentancyColors.specialColors.background,
      storm: rentancyColors.specialColors.storm,
      purple: rentancyColors.specialColors.purple,
      orange: rentancyColors.specialColors.orange,
      grey: rentancyColors.specialColors.grey,
      red2: rentancyColors.specialColors.red2,
      green: rentancyColors.specialColors.green,
      lightBlue1: rentancyColors.specialColors.lightBlue1,
      lightBlue2: rentancyColors.specialColors.lightBlue2,
      primaryA: rentancyColors.specialColors.primaryA,
      primaryB: rentancyColors.specialColors.primaryB,
      cyan: rentancyColors.specialColors.cyan,
      greenStage: rentancyColors.specialColors.greenStage,
    },
    primary: {
      main: rentancyColors.primary.main,
      light: rentancyColors.primary.light,
    },
    secondary: {
      main: rentancyColors.secondary.main,
    },
    error: {
      main: rentancyColors.error.main,
    },
    text: {
      primary: rentancyColors.text.primary,
      secondary: rentancyColors.text.secondary,
      secondary2: rentancyColors.text.secondary2,
      secondary3: rentancyColors.text.secondary3,
    },
    action: {
      disabledBackground: colors.grey[200],
    },
  },
  typography: {
    fontFamily: "inherit",
    h1: {
      fontWeight: 700,
      fontSize: 30,
    },
    h2: {
      fontWeight: 700,
      fontSize: 24,
    },
    h3: {
      fontWeight: 700,
      fontSize: 20,
    },
    h4: {
      fontWeight: 600,
      fontSize: 18,
    },
    h5: {
      fontWeight: 700,
      fontSize: 16,
      lineHeight: 1.25,
    },
    h6: {
      fontWeight: 400,
      fontSize: 16,
    },
    subtitle1: {
      fontWeight: 600,
      fontSize: 16,
    },
    subtitle2: {
      fontWeight: 600,
      fontSize: 14,
      lineHeight: 1.43,
    },
    body1: {
      fontWeight: 400,
      fontSize: 14,
      lineHeight: 1.43,
    },
    body2: {
      fontWeight: 500,
      fontSize: 14,
      lineHeight: 1.43,
    },
    caption: {
      fontWeight: 400,
      fontSize: 12,
      lineHeight: 1.16,
    },
    overline: {
      fontWeight: 500,
      fontSize: 10,
      textTransform: "none",
    },
  },
  shape: {
    borderRadius: 6,
  },
  components: {
    MuiOutlinedInput: {
      styleOverrides: {
        root: {
          "& fieldset": {
            borderColor: rentancyColors.specialColors.grey[400],
          },
          "&.Mui-disabled fieldset": {
            borderColor: `${rentancyColors.specialColors.grey[400]} !important`,
          },
        },
      },
    },
    MuiAvatar: {
      styleOverrides: {
        colorDefault: {
          backgroundColor: rentancyColors.specialColors.grey[200],
          color: rentancyColors.specialColors.grey[400],
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        sizeMedium: {
          height: 28,
          fontSize: 14,
        },
        sizeSmall: {
          height: 19,
          fontSize: 12,
        },
        filled: {
          background: `${rentancyColors.specialColors.grey[600]}26`,
          color: rentancyColors.specialColors.grey[600],
        },
      },
    },
    MuiTable: {
      styleOverrides: {
        root: {
          borderColor: rentancyColors.specialColors.grey[200],
        },
      },
    },
    MuiTableContainer: {
      styleOverrides: {
        root: {
          boxSizing: "border-box",
          background: "#FFF",
          border: `solid 1px ${rentancyColors.specialColors.themeBorder}`,
          borderRadius: 6,
          overflow: "auto",
        },
      },
    },
    MuiTableCell: {
      styleOverrides: {
        root: {
          boxSizing: "border-box",
          borderColor: rentancyColors.specialColors.grey[200],
          fontWeight: 400,
          ".MuiIconButton-root": {
            marginRight: 8,
            "&:last-child": {
              marginRight: 0,
            },
            "&:hover": {
              boxShadow: `0 0 0 4px ${alpha(colors.common.black, 0.04)}`,
              backgroundColor: alpha(colors.common.black, 0.04),
            },
          },
        },
        sizeMedium: {
          height: 54,
          padding: "6.5px 20px",
        },
        head: {
          color: rentancyColors.text.primary,
          fontSize: 14,
          fontWeight: 600,
          // just keeping the head nowrap may not have much side effects
          whiteSpace: "nowrap",
          "& .MuiTableSortLabel-root > .table-head-sort-icon": {
            margin: "0 -10px 0 10px",
          },
          "& .MuiTypography-root + .MuiSvgIcon-root": {
            margin: "0 10px",
          },
        },
        body: {
          color: rentancyColors.text.secondary2,
          "& .MuiTypography-root": {
            lineHeight: 1.43,
          },
          "& .MuiTypography-caption": {
            fontSize: 14,
            lineHeight: 1.66,
          },
          ".MuiListItemText-root": {
            margin: 0,
          },
        },
      },
    },
    MuiTabs: {
      styleOverrides: {
        indicator: {
          backgroundColor: rentancyColors.primary.main,
        },
      },
    },
    MuiTableSortLabel: {
      styleOverrides: {
        root: {
          position: "relative",
          left: -8,
          paddingLeft: 8,
          borderRadius: 4,
          "& svg": {
            width: 12,
          },
          "&:hover": {
            background: rentancyColors.specialColors.grey[100],
            paddingLeft: 8,
            "& svg": {
              opacity: 0.6,
            },
          },
        },
      },
    },
    MuiFab: {
      styleOverrides: {
        root: {
          textTransform: "none",
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: "none",
          fontWeight: 400,
        },
        outlinedPrimary: {
          color: rentancyColors.primary.main,
          border: `1px solid ${rentancyColors.primary.main}`,
        },
        containedInfo: {
          backgroundColor: rentancyColors.specialColors.grey[200],
          color: rentancyColors.primary.main,
          boxShadow: "none",
          "&:hover": {
            backgroundColor: rentancyColors.specialColors.grey[300],
            boxShadow: "none",
          },
        },
        contained: {
          "&.MUI-disabled": {
            background: rentancyColors.specialColors.grey[100],
            color: "#fff",
          },
        },
        containedSecondary: {
          color: "#FFF",
          boxShadow: "unset",
          "&:hover": {
            backgroundColor: rentancyColors.secondary.main,
            boxShadow: "unset",
          },
        },
        outlinedInfo: {
          color: rentancyColors.text.secondary2,
          boxShadow: "unset",
          borderColor: rentancyColors.specialColors.themeBorder,
          "&:hover": {
            boxShadow: "unset",
            borderColor: rentancyColors.specialColors.grey[800],
            background: rentancyColors.specialColors.grey[100],
          },
        },
        textInfo: {
          color: rentancyColors.text.secondary3,
          "&:hover": {
            backgroundColor: rentancyColors.specialColors.grey[100],
            boxShadow: "unset",
          },
        },
        sizeSmall: {
          height: 30,
          padding: "5px 8px",
        },
      },
    },
    MuiTab: {
      styleOverrides: {
        root: {
          fontSize: "14px",
          textTransform: "none",
          fontWeight: 500,
          color: rentancyColors.specialColors.grey[600],
          minWidth: "max-content !important",
          paddingInline: "10px",
          padding: "16px",
          "&:hover": {
            color: rentancyColors.primary.main,
          },
          "&$selected": {
            color: rentancyColors.primary.main,
          },
          "&:focus": {
            color: rentancyColors.primary.main,
          },
        },
      },
    },
    MuiFormControlLabel: {
      styleOverrides: {
        label: {
          fontSize: 12,
          color: rentancyColors.text.primary,
        },
      },
    },
    MuiCardActions: {
      styleOverrides: {
        root: {
          justifyContent: "flex-end",
        },
      },
    },
    MuiInputBase: {
      styleOverrides: {
        root: {
          fontSize: 14,
          "& input": {
            fontSize: 14,
            "&::-webkit-input-placeholder": {
              color: rentancyColors.specialColors.grey[400],
              opacity: "1",
            },
            ":-moz-placeholder": {
              color: rentancyColors.specialColors.grey[400],
              opacity: "1",
            },
            "::-moz-placeholder": {
              color: rentancyColors.specialColors.grey[400],
              opacity: "1",
            },
            ":-ms-input-placeholder": {
              color: rentancyColors.specialColors.grey[400],
              opacity: "1",
            },
            "&:-webkit-autofill": {
              transition: "background-color 5000s ease-in-out 0s",
            },
          },
          "& label": {
            fontSize: 12,
          },
          "&.Mui-disabled": {
            background: rentancyColors.specialColors.grey[100],
            color: rentancyColors.specialColors.grey[500],
          },
        },
      },
    },
    MuiFormLabel: {
      styleOverrides: {
        root: {
          fontSize: 12,
        },
        asterisk: {
          color: rentancyColors.error.main,
        },
      },
    },
    MuiDialogTitle: {
      styleOverrides: {
        root: {
          borderBottom: `1px solid ${rentancyColors.specialColors.grey[200]}`,
          fontSize: 18,
          fontWeight: "bold",
        },
      },
    },
    MuiDialogContent: {
      styleOverrides: {
        root: {
          padding: "20px 30px !important",
          boxSizing: "border-box",
          marginTop: "55px",
        },
      },
    },
    MuiDialogActions: {
      styleOverrides: {
        root: {
          borderTop: `1px solid ${rentancyColors.specialColors.grey[200]}`,
          padding: "20px 30px !important",
        },
      },
    },
    MuiIconButton: {
      styleOverrides: {
        root: {
          "&.Mui-disabled": {
            color: rentancyColors.specialColors.grey[400],
          },
          "&:hover": {
            color: rentancyColors.primary.main,
            backgroundColor: alpha(rentancyColors.primary.main, 0.15),
          },
        },
        colorInfo: {
          color: rentancyColors.specialColors.grey[700],
        },
        sizeSmall: {
          width: 30,
          height: 30,
        },
        sizeMedium: {
          width: 36,
          height: 36,
        },
        sizeLarge: {
          width: 40,
          height: 40,
        },
      },
    },
    MuiMenu: {
      styleOverrides: {
        paper: {
          border: `1px solid ${rentancyColors.specialColors.themeBorder}`,
          boxShadow: "0px 2px 5px 0px rgba(0, 0, 0, 0.1)!important",
        },
      },
    },
    MuiMenuItem: {
      styleOverrides: {
        root: {
          color: rentancyColors.text.secondary2,
          "&:hover": {
            backgroundColor: rentancyColors.specialColors.grey[100],
          },
        },
        divider: {
          borderBottomColor: rentancyColors.specialColors.grey[200],
        },
      },
    },
    MuiDivider: {
      styleOverrides: {
        root: {
          borderColor: rentancyColors.specialColors.grey[200],
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          borderColor: rentancyColors.specialColors.themeBorder,
        },
      },
    },
    MuiInput: {
      styleOverrides: {
        underline: {
          paddingBottom: "8px",
          fontWeight: 500,
          "&:after, &:before": {
            borderColor: rentancyColors.specialColors.themeBorder,
          },
          input: {
            fontSize: "16px",
            "&::placeholder": {
              fontWeight: 400,
            },
          },
        },
      },
    },
    MuiSelect: {
      styleOverrides: {
        standard: {
          fontSize: "16px",
          "&:after": {
            backgroundColor: rentancyColors.specialColors.themeBorder,
          },
        },
        icon: {
          color: rentancyColors.text.primary,
        },
      },
    },
    MuiAutocomplete: {
      styleOverrides: {
        endAdornment: {
          "& .MuiButtonBase-root": {
            color: rentancyColors.text.primary,
          },
        },
      },
    },
  },
  customShadows: {
    action: "0px 2px 5px rgba(0, 10, 30, 0.1)",
  },
  customShapes: {
    main: "6px",
    content: "4px",
  },
  customPxToVw: (px: number): string => {
    return `${px / 3.75}vw`;
  },
});
