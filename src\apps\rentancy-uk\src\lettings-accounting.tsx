import "./i18n";
import "@rentancy/common/src/config/sentryConfig";
import { AccountingApp } from "Containers/Accounting/AccountingApp";
import { MuiProvider } from "Containers/App/MuiProvider";
import { createRoot, Root } from "react-dom/client";
import * as Sentry from "@sentry/react";

import "@rentancy/common/src/assets/styles/index.css";
import { NotFound } from "@rentancy/common";

let root: Root | null = null;

// 渲染函数
function render(props: any = {}) {
  const { container } = props;
  const domElement = container ? container.querySelector("#root") : document.getElementById("root");

  if (!root) {
    root = createRoot(domElement!);
  }

  root.render(
    <MuiProvider>
      <Sentry.ErrorBoundary fallback={<NotFound />} showDialog>
        <AccountingApp />
      </Sentry.ErrorBoundary>
    </MuiProvider>,
  );
}

// 独立运行时直接渲染
if (!(window as any).__POWERED_BY_QIANKUN__) {
  render();
}

// qiankun 生命周期函数
export async function bootstrap() {
  console.log("[lettings-accounting] bootstrapped");
}

export async function mount(props: any) {
  console.log("[lettings-accounting] mounted", props);
  render(props);
}

export async function unmount(props: any) {
  console.log("[lettings-accounting] unmounted", props);
  if (root) {
    root.unmount();
    root = null;
  }
}
