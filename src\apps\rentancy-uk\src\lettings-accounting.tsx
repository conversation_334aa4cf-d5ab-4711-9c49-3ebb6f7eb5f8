import "./i18n";
import "@rentancy/common/src/config/sentryConfig";
import { AccountingApp } from "Containers/Accounting/AccountingApp";
import { MuiProvider } from "Containers/App/MuiProvider";
import { createRoot } from "react-dom/client";
import * as Sentry from "@sentry/react";

import "@rentancy/common/src/assets/styles/index.css";
import { NotFound } from "@rentancy/common";

const container = document.getElementById("root");
const root = createRoot(container!);
root.render(
  <MuiProvider>
    <Sentry.ErrorBoundary fallback={<NotFound />} showDialog>
      <AccountingApp />
    </Sentry.ErrorBoundary>
  </MuiProvider>,
);
