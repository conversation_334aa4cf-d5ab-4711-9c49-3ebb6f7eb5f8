import { useState, SyntheticEvent } from "react";
import { Box, Tab, Tabs as MuiTabs, Typography } from "@mui/material";
import { makeStyles } from "@mui/styles";

const useStyles = makeStyles(theme => ({
  tabs: {
    // No borderBottom here
  },
  tab: {
    minHeight: theme.spacing(4.5),
    textTransform: "none",
    minWidth: 0,
    padding: theme.spacing(0.25, 2),
    marginRight: theme.spacing(2),
    color: theme.palette.text.secondary3,
    fontWeight: theme.typography.fontWeightRegular,
    fontSize: theme.typography.pxToRem(15),
    borderRadius: theme.shape.borderRadius,
    "&:hover": {
      color: theme.palette.text.primary,
      backgroundColor: theme.palette.action.hover,
      opacity: 1,
    },
    "&.Mui-selected": {
      backgroundColor: theme.palette.action.hover,
      color: theme.palette.text.primary,
      fontWeight: theme.typography.fontWeightMedium,
      "& .MuiTypography-root": {
        color: theme.palette.text.primary,
      },
    },
    "&:focus": {
      color: theme.palette.text.primary,
    },
  },
  count: {
    marginLeft: theme.spacing(1),
    color: theme.palette.text.secondary,
    fontWeight: theme.typography.fontWeightRegular,
  },
}));

export type TabOption = {
  label: string;
  count: number;
  value: string;
};

type TabsProps = {
  tabs: TabOption[];
  initialTab?: string;
  onTabChange?: (value: string) => void;
};

export const Tabs = ({ tabs, initialTab, onTabChange }: TabsProps) => {
  const classes = useStyles();
  const [selectedTab, setSelectedTab] = useState(
    initialTab || (tabs.length > 0 ? tabs[0].value : ""),
  );

  const handleChange = (event: SyntheticEvent, newValue: string) => {
    setSelectedTab(newValue);
    if (onTabChange) {
      onTabChange(newValue);
    }
  };

  return (
    <MuiTabs
      value={selectedTab}
      onChange={handleChange}
      className={classes.tabs}
      TabIndicatorProps={{ style: { display: "none" } }} // Hide the indicator
      textColor="primary"
    >
      {tabs.map(tab => (
        <Tab
          key={tab.value}
          value={tab.value}
          label={
            <Box sx={{ display: "flex", alignItems: "center" }}>
              <Typography component="span">{tab.label}</Typography>
              <Typography component="span" className={classes.count}>
                {tab.count}
              </Typography>
            </Box>
          }
          className={classes.tab}
        />
      ))}
    </MuiTabs>
  );
};
